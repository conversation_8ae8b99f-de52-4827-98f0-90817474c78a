#Reply Keyboard Markup

This is an example of how to use reply keyboard markup on a ESP8266 based board.

![alt text](https://core.telegram.org/file/811140184/1/5YJxx-rostA/ad3f74094485fb97bd "Reply Keyboard example")

The application will turn on and off an LED based on commands received via telegram.

#Inline Keyboard Markup

This is an example of how to use reply keyboard markup on a ESP8266 based board.


![alt text](https://core.telegram.org/file/811140999/1/2JSoUVlWKa0/4fad2e2743dc8eda04 "Inline Keyboard example")

Right now working only URL redirection button. Other features will be added later.

-----------------

NOTE: You will need to enter your SSID, password and bot Token for the example to work.

Application written by [<PERSON>](https://github.com/witnessmenow)



## License

![License](https://img.shields.io/github/license/witnessmenow/Universal-Arduino-Telegram-Bot)
Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.