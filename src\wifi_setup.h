#ifndef WIFI_SETUP_H
#define WIFI_SETUP_H

#include <WiFi.h>
#include <ESPAsyncWebServer.h>
#include <DNSServer.h>
#include <Preferences.h>
#include "config.h"

class WiFiSetup {
private:
    AsyncWebServer* server;
    DNSServer* dnsServer;
    Preferences preferences;
    bool setupMode;
    bool credentialsValid;
    unsigned long setupStartTime;
    
    // Stored credentials
    String wifiSSID;
    String wifiPassword;
    String botToken;
    String chatID;
    
    // Private methods
    void startAccessPoint();
    void stopAccessPoint();
    void setupWebServer();
    void handleRoot(AsyncWebServerRequest *request);
    void handleSave(AsyncWebServerRequest *request);
    void handleNotFound(AsyncWebServerRequest *request);
    bool connectToWiFi();
    void saveCredentials();
    bool loadCredentials();
    void clearCredentials();

public:
    WiFiSetup();
    ~WiFiSetup();
    
    // Public methods
    bool begin();
    void loop();
    bool isSetupMode() const { return setupMode; }
    bool isConnected() const { return WiFi.status() == WL_CONNECTED; }
    bool hasValidCredentials() const { return credentialsValid; }
    
    // Credential getters
    String getWiFiSSID() const { return wifiSSID; }
    String getWiFiPassword() const { return wifiPassword; }
    String getBotToken() const { return botToken; }
    String getChatID() const { return chatID; }
    
    // Control methods
    void enterSetupMode();
    void exitSetupMode();
    void resetSettings();
    bool reconnectWiFi();
    
    // Status methods
    String getConnectionStatus();
    String getIPAddress();
    int getSignalStrength();
};

#endif // WIFI_SETUP_H
