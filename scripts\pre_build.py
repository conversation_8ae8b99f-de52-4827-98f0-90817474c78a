#!/usr/bin/env python3
"""
ESPgram Pre-Build Script
========================
Automatically updates version information before compilation.

Developer: SK Raihan
Organization: SKR Electronics Lab
Email: <EMAIL>
"""

import os
import sys
import json
import datetime
from pathlib import Path

# Configuration
VERSION_FILE = "version.json"
CONFIG_HEADER = "include/config.h"
VERSION_HEADER = "include/version.h"

def load_version():
    """Load current version from version.json"""
    if os.path.exists(VERSION_FILE):
        with open(VERSION_FILE, 'r') as f:
            return json.load(f)
    else:
        # Default version for first build
        return {
            "major": 1,
            "minor": 0,
            "patch": 0,
            "build": 0,
            "version_string": "1.0.0",
            "last_build": None
        }

def increment_version(version_data, increment_type="build"):
    """Increment version based on type"""
    if increment_type == "major":
        version_data["major"] += 1
        version_data["minor"] = 0
        version_data["patch"] = 0
    elif increment_type == "minor":
        version_data["minor"] += 1
        version_data["patch"] = 0
    elif increment_type == "patch":
        version_data["patch"] += 1
    
    # Always increment build number
    version_data["build"] += 1
    
    # Update version string
    version_data["version_string"] = f"{version_data['major']}.{version_data['minor']}.{version_data['patch']}"
    version_data["last_build"] = datetime.datetime.now().isoformat()
    
    return version_data

def save_version(version_data):
    """Save version data to version.json"""
    with open(VERSION_FILE, 'w') as f:
        json.dump(version_data, f, indent=2)

def update_version_header(version_data):
    """Update version.h header file"""
    build_date = datetime.datetime.now().strftime("%Y-%m-%d")
    build_time = datetime.datetime.now().strftime("%H:%M:%S")
    
    header_content = f"""#ifndef VERSION_H
#define VERSION_H

// ESPgram Version Information
// Auto-generated by pre_build.py - DO NOT EDIT MANUALLY

#define FIRMWARE_VERSION_MAJOR {version_data['major']}
#define FIRMWARE_VERSION_MINOR {version_data['minor']}
#define FIRMWARE_VERSION_PATCH {version_data['patch']}
#define FIRMWARE_VERSION_BUILD {version_data['build']}

#define FIRMWARE_VERSION_STRING "{version_data['version_string']}"
#define FIRMWARE_BUILD_DATE "{build_date}"
#define FIRMWARE_BUILD_TIME "{build_time}"
#define FIRMWARE_BUILD_TIMESTAMP "{build_date} {build_time}"

// Developer Information
#define FIRMWARE_DEVELOPER "SK Raihan"
#define FIRMWARE_ORGANIZATION "SKR Electronics Lab"
#define FIRMWARE_EMAIL "<EMAIL>"
#define FIRMWARE_WEBSITE "skrelectronicslab.com"

// Project Information
#define PROJECT_NAME "ESPgram"
#define PROJECT_DESCRIPTION "Revolutionary IoT Control System"
#define PROJECT_LICENSE "Custom License - SK Raihan"

// Build Information
#define BUILD_ENVIRONMENT "PlatformIO"
#define BUILD_PLATFORM "ESP32"
#define BUILD_FRAMEWORK "Arduino"

#endif // VERSION_H
"""
    
    # Create include directory if it doesn't exist
    os.makedirs("include", exist_ok=True)
    
    with open(VERSION_HEADER, 'w') as f:
        f.write(header_content)

def update_config_header(version_data):
    """Update config.h with version information"""
    if not os.path.exists(CONFIG_HEADER):
        return
    
    # Read current config.h
    with open(CONFIG_HEADER, 'r') as f:
        content = f.read()
    
    # Update version defines if they exist
    lines = content.split('\n')
    updated_lines = []
    
    for line in lines:
        if line.strip().startswith('#define FIRMWARE_VERSION'):
            if 'VERSION_STRING' in line:
                updated_lines.append(f'#define FIRMWARE_VERSION_STRING "{version_data["version_string"]}"')
            elif 'VERSION_MAJOR' in line:
                updated_lines.append(f'#define FIRMWARE_VERSION_MAJOR {version_data["major"]}')
            elif 'VERSION_MINOR' in line:
                updated_lines.append(f'#define FIRMWARE_VERSION_MINOR {version_data["minor"]}')
            elif 'VERSION_PATCH' in line:
                updated_lines.append(f'#define FIRMWARE_VERSION_PATCH {version_data["patch"]}')
            else:
                updated_lines.append(line)
        else:
            updated_lines.append(line)
    
    # Write back to config.h
    with open(CONFIG_HEADER, 'w') as f:
        f.write('\n'.join(updated_lines))

def main():
    """Main pre-build process"""
    print("=" * 50)
    print("ESPgram Pre-Build Script")
    print("=" * 50)
    print(f"Developer: SK Raihan")
    print(f"Organization: SKR Electronics Lab")
    print(f"Timestamp: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Load current version
    version_data = load_version()
    print(f"Current version: {version_data['version_string']} (Build {version_data['build']})")
    
    # Determine increment type from command line args
    increment_type = "build"  # Default
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg in ["major", "minor", "patch", "build"]:
            increment_type = arg
    
    # Increment version
    version_data = increment_version(version_data, increment_type)
    print(f"New version: {version_data['version_string']} (Build {version_data['build']})")
    
    # Save version data
    save_version(version_data)
    print(f"✅ Version saved to {VERSION_FILE}")
    
    # Update header files
    update_version_header(version_data)
    print(f"✅ Version header updated: {VERSION_HEADER}")
    
    update_config_header(version_data)
    print(f"✅ Config header updated: {CONFIG_HEADER}")
    
    print()
    print("🚀 Pre-build process completed successfully!")
    print("=" * 50)

if __name__ == "__main__":
    main()
