#ifndef GPIO_MANAGER_H
#define GPIO_MANAGER_H

#include <Arduino.h>
#include <EEPROM.h>
#include "config.h"

enum PinMode {
    PIN_DISABLED = 0,
    PIN_DIGITAL_OUTPUT = 1,
    PIN_DIGITAL_INPUT = 2,
    PIN_PWM_OUTPUT = 3,
    PIN_ANALOG_INPUT = 4
};

struct GPIOPin {
    uint8_t pin;
    PinMode mode;
    String name;
    int value;
    bool enabled;
    
    GPIOPin() : pin(0), mode(PIN_DISABLED), name(""), value(0), enabled(false) {}
};

class GPIOManager {
private:
    GPIOPin pins[MAX_GPIO_PINS];
    int pinCount;
    
    // Private methods
    bool isValidPin(uint8_t pin);
    bool isPinConfigured(uint8_t pin);
    int findPinIndex(uint8_t pin);
    void saveConfiguration();
    void loadConfiguration();
    String pinModeToString(PinMode mode);
    PinMode stringToPinMode(String modeStr);
    
public:
    GPIOManager();
    
    // Initialization
    void begin();
    
    // Pin configuration
    bool configurePin(uint8_t pin, PinMode mode, String name = "");
    bool removePin(uint8_t pin);
    bool enablePin(uint8_t pin);
    bool disablePin(uint8_t pin);
    
    // Pin control
    bool digitalWrite(uint8_t pin, bool state);
    bool digitalRead(uint8_t pin);
    bool analogWrite(uint8_t pin, int value); // PWM
    int analogRead(uint8_t pin);
    bool togglePin(uint8_t pin);
    
    // Pin information
    String listPins();
    String getPinStatus(uint8_t pin);
    String getPinName(uint8_t pin);
    PinMode getPinMode(uint8_t pin);
    int getPinValue(uint8_t pin);
    bool isPinEnabled(uint8_t pin);
    
    // Bulk operations
    void updateAllPins();
    String readAllInputs();
    void resetAllPins();
    
    // Configuration management
    void clearConfiguration();
    int getConfiguredPinCount();
    
    // Available pins for ESP8266
    String getAvailablePins();
};

#endif // GPIO_MANAGER_H
