#ESP32 - Chat Action

This is a basic example of how to use chat action using UniversalTelegramBot for ESP32 based boards.

Application originally written by [<PERSON><PERSON><PERSON><PERSON>](giancar<PERSON>.<EMAIL>) for [ESP8266-TelegramBot library](https://github.com/Gianbacchio/ESP8266-TelegramBot)

Adapted by [<PERSON>](https://github.com/witnessmenow)

NOTE: You will need to enter your SSID, password and bot Token for the example to work.

## License

![License](https://img.shields.io/github/license/witnessmenow/Universal-Arduino-Telegram-Bot)
Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.