#!/usr/bin/env python3
"""
ESPgram Build and Release Script
Builds the firmware and creates a release package
"""

import os
import sys
import subprocess

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False, e.stderr

def main():
    """Main function"""
    print("ESPgram Build and Release Script")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not os.path.exists('platformio.ini'):
        print("❌ Error: platformio.ini not found. Run this script from the project root.")
        sys.exit(1)
    
    # Step 1: Clean previous build
    success, output = run_command("pio run --target clean", "Cleaning previous build")
    if not success:
        print("⚠️ Warning: Clean failed, continuing anyway...")
    
    # Step 2: Build firmware
    success, output = run_command("pio run", "Building firmware")
    if not success:
        print("❌ Build failed. Please fix errors and try again.")
        sys.exit(1)
    
    # Step 3: Create release
    if os.path.exists('scripts/create_release.py'):
        success, output = run_command("python scripts/create_release.py", "Creating release package")
        if not success:
            print("❌ Release creation failed.")
            sys.exit(1)
    else:
        print("⚠️ Warning: Release script not found, skipping release creation")
    
    print("\n🎉 Build and release completed successfully!")
    print("\n📋 Next steps:")
    print("   1. Upload firmware: pio run --target upload")
    print("   2. Monitor serial: pio device monitor")
    print("   3. Connect to ESPgram-Setup WiFi")
    print("   4. Configure at ***********")

if __name__ == "__main__":
    main()
