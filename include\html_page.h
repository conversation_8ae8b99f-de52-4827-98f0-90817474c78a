#ifndef HTML_PAGE_H
#define HTML_PAGE_H

#include <Arduino.h>

// Embedded HTML page stored in PROGMEM for memory efficiency
const char SETUP_HTML[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESPgram Setup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            font-size: 1.1em;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            background: rgba(255,255,255,0.9);
            color: #333;
            box-sizing: border-box;
        }
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            background: white;
            box-shadow: 0 0 10px rgba(255,255,255,0.5);
        }
        .btn {
            width: 100%;
            padding: 15px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #45a049;
        }
        .info {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            line-height: 1.5;
        }
        .telegram-icon {
            font-size: 2em;
            text-align: center;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="telegram-icon">📱</div>
        <h1>ESPgram Setup</h1>
        
        <div class="info">
            <strong>Welcome to ESPgram!</strong><br>
            Configure your WiFi and Telegram Bot credentials below. 
            After setup, control your ESP8266 entirely through Telegram.
        </div>

        <form action="/save" method="POST">
            <div class="form-group">
                <label for="ssid">WiFi Network Name (SSID):</label>
                <input type="text" id="ssid" name="ssid" required maxlength="32" 
                       placeholder="Enter your WiFi network name">
            </div>

            <div class="form-group">
                <label for="password">WiFi Password:</label>
                <input type="password" id="password" name="password" maxlength="64" 
                       placeholder="Enter your WiFi password">
            </div>

            <div class="form-group">
                <label for="bot_token">Telegram Bot Token:</label>
                <input type="text" id="bot_token" name="bot_token" required maxlength="64" 
                       placeholder="123456789:ABCdefGHIjklMNOpqrsTUVwxyz">
            </div>

            <div class="form-group">
                <label for="chat_id">Telegram Chat ID:</label>
                <input type="text" id="chat_id" name="chat_id" required maxlength="16" 
                       placeholder="123456789">
            </div>

            <div class="info">
                <strong>Need help?</strong><br>
                • Create a bot: Message @BotFather on Telegram<br>
                • Get Chat ID: Message @userinfobot on Telegram
            </div>

            <button type="submit" class="btn">Save Configuration</button>
        </form>
    </div>

    <script>
        // Auto-focus first input
        document.getElementById('ssid').focus();
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const botToken = document.getElementById('bot_token').value;
            const chatId = document.getElementById('chat_id').value;
            
            if (!botToken.includes(':')) {
                alert('Bot token should contain a colon (:)');
                e.preventDefault();
                return;
            }
            
            if (!/^\d+$/.test(chatId)) {
                alert('Chat ID should contain only numbers');
                e.preventDefault();
                return;
            }
        });
    </script>
</body>
</html>
)rawliteral";

// Success page
const char SUCCESS_HTML[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESPgram - Configuration Saved</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            text-align: center;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        h1 { font-size: 2.5em; margin-bottom: 20px; }
        .success-icon { font-size: 4em; margin-bottom: 20px; }
        p { font-size: 1.2em; line-height: 1.5; }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✅</div>
        <h1>Success!</h1>
        <p>Configuration saved successfully!</p>
        <p>ESPgram will now restart and connect to your WiFi network.</p>
        <p>Check your Telegram for the startup message.</p>
    </div>
    <script>
        setTimeout(function() {
            window.close();
        }, 5000);
    </script>
</body>
</html>
)rawliteral";

#endif // HTML_PAGE_H
