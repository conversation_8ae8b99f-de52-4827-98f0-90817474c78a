#!/bin/bash
echo "ESPgram Firmware Flash Script"
echo "==============================="
echo ""
echo "Make sure your ESP8266 is connected and in download mode"
read -p "Press Enter to continue..."
echo ""
echo "Flashing firmware..."
esptool.py --port /dev/ttyUSB0 --baud 921600 write_flash 0x0 ESPgram_v1.0.0.bin
echo ""
if [ $? -eq 0 ]; then
    echo "✅ Firmware flashed successfully!"
    echo "Connect to ESPgram-Setup WiFi to configure"
else
    echo "❌ Flash failed. Check connection and try again."
fi
read -p "Press Enter to exit..."
