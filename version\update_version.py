#!/usr/bin/env python3
"""
ESPgram Version Update Script
Automatically updates version in version.txt and config.h before build
"""

import os
import re
import sys
from datetime import datetime

def read_version():
    """Read current version from version.txt"""
    version_file = os.path.join("version", "version.txt")
    if os.path.exists(version_file):
        with open(version_file, 'r') as f:
            return f.read().strip()
    return "1.0.0"

def update_config_h(version):
    """Update version in config.h"""
    config_file = os.path.join("include", "config.h")
    if not os.path.exists(config_file):
        print(f"Warning: {config_file} not found")
        return
    
    with open(config_file, 'r') as f:
        content = f.read()
    
    # Update FW_VERSION define
    pattern = r'#define\s+FW_VERSION\s+"[^"]*"'
    replacement = f'#define FW_VERSION "{version}"'
    content = re.sub(pattern, replacement, content)
    
    with open(config_file, 'w') as f:
        f.write(content)
    
    print(f"Updated config.h with version {version}")

def increment_version(version, increment_type="patch"):
    """Increment version number"""
    parts = version.split('.')
    if len(parts) != 3:
        return "1.0.0"
    
    major, minor, patch = map(int, parts)
    
    if increment_type == "major":
        major += 1
        minor = 0
        patch = 0
    elif increment_type == "minor":
        minor += 1
        patch = 0
    else:  # patch
        patch += 1
    
    return f"{major}.{minor}.{patch}"

def main():
    """Main function"""
    print("ESPgram Version Update Script")
    print("=" * 40)
    
    current_version = read_version()
    print(f"Current version: {current_version}")
    
    # Check if we should increment version
    # This can be controlled by environment variable or command line arg
    increment = os.environ.get('INCREMENT_VERSION', 'false').lower() == 'true'
    
    if increment:
        new_version = increment_version(current_version)
        print(f"Incrementing to: {new_version}")
        
        # Update version.txt
        version_file = os.path.join("version", "version.txt")
        with open(version_file, 'w') as f:
            f.write(new_version)
        
        current_version = new_version
    
    # Always update config.h with current version
    update_config_h(current_version)
    
    print(f"Build version: {current_version}")
    print(f"Build time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
