#!/usr/bin/env python3
"""
ESPgram Version Update Script
Updates version.txt and config.h with new version information
"""

import os
import re
import sys
from datetime import datetime

def update_version(new_version):
    """Update version in both version.txt and config.h"""
    
    # Get current directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)
    
    # File paths
    version_file = os.path.join(script_dir, "version.txt")
    config_file = os.path.join(project_root, "include", "config.h")
    
    # Validate version format (x.y.z)
    version_pattern = r'^\d+\.\d+\.\d+$'
    if not re.match(version_pattern, new_version):
        print(f"Error: Invalid version format '{new_version}'. Use format x.y.z (e.g., 1.0.0)")
        return False
    
    try:
        # Update version.txt
        print(f"Updating version.txt to {new_version}...")
        with open(version_file, 'w') as f:
            f.write(f"ESPgram v{new_version}\n")
            f.write(f"Build Date: {datetime.now().strftime('%Y-%m-%d')}\n")
            f.write("Features: WiFi Setup, Telegram Bot, GPIO Control, Sensor Monitoring, Automation Engine\n")
        
        # Update config.h
        print(f"Updating config.h to {new_version}...")
        with open(config_file, 'r') as f:
            content = f.read()
        
        # Replace version definition
        updated_content = re.sub(
            r'#define FW_VERSION ".*"',
            f'#define FW_VERSION "{new_version}"',
            content
        )
        
        with open(config_file, 'w') as f:
            f.write(updated_content)
        
        print(f"✅ Version successfully updated to {new_version}")
        print(f"📁 Updated files:")
        print(f"   - {version_file}")
        print(f"   - {config_file}")
        
        return True
        
    except FileNotFoundError as e:
        print(f"Error: File not found - {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def get_current_version():
    """Get current version from config.h"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)
    config_file = os.path.join(project_root, "include", "config.h")
    
    try:
        with open(config_file, 'r') as f:
            content = f.read()
        
        match = re.search(r'#define FW_VERSION "([^"]+)"', content)
        if match:
            return match.group(1)
        else:
            return "Unknown"
    except:
        return "Unknown"

def increment_version(version_type="patch"):
    """Automatically increment version"""
    current = get_current_version()
    if current == "Unknown":
        print("Error: Could not determine current version")
        return False
    
    try:
        major, minor, patch = map(int, current.split('.'))
        
        if version_type == "major":
            major += 1
            minor = 0
            patch = 0
        elif version_type == "minor":
            minor += 1
            patch = 0
        else:  # patch
            patch += 1
        
        new_version = f"{major}.{minor}.{patch}"
        print(f"Incrementing {version_type} version: {current} → {new_version}")
        
        return update_version(new_version)
        
    except ValueError:
        print(f"Error: Invalid current version format '{current}'")
        return False

def main():
    """Main function"""
    print("ESPgram Version Update Script")
    print("=" * 40)
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python update_version.py <version>     # Set specific version (e.g., 1.2.3)")
        print("  python update_version.py patch         # Increment patch version")
        print("  python update_version.py minor         # Increment minor version")
        print("  python update_version.py major         # Increment major version")
        print("  python update_version.py current       # Show current version")
        print()
        print(f"Current version: {get_current_version()}")
        return
    
    arg = sys.argv[1].lower()
    
    if arg == "current":
        print(f"Current version: {get_current_version()}")
    elif arg in ["patch", "minor", "major"]:
        increment_version(arg)
    else:
        # Assume it's a specific version
        update_version(arg)

if __name__ == "__main__":
    main()
