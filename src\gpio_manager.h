#ifndef GPIO_MANAGER_H
#define GPIO_MANAGER_H

#include <Arduino.h>
#include <Preferences.h>
#include "config.h"

struct GPIOPin {
    uint8_t pin;
    PinType type;
    String name;
    int value;
    int pwmChannel;  // For PWM pins
    bool enabled;
    unsigned long lastUpdate;
};

class GPIOManager {
private:
    Preferences preferences;
    GPIOPin pins[MAX_GPIO_PINS];
    int configuredPinCount;
    bool initialized;
    
    // PWM management
    int nextPWMChannel;
    bool pwmChannels[16]; // ESP32 has 16 PWM channels
    
    // Private methods
    bool isValidPin(uint8_t pin);
    int findPinIndex(uint8_t pin);
    int getFreePWMChannel();
    void releasePWMChannel(int channel);
    void saveConfiguration();
    void loadConfiguration();
    void initializePin(int index);
    
    // Command parsing helpers
    String parseSetCommand(const String& args);
    String parseReadCommand(const String& args);
    String parseWriteCommand(const String& args);
    String parsePWMCommand(const String& args);
    String parseListCommand();
    String parseRemoveCommand(const String& args);
    String parseToggleCommand(const String& args);

public:
    GPIOManager();
    ~GPIOManager();
    
    // Initialization
    bool begin();
    void loop();
    
    // Pin configuration
    bool configurePin(uint8_t pin, PinType type, const String& name = "");
    bool removePin(uint8_t pin);
    bool enablePin(uint8_t pin, bool enable = true);
    
    // Pin operations
    bool digitalRead(uint8_t pin, int& value);
    bool digitalWrite(uint8_t pin, int value);
    bool analogRead(uint8_t pin, int& value);
    bool pwmWrite(uint8_t pin, int value);
    bool togglePin(uint8_t pin);
    
    // Information methods
    String getStatusReport();
    String getPinInfo(uint8_t pin);
    int getConfiguredPinCount() const { return configuredPinCount; }
    bool isPinAvailable(uint8_t pin);
    bool isPinConfigured(uint8_t pin);
    
    // Command interface
    String executeCommand(const String& command);
    
    // Bulk operations
    String readAllPins();
    String listConfiguredPins();
    void resetAllPins();
    
    // Pin name management
    bool setPinName(uint8_t pin, const String& name);
    String getPinName(uint8_t pin);
    
    // Advanced features
    bool setPinMode(uint8_t pin, PinType type);
    PinType getPinType(uint8_t pin);
    int getPinValue(uint8_t pin);
    unsigned long getPinLastUpdate(uint8_t pin);
};

#endif // GPIO_MANAGER_H
