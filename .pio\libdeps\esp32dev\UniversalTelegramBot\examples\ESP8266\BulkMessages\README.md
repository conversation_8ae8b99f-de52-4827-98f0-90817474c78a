#ESP8266 - Bulk Messages (Send Message to all users who /start using your Telegram bot)

This is a basic example of how to use UniversalTelegramBot on ESP8266 based boards.

The application will echo bulk messages to subscribed users.

NOTE: You will need to enter your SSID, password and Bot token for the example to work.

Tested on 5 subscribed users. I don't know what will be with 10 000 users, but 10 000 users better work with DB or something :)
Just for little community, for example send some data from your Arduino device to all your family or friends who using your Telegram bot.

## License

![License](https://img.shields.io/github/license/witnessmenow/Universal-Arduino-Telegram-Bot)
Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.