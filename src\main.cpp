/*
 * ESPgram - ESP32 Telegram Bot Control Firmware
 * 
 * A comprehensive firmware for ESP32 that provides Telegram Bot-based control
 * of GPIO pins, sensors, and automation rules. Features include:
 * 
 * - Captive portal WiFi setup
 * - Telegram <PERSON>t integration with inline keyboards
 * - GPIO pin management (Digital I/O, PWM, Analog)
 * - Sensor support (DHT, LDR, PIR, etc.)
 * - Automation engine with rules
 * - Persistent configuration storage
 * - Memory-optimized design
 * 
 * Author: ESPgram Development Team
 * Version: 1.0.0
 * Target: ESP32
 */

#include <Arduino.h>
#include <WiFi.h>
#include <Preferences.h>

// Project modules
#include "config.h"
#include "version.h"
#include "wifi_setup.h"
#include "telegram_bot.h"
#include "gpio_manager.h"
#include "sensor_handler.h"
#include "automation.h"

// Global objects
WiFiSetup wifiSetup;
TelegramBot telegramBot;
GPIOManager gpioManager;
SensorHandler sensorHandler;
AutomationEngine automation;

// System state
bool systemInitialized = false;
unsigned long lastHeartbeat = 0;
unsigned long lastMemoryCheck = 0;
const unsigned long HEARTBEAT_INTERVAL = 30000;  // 30 seconds
const unsigned long MEMORY_CHECK_INTERVAL = 60000; // 1 minute
const uint32_t LOW_MEMORY_THRESHOLD = 50000; // 50KB

// Function declarations
void setup();
void loop();
void initializeSystem();
void checkSystemHealth();
void handleLowMemory();
void printSystemInfo();

void setup() {
    // Initialize serial communication
    Serial.begin(115200);
    delay(1000);
    
    DEBUG_PRINTLN();
    DEBUG_PRINTLN("========================================");
    DEBUG_PRINTF("  %s v%s\n", FW_NAME, FW_VERSION);
    DEBUG_PRINTLN("  ESP32 Telegram Bot Control Firmware");
    DEBUG_PRINTLN("========================================");
    DEBUG_PRINTLN();
    
    // Print system information
    printSystemInfo();
    
    // Initialize system components
    initializeSystem();
    
    DEBUG_PRINTLN("Setup completed successfully!");
    DEBUG_PRINTLN("========================================");
}

void loop() {
    // Handle WiFi setup and connection
    wifiSetup.loop();
    
    // Only run main functionality if WiFi is connected and not in setup mode
    if (wifiSetup.isConnected() && !wifiSetup.isSetupMode()) {
        
        // Initialize system if not already done
        if (!systemInitialized) {
            // Initialize Telegram bot
            if (telegramBot.begin(wifiSetup.getBotToken(), wifiSetup.getChatID())) {
                DEBUG_PRINTLN("Telegram bot initialized successfully");
                
                // Set module references for cross-communication
                telegramBot.setModuleReferences(&gpioManager, &sensorHandler, &automation);
                sensorHandler.setTelegramBot(&telegramBot);
                automation.setModuleReferences(&gpioManager, &sensorHandler, &telegramBot);
                
                // Send startup notification
                telegramBot.sendStartupMessage();
                
                systemInitialized = true;
                DEBUG_PRINTLN("System fully initialized!");
            } else {
                DEBUG_PRINTLN("Failed to initialize Telegram bot, retrying...");
                delay(5000);
                return;
            }
        }
        
        // Run main system loops
        if (systemInitialized) {
            telegramBot.loop();
            gpioManager.loop();
            sensorHandler.loop();
            automation.loop();
            
            // System health monitoring
            checkSystemHealth();
        }
    }
    
    // Small delay to prevent watchdog issues
    delay(10);
}

void initializeSystem() {
    DEBUG_PRINTLN("Initializing system components...");
    
    // Initialize WiFi setup
    DEBUG_PRINT("WiFi Setup: ");
    if (wifiSetup.begin()) {
        DEBUG_PRINTLN("✓ Connected to WiFi");
    } else {
        DEBUG_PRINTLN("⚠ Entered setup mode");
    }
    
    // Initialize GPIO manager
    DEBUG_PRINT("GPIO Manager: ");
    if (gpioManager.begin()) {
        DEBUG_PRINTLN("✓ Initialized");
    } else {
        DEBUG_PRINTLN("✗ Failed");
    }
    
    // Initialize sensor handler
    DEBUG_PRINT("Sensor Handler: ");
    if (sensorHandler.begin()) {
        DEBUG_PRINTLN("✓ Initialized");
    } else {
        DEBUG_PRINTLN("✗ Failed");
    }

    // Initialize automation engine
    DEBUG_PRINT("Automation Engine: ");
    if (automation.begin()) {
        DEBUG_PRINTLN("✓ Initialized");
    } else {
        DEBUG_PRINTLN("✗ Failed");
    }
    
    DEBUG_PRINTLN();
}

void checkSystemHealth() {
    unsigned long currentTime = millis();
    
    // Heartbeat check
    if (currentTime - lastHeartbeat > HEARTBEAT_INTERVAL) {
        lastHeartbeat = currentTime;
        
        // Check WiFi connection
        if (!WiFi.isConnected()) {
            DEBUG_PRINTLN("WiFi connection lost!");
            telegramBot.notifyWiFiDisconnected();
            systemInitialized = false; // Will reinitialize when WiFi reconnects
        }
        
        DEBUG_PRINTF("Heartbeat: Uptime %lu seconds, Free heap: %u bytes\n", 
                     currentTime / 1000, ESP.getFreeHeap());
    }
    
    // Memory check
    if (currentTime - lastMemoryCheck > MEMORY_CHECK_INTERVAL) {
        lastMemoryCheck = currentTime;
        
        uint32_t freeHeap = ESP.getFreeHeap();
        if (freeHeap < LOW_MEMORY_THRESHOLD) {
            DEBUG_PRINTF("Low memory warning: %u bytes free\n", freeHeap);
            handleLowMemory();
        }
    }
}

void handleLowMemory() {
    DEBUG_PRINTLN("Handling low memory condition...");
    
    // Notify via Telegram
    if (systemInitialized) {
        telegramBot.notifyLowMemory();
    }
    
    // Force garbage collection
    ESP.restart(); // In extreme cases, restart to free memory
}

void printSystemInfo() {
    DEBUG_PRINTLN("System Information:");
    DEBUG_PRINTF("• Chip Model: %s\n", ESP.getChipModel());
    DEBUG_PRINTF("• Chip Revision: %d\n", ESP.getChipRevision());
    DEBUG_PRINTF("• CPU Frequency: %d MHz\n", ESP.getCpuFreqMHz());
    DEBUG_PRINTF("• Flash Size: %d bytes\n", ESP.getFlashChipSize());
    DEBUG_PRINTF("• Free Heap: %d bytes\n", ESP.getFreeHeap());
    DEBUG_PRINTF("• Total Heap: %d bytes\n", ESP.getHeapSize());
    
    if (ESP.getPsramSize() > 0) {
        DEBUG_PRINTF("• PSRAM Size: %d bytes\n", ESP.getPsramSize());
        DEBUG_PRINTF("• Free PSRAM: %d bytes\n", ESP.getFreePsram());
    }
    
    DEBUG_PRINTF("• MAC Address: %s\n", WiFi.macAddress().c_str());
    DEBUG_PRINTLN();
}
