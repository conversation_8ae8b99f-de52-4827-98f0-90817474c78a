#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <EEPROM.h>

// Include all modules
#include "config.h"
#include "wifi_setup.h"
#include "telegram_bot.h"
#include "gpio_manager.h"
#include "sensor_handler.h"
#include "automation.h"

// Global objects
WiFiSetup wifiSetup;
TelegramBot telegramBot;
GPIOManager gpioManager;
SensorHandler sensorHandler;
AutomationEngine automationEngine;

// System state
bool systemInitialized = false;
unsigned long lastHeartbeat = 0;
unsigned long bootTime = 0;

void setup() {
    // Initialize serial communication
    Serial.begin(SERIAL_BAUD);
    delay(1000);
    
    Serial.println();
    Serial.println("========================================");
    Serial.println("ESPgram Firmware v" + String(FW_VERSION));
    Serial.println("========================================");
    
    bootTime = millis();
    
    // Initialize EEPROM
    EEPROM.begin(EEPROM_SIZE);
    
    // Initialize WiFi setup
    Serial.println("Initializing WiFi setup...");
    wifiSetup.begin();
    
    // If WiFi is connected, initialize other modules
    if (WiFi.status() == WL_CONNECTED && !wifiSetup.isSetupMode()) {
        initializeSystem();
    }
    
    Serial.println("Setup completed");
    Serial.println("========================================");
}

void loop() {
    // Handle WiFi setup if in setup mode
    if (wifiSetup.isSetupMode()) {
        wifiSetup.handleClient();
        return;
    }
    
    // Check WiFi connection
    if (WiFi.status() != WL_CONNECTED) {
        Serial.println("WiFi disconnected, attempting reconnection...");
        if (!wifiSetup.connectToWiFi()) {
            Serial.println("Failed to reconnect, starting setup mode");
            wifiSetup.startSetupMode();
            return;
        } else {
            // Reinitialize system after WiFi reconnection
            if (!systemInitialized) {
                initializeSystem();
            }
        }
    }
    
    // Run main system loop if initialized
    if (systemInitialized) {
        runSystemLoop();
    }
    
    // System heartbeat
    if (millis() - lastHeartbeat >= 60000) { // Every minute
        printSystemStatus();
        lastHeartbeat = millis();
    }
    
    // Small delay to prevent watchdog issues
    delay(10);
}

void initializeSystem() {
    Serial.println("Initializing system modules...");
    
    // Initialize GPIO Manager
    Serial.println("Starting GPIO Manager...");
    gpioManager.begin();
    
    // Initialize Sensor Handler
    Serial.println("Starting Sensor Handler...");
    sensorHandler.begin();
    
    // Initialize Automation Engine
    Serial.println("Starting Automation Engine...");
    automationEngine.begin();
    automationEngine.setModuleReferences(&gpioManager, &sensorHandler, &telegramBot);
    
    // Initialize Telegram Bot
    Serial.println("Starting Telegram Bot...");
    String botToken = wifiSetup.getBotToken();
    String chatId = wifiSetup.getChatId();
    
    if (telegramBot.begin(botToken, chatId)) {
        telegramBot.setModuleReferences(&gpioManager, &sensorHandler, &automationEngine);
        
        // Send startup message
        delay(2000); // Give some time for connection to stabilize
        telegramBot.sendStartupMessage();
        
        systemInitialized = true;
        Serial.println("System initialized successfully!");
    } else {
        Serial.println("Failed to initialize Telegram bot");
        systemInitialized = false;
    }
}

void runSystemLoop() {
    // Handle Telegram messages
    telegramBot.handleMessages();
    
    // Update sensors
    sensorHandler.loop();
    
    // Check automation rules
    automationEngine.loop();
    
    // Update GPIO inputs
    gpioManager.updateAllPins();
    
    // Check for memory issues
    if (ESP.getFreeHeap() < 1000) {
        Serial.println("WARNING: Low memory detected!");
        telegramBot.sendNotification("⚠️ Low memory warning: " + String(ESP.getFreeHeap()) + " bytes free");
    }
}

void printSystemStatus() {
    Serial.println("========================================");
    Serial.println("System Status Report");
    Serial.println("========================================");
    Serial.println("Uptime: " + String((millis() - bootTime) / 1000) + " seconds");
    Serial.println("Free Heap: " + String(ESP.getFreeHeap()) + " bytes");
    Serial.println("WiFi SSID: " + String(WiFi.SSID()));
    Serial.println("WiFi RSSI: " + String(WiFi.RSSI()) + " dBm");
    Serial.println("IP Address: " + WiFi.localIP().toString());
    Serial.println("GPIO Pins: " + String(gpioManager.getConfiguredPinCount()));
    Serial.println("Sensors: " + String(sensorHandler.getConfiguredSensorCount()));
    Serial.println("Automation Rules: " + String(automationEngine.getRuleCount()));
    Serial.println("Telegram Bot: " + String(telegramBot.isInitialized() ? "Connected" : "Disconnected"));
    Serial.println("========================================");
}

// System reset function (can be called via Telegram)
void resetSystem() {
    Serial.println("Performing system reset...");
    
    // Clear all configurations
    gpioManager.resetAllPins();
    sensorHandler.clearConfiguration();
    automationEngine.clearConfiguration();
    wifiSetup.resetConfiguration();
    
    // Send notification before reset
    if (telegramBot.isInitialized()) {
        telegramBot.sendNotification("🔄 System reset completed. Restarting...");
        delay(2000);
    }
    
    // Restart ESP
    ESP.restart();
}

// Memory optimization functions
void optimizeMemory() {
    // Force garbage collection
    ESP.wdtFeed();
    
    // Print memory usage
    Serial.println("Memory optimization - Free heap: " + String(ESP.getFreeHeap()) + " bytes");
}

// Error handling
void handleSystemError(String error) {
    Serial.println("SYSTEM ERROR: " + error);
    
    if (telegramBot.isInitialized()) {
        telegramBot.sendNotification("❌ System Error: " + error);
    }
    
    // Log error to EEPROM for debugging (optional)
    // Could implement error logging here
}

// Watchdog timer reset
void resetWatchdog() {
    ESP.wdtFeed();
}

// OTA update preparation (for future use)
void prepareForOTA() {
    Serial.println("Preparing for OTA update...");
    
    if (telegramBot.isInitialized()) {
        telegramBot.sendNotification("🔄 Preparing for firmware update...");
    }
    
    // Stop all modules gracefully
    systemInitialized = false;
    
    // Additional OTA preparation code can be added here
}
