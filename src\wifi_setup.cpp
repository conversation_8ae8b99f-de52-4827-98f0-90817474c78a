#include "wifi_setup.h"
#include "html_page.h"

WiFiSetup::WiFiSetup() : server(WEB_SERVER_PORT), setupMode(false), setupStartTime(0) {
    memset(&config, 0, sizeof(config));
}

void WiFiSetup::begin() {
    EEPROM.begin(EEPROM_SIZE);
    
    if (!loadConfig()) {
        Serial.println("No valid configuration found, starting setup mode");
        startSetupMode();
    } else {
        Serial.println("Configuration loaded successfully");
        if (!connectToWiFi()) {
            Serial.println("Failed to connect to WiFi, starting setup mode");
            startSetupMode();
        }
    }
}

void WiFiSetup::startAccessPoint() {
    WiFi.mode(WIFI_AP_STA);
    WiFi.softAPConfig(AP_IP, AP_GATEWAY, AP_SUBNET);
    WiFi.softAP(AP_SSID, AP_PASSWORD);
    
    Serial.print("Access Point started: ");
    Serial.println(AP_SSID);
    Serial.print("IP address: ");
    Serial.println(WiFi.softAPIP());
}

void WiFiSetup::setupWebServer() {
    server.on("/", [this]() { handleRoot(); });
    server.on("/save", HTTP_POST, [this]() { handleSave(); });
    server.onNotFound([this]() { handleNotFound(); });
    
    server.begin();
    Serial.println("Web server started");
}

void WiFiSetup::handleRoot() {
    server.send_P(200, "text/html", SETUP_HTML);
}

void WiFiSetup::handleSave() {
    // Get form data
    String ssid = server.arg("ssid");
    String password = server.arg("password");
    String botToken = server.arg("bot_token");
    String chatId = server.arg("chat_id");
    
    // Validate input
    if (ssid.length() == 0 || botToken.length() == 0 || chatId.length() == 0) {
        server.send(400, "text/plain", "Missing required fields");
        return;
    }
    
    // URL decode and store configuration
    ssid = urlDecode(ssid);
    password = urlDecode(password);
    botToken = urlDecode(botToken);
    chatId = urlDecode(chatId);
    
    strncpy(config.ssid, ssid.c_str(), MAX_SSID_LENGTH - 1);
    strncpy(config.password, password.c_str(), MAX_PASS_LENGTH - 1);
    strncpy(config.botToken, botToken.c_str(), MAX_TOKEN_LENGTH - 1);
    strncpy(config.chatId, chatId.c_str(), MAX_CHAT_ID_LENGTH - 1);
    config.configured = true;
    
    if (saveConfig()) {
        server.send_P(200, "text/html", SUCCESS_HTML);
        Serial.println("Configuration saved successfully");
        
        // Restart after a short delay
        delay(2000);
        ESP.restart();
    } else {
        server.send(500, "text/plain", "Failed to save configuration");
    }
}

void WiFiSetup::handleNotFound() {
    // Redirect to root for captive portal behavior
    server.sendHeader("Location", "/", true);
    server.send(302, "text/plain", "");
}

bool WiFiSetup::saveConfig() {
    EEPROM.put(WIFI_SSID_ADDR, config.ssid);
    EEPROM.put(WIFI_PASS_ADDR, config.password);
    EEPROM.put(BOT_TOKEN_ADDR, config.botToken);
    EEPROM.put(CHAT_ID_ADDR, config.chatId);
    EEPROM.put(CONFIG_FLAG_ADDR, config.configured);
    
    return EEPROM.commit();
}

bool WiFiSetup::loadConfig() {
    EEPROM.get(WIFI_SSID_ADDR, config.ssid);
    EEPROM.get(WIFI_PASS_ADDR, config.password);
    EEPROM.get(BOT_TOKEN_ADDR, config.botToken);
    EEPROM.get(CHAT_ID_ADDR, config.chatId);
    EEPROM.get(CONFIG_FLAG_ADDR, config.configured);
    
    // Ensure null termination
    config.ssid[MAX_SSID_LENGTH - 1] = '\0';
    config.password[MAX_PASS_LENGTH - 1] = '\0';
    config.botToken[MAX_TOKEN_LENGTH - 1] = '\0';
    config.chatId[MAX_CHAT_ID_LENGTH - 1] = '\0';
    
    return config.configured;
}

void WiFiSetup::clearConfig() {
    memset(&config, 0, sizeof(config));
    config.configured = false;
    saveConfig();
}

String WiFiSetup::urlDecode(String str) {
    String decoded = "";
    char temp[] = "0x00";
    unsigned int len = str.length();
    unsigned int i = 0;
    
    while (i < len) {
        char decodedChar;
        char encodedChar = str.charAt(i++);
        
        if ((encodedChar == '%') && (i + 1 < len)) {
            temp[2] = str.charAt(i++);
            temp[3] = str.charAt(i++);
            decodedChar = strtol(temp, NULL, 16);
        } else if (encodedChar == '+') {
            decodedChar = ' ';
        } else {
            decodedChar = encodedChar;
        }
        
        decoded += decodedChar;
    }
    
    return decoded;
}

void WiFiSetup::handleClient() {
    if (setupMode) {
        server.handleClient();
        
        // Check for setup timeout
        if (isSetupTimeout()) {
            Serial.println("Setup timeout reached");
            stopSetupMode();
        }
    }
}

bool WiFiSetup::isConfigured() {
    return config.configured;
}

bool WiFiSetup::connectToWiFi() {
    if (!config.configured) {
        return false;
    }
    
    WiFi.mode(WIFI_STA);
    WiFi.begin(config.ssid, config.password);
    
    Serial.print("Connecting to WiFi: ");
    Serial.println(config.ssid);
    
    unsigned long startTime = millis();
    while (WiFi.status() != WL_CONNECTED && millis() - startTime < WIFI_CONNECT_TIMEOUT) {
        delay(500);
        Serial.print(".");
    }
    
    if (WiFi.status() == WL_CONNECTED) {
        Serial.println();
        Serial.print("Connected! IP address: ");
        Serial.println(WiFi.localIP());
        return true;
    } else {
        Serial.println();
        Serial.println("Failed to connect to WiFi");
        return false;
    }
}

void WiFiSetup::startSetupMode() {
    setupMode = true;
    setupStartTime = millis();
    startAccessPoint();
    setupWebServer();
}

void WiFiSetup::stopSetupMode() {
    setupMode = false;
    server.stop();
    WiFi.softAPdisconnect(true);
}

bool WiFiSetup::isSetupMode() {
    return setupMode;
}

String WiFiSetup::getSSID() {
    return String(config.ssid);
}

String WiFiSetup::getPassword() {
    return String(config.password);
}

String WiFiSetup::getBotToken() {
    return String(config.botToken);
}

String WiFiSetup::getChatId() {
    return String(config.chatId);
}

void WiFiSetup::resetConfiguration() {
    clearConfig();
    Serial.println("Configuration reset");
}

bool WiFiSetup::isSetupTimeout() {
    return setupMode && (millis() - setupStartTime > SETUP_TIMEOUT);
}
