#include "wifi_setup.h"
#include "html_page.h"

WiFiSetup::WiFiSetup() : server(nullptr), dnsServer(nullptr), setupMode(false), 
                         credentialsValid(false), setupStartTime(0) {
}

WiFiSetup::~WiFiSetup() {
    if (server) {
        delete server;
    }
    if (dnsServer) {
        delete dnsServer;
    }
}

bool WiFiSetup::begin() {
    DEBUG_PRINTLN("WiFiSetup: Initializing...");
    
    // Initialize preferences
    preferences.begin(PREFS_NAMESPACE, false);
    
    // Load saved credentials
    credentialsValid = loadCredentials();
    
    if (credentialsValid) {
        DEBUG_PRINTLN("WiFiSetup: Found saved credentials, attempting connection...");
        if (connectToWiFi()) {
            DEBUG_PRINTLN("WiFiSetup: Connected to WiFi successfully");
            return true;
        } else {
            DEBUG_PRINTLN("WiFiSetup: Failed to connect with saved credentials");
        }
    }
    
    // Enter setup mode if no valid credentials or connection failed
    DEBUG_PRINTLN("WiFiSetup: Entering setup mode");
    enterSetupMode();
    return false;
}

void WiFiSetup::loop() {
    if (setupMode) {
        // Handle DNS requests for captive portal
        if (dnsServer) {
            dnsServer->processNextRequest();
        }
        
        // Check for setup timeout
        if (millis() - setupStartTime > CAPTIVE_PORTAL_TIMEOUT) {
            DEBUG_PRINTLN("WiFiSetup: Setup timeout, restarting...");
            ESP.restart();
        }
    } else {
        // Monitor WiFi connection in normal mode
        if (WiFi.status() != WL_CONNECTED) {
            DEBUG_PRINTLN("WiFiSetup: WiFi disconnected, attempting reconnection...");
            if (!reconnectWiFi()) {
                DEBUG_PRINTLN("WiFiSetup: Reconnection failed, entering setup mode");
                enterSetupMode();
            }
        }
    }
}

void WiFiSetup::enterSetupMode() {
    setupMode = true;
    setupStartTime = millis();
    
    // Disconnect from any existing WiFi
    WiFi.disconnect(true);
    delay(1000);
    
    startAccessPoint();
    setupWebServer();
    
    DEBUG_PRINTF("WiFiSetup: Setup mode active. Connect to '%s' and visit http://***********\n", AP_SSID);
}

void WiFiSetup::exitSetupMode() {
    if (!setupMode) return;
    
    setupMode = false;
    stopAccessPoint();
    
    if (server) {
        delete server;
        server = nullptr;
    }
    
    if (dnsServer) {
        delete dnsServer;
        dnsServer = nullptr;
    }
    
    DEBUG_PRINTLN("WiFiSetup: Exited setup mode");
}

void WiFiSetup::startAccessPoint() {
    WiFi.mode(WIFI_AP_STA);
    
    // Configure AP
    WiFi.softAPConfig(AP_IP, AP_GATEWAY, AP_SUBNET);
    WiFi.softAP(AP_SSID, AP_PASSWORD);
    
    // Start DNS server for captive portal
    dnsServer = new DNSServer();
    dnsServer->start(53, "*", AP_IP);
    
    DEBUG_PRINTF("WiFiSetup: Access Point started - SSID: %s, IP: %s\n", 
                 AP_SSID, WiFi.softAPIP().toString().c_str());
}

void WiFiSetup::stopAccessPoint() {
    if (dnsServer) {
        dnsServer->stop();
        delete dnsServer;
        dnsServer = nullptr;
    }
    
    WiFi.softAPdisconnect(true);
    WiFi.mode(WIFI_STA);
    
    DEBUG_PRINTLN("WiFiSetup: Access Point stopped");
}

void WiFiSetup::setupWebServer() {
    if (server) {
        delete server;
    }
    
    server = new AsyncWebServer(WEB_SERVER_PORT);
    
    // Captive portal - redirect all requests to setup page
    server->onNotFound([this](AsyncWebServerRequest *request) {
        handleNotFound(request);
    });
    
    // Setup page
    server->on("/", HTTP_GET, [this](AsyncWebServerRequest *request) {
        handleRoot(request);
    });
    
    // Save configuration
    server->on("/save", HTTP_POST, [this](AsyncWebServerRequest *request) {
        handleSave(request);
    });
    
    // Start server
    server->begin();
    DEBUG_PRINTLN("WiFiSetup: Web server started");
}

void WiFiSetup::handleRoot(AsyncWebServerRequest *request) {
    DEBUG_PRINTLN("WiFiSetup: Serving setup page");
    request->send_P(200, "text/html", SETUP_HTML);
}

void WiFiSetup::handleNotFound(AsyncWebServerRequest *request) {
    // Captive portal - redirect everything to setup page
    if (request->host() != WiFi.softAPIP().toString()) {
        request->redirect("http://" + WiFi.softAPIP().toString());
        return;
    }
    
    // Serve setup page for any unknown route
    handleRoot(request);
}

void WiFiSetup::handleSave(AsyncWebServerRequest *request) {
    DEBUG_PRINTLN("WiFiSetup: Processing save request");
    
    bool hasError = false;
    String errorMsg = "";
    
    // Get form parameters
    if (request->hasParam("wifi_ssid", true)) {
        wifiSSID = request->getParam("wifi_ssid", true)->value();
        wifiSSID.trim();
    } else {
        hasError = true;
        errorMsg = "Missing WiFi SSID";
    }
    
    if (request->hasParam("wifi_password", true)) {
        wifiPassword = request->getParam("wifi_password", true)->value();
    }
    
    if (request->hasParam("bot_token", true)) {
        botToken = request->getParam("bot_token", true)->value();
        botToken.trim();
    } else {
        hasError = true;
        errorMsg = "Missing Bot Token";
    }
    
    if (request->hasParam("chat_id", true)) {
        chatID = request->getParam("chat_id", true)->value();
        chatID.trim();
    } else {
        hasError = true;
        errorMsg = "Missing Chat ID";
    }
    
    // Validate inputs
    if (wifiSSID.length() == 0) {
        hasError = true;
        errorMsg = "WiFi SSID cannot be empty";
    }
    
    if (botToken.length() < 10) {
        hasError = true;
        errorMsg = "Invalid Bot Token";
    }
    
    if (chatID.length() == 0) {
        hasError = true;
        errorMsg = "Chat ID cannot be empty";
    }
    
    if (hasError) {
        DEBUG_PRINTF("WiFiSetup: Validation error: %s\n", errorMsg.c_str());
        request->send(400, "text/plain", "Error: " + errorMsg);
        return;
    }
    
    // Save credentials
    saveCredentials();
    credentialsValid = true;
    
    DEBUG_PRINTLN("WiFiSetup: Credentials saved successfully");
    request->send_P(200, "text/html", SUCCESS_HTML);
    
    // Schedule restart to apply new settings
    delay(2000);
    ESP.restart();
}

bool WiFiSetup::connectToWiFi() {
    if (wifiSSID.length() == 0) {
        DEBUG_PRINTLN("WiFiSetup: No SSID configured");
        return false;
    }

    DEBUG_PRINTF("WiFiSetup: Connecting to WiFi: %s\n", wifiSSID.c_str());

    WiFi.mode(WIFI_STA);
    WiFi.begin(wifiSSID.c_str(), wifiPassword.c_str());

    // Wait for connection with timeout
    unsigned long startTime = millis();
    while (WiFi.status() != WL_CONNECTED && millis() - startTime < 20000) {
        delay(500);
        DEBUG_PRINT(".");
    }
    DEBUG_PRINTLN();

    if (WiFi.status() == WL_CONNECTED) {
        DEBUG_PRINTF("WiFiSetup: Connected! IP: %s\n", WiFi.localIP().toString().c_str());
        return true;
    } else {
        DEBUG_PRINTF("WiFiSetup: Connection failed. Status: %d\n", WiFi.status());
        return false;
    }
}

bool WiFiSetup::reconnectWiFi() {
    if (!credentialsValid) {
        return false;
    }

    WiFi.disconnect();
    delay(1000);

    return connectToWiFi();
}

void WiFiSetup::saveCredentials() {
    DEBUG_PRINTLN("WiFiSetup: Saving credentials to preferences");

    preferences.putString(WIFI_SSID_KEY, wifiSSID);
    preferences.putString(WIFI_PASS_KEY, wifiPassword);
    preferences.putString(BOT_TOKEN_KEY, botToken);
    preferences.putString(CHAT_ID_KEY, chatID);
    preferences.putBool(SETUP_DONE_KEY, true);

    DEBUG_PRINTLN("WiFiSetup: Credentials saved successfully");
}

bool WiFiSetup::loadCredentials() {
    DEBUG_PRINTLN("WiFiSetup: Loading credentials from preferences");

    if (!preferences.getBool(SETUP_DONE_KEY, false)) {
        DEBUG_PRINTLN("WiFiSetup: No setup completed flag found");
        return false;
    }

    wifiSSID = preferences.getString(WIFI_SSID_KEY, "");
    wifiPassword = preferences.getString(WIFI_PASS_KEY, "");
    botToken = preferences.getString(BOT_TOKEN_KEY, "");
    chatID = preferences.getString(CHAT_ID_KEY, "");

    if (wifiSSID.length() == 0 || botToken.length() == 0 || chatID.length() == 0) {
        DEBUG_PRINTLN("WiFiSetup: Incomplete credentials found");
        return false;
    }

    DEBUG_PRINTLN("WiFiSetup: Valid credentials loaded");
    return true;
}

void WiFiSetup::clearCredentials() {
    DEBUG_PRINTLN("WiFiSetup: Clearing all credentials");

    preferences.clear();

    wifiSSID = "";
    wifiPassword = "";
    botToken = "";
    chatID = "";
    credentialsValid = false;

    DEBUG_PRINTLN("WiFiSetup: Credentials cleared");
}

void WiFiSetup::resetSettings() {
    clearCredentials();
    enterSetupMode();
}

String WiFiSetup::getConnectionStatus() {
    switch (WiFi.status()) {
        case WL_CONNECTED:
            return "Connected";
        case WL_NO_SSID_AVAIL:
            return "SSID not found";
        case WL_CONNECT_FAILED:
            return "Connection failed";
        case WL_CONNECTION_LOST:
            return "Connection lost";
        case WL_DISCONNECTED:
            return "Disconnected";
        default:
            return "Unknown";
    }
}

String WiFiSetup::getIPAddress() {
    if (WiFi.status() == WL_CONNECTED) {
        return WiFi.localIP().toString();
    }
    return "0.0.0.0";
}

int WiFiSetup::getSignalStrength() {
    if (WiFi.status() == WL_CONNECTED) {
        return WiFi.RSSI();
    }
    return 0;
}
