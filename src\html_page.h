#ifndef HTML_PAGE_H
#define HTML_PAGE_H

#include <Arduino.h>

// Embedded HTML page for WiFi and Telegram setup
const char SETUP_HTML[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESPgram Setup</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 2.5em;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        h1 {
            color: #333;
            font-size: 1.8em;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #666;
            font-size: 0.9em;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
        }
        
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .help-text {
            font-size: 0.8em;
            color: #666;
            margin-top: 5px;
        }
        
        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
        }
        
        .submit-btn:active {
            transform: translateY(0);
        }
        
        .info-box {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin-bottom: 25px;
            border-radius: 0 8px 8px 0;
        }
        
        .info-box h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .info-box p {
            color: #666;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        .telegram-steps {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 25px;
            border-radius: 0 8px 8px 0;
        }
        
        .telegram-steps ol {
            margin-left: 20px;
            color: #333;
        }
        
        .telegram-steps li {
            margin-bottom: 8px;
            font-size: 0.9em;
        }
        
        .status {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: none;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">📱</div>
            <h1>ESPgram Setup</h1>
            <p class="subtitle">Configure your ESP32 for Telegram control</p>
        </div>
        
        <div class="info-box">
            <h3>Welcome to ESPgram!</h3>
            <p>This setup will connect your ESP32 to WiFi and configure Telegram Bot control. After setup, all interactions will happen through Telegram.</p>
        </div>
        
        <form id="setupForm" action="/save" method="POST">
            <div class="form-group">
                <label for="wifi_ssid">WiFi Network Name (SSID)</label>
                <input type="text" id="wifi_ssid" name="wifi_ssid" required maxlength="32">
                <div class="help-text">Enter your WiFi network name</div>
            </div>
            
            <div class="form-group">
                <label for="wifi_password">WiFi Password</label>
                <input type="password" id="wifi_password" name="wifi_password" maxlength="64">
                <div class="help-text">Leave empty for open networks</div>
            </div>
            
            <div class="telegram-steps">
                <h3>🤖 How to get Telegram Bot credentials:</h3>
                <ol>
                    <li>Open Telegram and search for <strong>@BotFather</strong></li>
                    <li>Send <code>/newbot</code> and follow instructions</li>
                    <li>Copy the Bot Token (looks like: 123456789:ABC-DEF...)</li>
                    <li>Start a chat with your bot and send any message</li>
                    <li>Visit: <code>https://api.telegram.org/bot&lt;TOKEN&gt;/getUpdates</code></li>
                    <li>Find your Chat ID in the response</li>
                </ol>
            </div>
            
            <div class="form-group">
                <label for="bot_token">Telegram Bot Token</label>
                <input type="text" id="bot_token" name="bot_token" required maxlength="50">
                <div class="help-text">Get this from @BotFather on Telegram</div>
            </div>
            
            <div class="form-group">
                <label for="chat_id">Telegram Chat ID</label>
                <input type="text" id="chat_id" name="chat_id" required maxlength="20">
                <div class="help-text">Your personal chat ID with the bot</div>
            </div>
            
            <button type="submit" class="submit-btn">💾 Save Configuration</button>
        </form>
        
        <div id="status" class="status"></div>
    </div>
    
    <script>
        document.getElementById('setupForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const status = document.getElementById('status');
            
            // Show loading
            status.className = 'status';
            status.style.display = 'block';
            status.innerHTML = '⏳ Saving configuration...';
            
            fetch('/save', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                if (data.includes('success')) {
                    status.className = 'status success';
                    status.innerHTML = '✅ Configuration saved! ESP32 will restart and connect to WiFi. Check your Telegram bot for confirmation.';
                    
                    // Disable form
                    const inputs = this.querySelectorAll('input, button');
                    inputs.forEach(input => input.disabled = true);
                    
                } else {
                    status.className = 'status error';
                    status.innerHTML = '❌ Error saving configuration. Please check your inputs and try again.';
                }
            })
            .catch(error => {
                status.className = 'status error';
                status.innerHTML = '❌ Connection error. Please try again.';
            });
        });
    </script>
</body>
</html>
)rawliteral";

// Success page after configuration
const char SUCCESS_HTML[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESPgram - Setup Complete</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            text-align: center; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 50px 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 { color: #4CAF50; margin-bottom: 20px; }
        .icon { font-size: 4em; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">✅</div>
        <h1>Setup Complete!</h1>
        <p>Your ESPgram device has been configured successfully.</p>
        <p>The device will now restart and connect to your WiFi network.</p>
        <p><strong>Check your Telegram bot for a welcome message!</strong></p>
        <br>
        <p><em>You can now disconnect from this WiFi network.</em></p>
    </div>
</body>
</html>
)rawliteral";

#endif // HTML_PAGE_H
