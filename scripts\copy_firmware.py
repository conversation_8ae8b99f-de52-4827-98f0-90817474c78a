#!/usr/bin/env python3
"""
ESPgram Firmware Copy Script
Copies the compiled firmware to release folder after successful build
"""

import os
import shutil
import sys
from datetime import datetime

def main():
    """Main function to copy firmware to release folder"""
    print("ESPgram Firmware Copy Script")
    print("=" * 40)
    
    # Source firmware file
    source_file = os.path.join(".pio", "build", "esp32dev", "firmware.bin")
    
    if not os.path.exists(source_file):
        print(f"Error: Firmware file not found at {source_file}")
        sys.exit(1)
    
    # Create release directory if it doesn't exist
    release_dir = "release"
    if not os.path.exists(release_dir):
        os.makedirs(release_dir)
        print(f"Created release directory: {release_dir}")
    
    # Read version
    version_file = os.path.join("version", "version.txt")
    version = "unknown"
    if os.path.exists(version_file):
        with open(version_file, 'r') as f:
            version = f.read().strip()
    
    # Generate firmware filename with version and timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    firmware_name = f"ESPgram_v{version}_{timestamp}.bin"
    latest_name = "ESPgram_latest.bin"
    
    # Copy firmware files
    dest_versioned = os.path.join(release_dir, firmware_name)
    dest_latest = os.path.join(release_dir, latest_name)
    
    try:
        # Copy versioned firmware
        shutil.copy2(source_file, dest_versioned)
        print(f"Copied firmware to: {dest_versioned}")
        
        # Copy as latest
        shutil.copy2(source_file, dest_latest)
        print(f"Copied firmware to: {dest_latest}")
        
        # Get file size
        file_size = os.path.getsize(source_file)
        print(f"Firmware size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
        
        # Create build info file
        info_file = os.path.join(release_dir, "build_info.txt")
        with open(info_file, 'w') as f:
            f.write(f"ESPgram Firmware Build Information\n")
            f.write(f"=" * 40 + "\n")
            f.write(f"Version: {version}\n")
            f.write(f"Build Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Firmware Size: {file_size:,} bytes ({file_size/1024:.1f} KB)\n")
            f.write(f"Target: ESP32\n")
            f.write(f"Framework: Arduino\n")
            f.write(f"Latest Firmware: {latest_name}\n")
            f.write(f"Versioned Firmware: {firmware_name}\n")
        
        print(f"Created build info: {info_file}")
        print("Firmware copy completed successfully!")
        
    except Exception as e:
        print(f"Error copying firmware: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
