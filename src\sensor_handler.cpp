#include "sensor_handler.h"
#include "telegram_bot.h"

SensorHandler::SensorHandler() : sensorCount(0), initialized(false), telegramBot(nullptr), lastSensorRead(0) {
    // Initialize sensor array
    for (int i = 0; i < MAX_SENSORS; i++) {
        sensors[i].id = 255; // Invalid ID
        sensors[i].type = SENSOR_NONE;
        sensors[i].pin = 255;
        sensors[i].name = "";
        sensors[i].value = 0.0;
        sensors[i].threshold = 0.0;
        sensors[i].enabled = false;
        sensors[i].alertEnabled = false;
        sensors[i].lastRead = 0;
        sensors[i].dhtSensor = nullptr;
    }
}

SensorHandler::~SensorHandler() {
    // Clean up DHT sensors
    for (int i = 0; i < sensorCount; i++) {
        if (sensors[i].dhtSensor) {
            delete sensors[i].dhtSensor;
        }
    }
}

bool SensorHandler::begin() {
    DEBUG_PRINTLN("SensorHandler: Initializing...");
    
    preferences.begin(PREFS_NAMESPACE, false);
    loadConfiguration();
    
    // Initialize all configured sensors
    for (int i = 0; i < sensorCount; i++) {
        if (sensors[i].id != 255) {
            initializeSensor(i);
        }
    }
    
    initialized = true;
    DEBUG_PRINTF("SensorHandler: Initialized with %d sensors\n", sensorCount);
    return true;
}

void SensorHandler::loop() {
    if (!initialized) return;
    
    unsigned long currentTime = millis();
    
    // Read sensors periodically
    if (currentTime - lastSensorRead > SENSOR_READ_INTERVAL) {
        lastSensorRead = currentTime;
        
        for (int i = 0; i < sensorCount; i++) {
            if (sensors[i].enabled && sensors[i].id != 255) {
                readSensor(i);
            }
        }
        
        // Check thresholds for alerts
        checkThresholds();
    }
}

bool SensorHandler::addSensor(SensorType type, uint8_t pin, const String& name) {
    if (!isValidSensorPin(pin, type)) {
        DEBUG_PRINTF("SensorHandler: Invalid pin %d for sensor type %d\n", pin, type);
        return false;
    }
    
    // Check if pin is already used
    if (findSensorByPin(pin) != -1) {
        DEBUG_PRINTF("SensorHandler: Pin %d already in use\n", pin);
        return false;
    }
    
    // Find free slot
    int index = findFreeSensorSlot();
    if (index == -1) {
        DEBUG_PRINTLN("SensorHandler: No free sensor slots");
        return false;
    }
    
    // Configure sensor
    sensors[index].id = index + 1; // IDs start from 1
    sensors[index].type = type;
    sensors[index].pin = pin;
    sensors[index].name = name.length() > 0 ? name : (sensorTypeToString(type) + String(pin));
    sensors[index].value = 0.0;
    sensors[index].threshold = 0.0;
    sensors[index].enabled = true;
    sensors[index].alertEnabled = false;
    sensors[index].lastRead = 0;
    sensors[index].dhtSensor = nullptr;
    
    if (index >= sensorCount) {
        sensorCount = index + 1;
    }
    
    initializeSensor(index);
    saveConfiguration();
    
    DEBUG_PRINTF("SensorHandler: Added sensor %s (ID: %d) on pin %d\n", 
                 sensors[index].name.c_str(), sensors[index].id, pin);
    return true;
}

bool SensorHandler::removeSensor(uint8_t id) {
    int index = findSensorById(id);
    if (index == -1) {
        return false;
    }
    
    // Clean up DHT sensor if exists
    destroyDHTSensor(index);
    
    // Clear configuration
    sensors[index].id = 255;
    sensors[index].type = SENSOR_NONE;
    sensors[index].pin = 255;
    sensors[index].name = "";
    sensors[index].value = 0.0;
    sensors[index].threshold = 0.0;
    sensors[index].enabled = false;
    sensors[index].alertEnabled = false;
    sensors[index].lastRead = 0;
    sensors[index].dhtSensor = nullptr;
    
    // Update sensor count
    int maxId = 0;
    for (int i = 0; i < MAX_SENSORS; i++) {
        if (sensors[i].id != 255 && sensors[i].id > maxId) {
            maxId = sensors[i].id;
        }
    }
    sensorCount = maxId;
    
    saveConfiguration();
    
    DEBUG_PRINTF("SensorHandler: Removed sensor ID %d\n", id);
    return true;
}

bool SensorHandler::enableSensor(uint8_t id, bool enable) {
    int index = findSensorById(id);
    if (index == -1) {
        return false;
    }
    
    sensors[index].enabled = enable;
    saveConfiguration();
    
    DEBUG_PRINTF("SensorHandler: Sensor ID %d %s\n", id, enable ? "enabled" : "disabled");
    return true;
}

bool SensorHandler::setSensorThreshold(uint8_t id, float threshold) {
    int index = findSensorById(id);
    if (index == -1) {
        return false;
    }
    
    sensors[index].threshold = threshold;
    sensors[index].alertEnabled = true; // Enable alerts when threshold is set
    saveConfiguration();
    
    DEBUG_PRINTF("SensorHandler: Sensor ID %d threshold set to %.2f\n", id, threshold);
    return true;
}

bool SensorHandler::enableSensorAlert(uint8_t id, bool enable) {
    int index = findSensorById(id);
    if (index == -1) {
        return false;
    }
    
    sensors[index].alertEnabled = enable;
    saveConfiguration();
    
    DEBUG_PRINTF("SensorHandler: Sensor ID %d alerts %s\n", id, enable ? "enabled" : "disabled");
    return true;
}

bool SensorHandler::readSensorValue(uint8_t id, float& value) {
    int index = findSensorById(id);
    if (index == -1 || !sensors[index].enabled) {
        return false;
    }
    
    readSensor(index);
    value = sensors[index].value;
    return true;
}

String SensorHandler::readAllSensors() {
    String result = "📊 *All Sensor Values*\n\n";
    
    bool hasSensors = false;
    for (int i = 0; i < sensorCount; i++) {
        if (sensors[i].id != 255 && sensors[i].enabled) {
            hasSensors = true;
            readSensor(i);
            
            result += "*" + sensors[i].name + "* (ID: " + String(sensors[i].id) + ")\n";
            result += "• Type: " + sensorTypeToString(sensors[i].type) + "\n";
            result += "• Pin: " + String(sensors[i].pin) + "\n";
            result += "• Value: " + String(sensors[i].value, 2);
            
            // Add units based on sensor type
            switch (sensors[i].type) {
                case SENSOR_DHT11:
                case SENSOR_DHT22:
                    result += "°C / " + String(sensors[i].value, 1) + "%"; // Temp/Humidity
                    break;
                case SENSOR_LDR:
                    result += " (Light Level)";
                    break;
                default:
                    break;
            }
            
            if (sensors[i].alertEnabled) {
                result += "\n• Threshold: " + String(sensors[i].threshold, 2);
            }
            
            result += "\n\n";
        }
    }
    
    if (!hasSensors) {
        result += "No sensors configured or enabled.";
    }
    
    return result;
}

// Private helper methods
int SensorHandler::findFreeSensorSlot() {
    for (int i = 0; i < MAX_SENSORS; i++) {
        if (sensors[i].id == 255) {
            return i;
        }
    }
    return -1;
}

int SensorHandler::findSensorById(uint8_t id) {
    for (int i = 0; i < MAX_SENSORS; i++) {
        if (sensors[i].id == id) {
            return i;
        }
    }
    return -1;
}

int SensorHandler::findSensorByPin(uint8_t pin) {
    for (int i = 0; i < MAX_SENSORS; i++) {
        if (sensors[i].pin == pin && sensors[i].id != 255) {
            return i;
        }
    }
    return -1;
}

bool SensorHandler::isValidSensorPin(uint8_t pin, SensorType type) {
    if (!IS_VALID_GPIO(pin)) {
        return false;
    }

    switch (type) {
        case SENSOR_DHT11:
        case SENSOR_DHT22:
        case SENSOR_PIR:
        case SENSOR_IR:
            return true; // Any GPIO pin
        case SENSOR_LDR:
            return IS_ADC_CAPABLE(pin); // Needs ADC capability
        default:
            return false;
    }
}

void SensorHandler::initializeSensor(int index) {
    if (index < 0 || index >= MAX_SENSORS || sensors[index].id == 255) {
        return;
    }

    uint8_t pin = sensors[index].pin;

    switch (sensors[index].type) {
        case SENSOR_DHT11:
        case SENSOR_DHT22:
            createDHTSensor(index);
            break;

        case SENSOR_LDR:
            pinMode(pin, INPUT);
            break;

        case SENSOR_PIR:
        case SENSOR_IR:
            pinMode(pin, INPUT_PULLUP);
            break;

        default:
            break;
    }

    sensors[index].lastRead = millis();
    DEBUG_PRINTF("SensorHandler: Initialized sensor %s on pin %d\n",
                 sensors[index].name.c_str(), pin);
}

void SensorHandler::readSensor(int index) {
    if (index < 0 || index >= MAX_SENSORS || sensors[index].id == 255 || !sensors[index].enabled) {
        return;
    }

    float newValue = 0.0;
    uint8_t pin = sensors[index].pin;

    switch (sensors[index].type) {
        case SENSOR_DHT11:
        case SENSOR_DHT22:
            if (sensors[index].dhtSensor) {
                float temp = sensors[index].dhtSensor->readTemperature();
                float humidity = sensors[index].dhtSensor->readHumidity();

                if (!isnan(temp) && !isnan(humidity)) {
                    // Store temperature as primary value
                    newValue = temp;
                    // Note: In a more advanced implementation, you might want to store both values
                }
            }
            break;

        case SENSOR_LDR:
            newValue = analogRead(pin);
            // Convert to percentage (0-100%)
            newValue = (newValue / 4095.0) * 100.0;
            break;

        case SENSOR_PIR:
        case SENSOR_IR:
            newValue = digitalRead(pin) ? 1.0 : 0.0;
            break;

        default:
            return;
    }

    sensors[index].value = newValue;
    sensors[index].lastRead = millis();
}

void SensorHandler::checkThresholds() {
    if (!telegramBot) return;

    for (int i = 0; i < sensorCount; i++) {
        if (sensors[i].id != 255 && sensors[i].enabled && sensors[i].alertEnabled) {
            float currentValue = sensors[i].value;
            float threshold = sensors[i].threshold;

            // Simple threshold check (can be enhanced with hysteresis)
            if (currentValue > threshold) {
                String alertMsg = "🚨 *Sensor Alert*\n\n";
                alertMsg += "Sensor: " + sensors[i].name + "\n";
                alertMsg += "Current: " + String(currentValue, 2) + "\n";
                alertMsg += "Threshold: " + String(threshold, 2) + "\n";
                alertMsg += "Pin: " + String(sensors[i].pin);

                telegramBot->sendMessage(alertMsg);

                // Disable alert temporarily to avoid spam (re-enable after some time)
                sensors[i].alertEnabled = false;
            }
        }
    }
}

// DHT sensor management
bool SensorHandler::createDHTSensor(int index) {
    if (sensors[index].dhtSensor) {
        delete sensors[index].dhtSensor;
    }

    uint8_t dhtType = (sensors[index].type == SENSOR_DHT11) ? DHT11 : DHT22;
    sensors[index].dhtSensor = new DHT(sensors[index].pin, dhtType);
    sensors[index].dhtSensor->begin();

    return true;
}

void SensorHandler::destroyDHTSensor(int index) {
    if (sensors[index].dhtSensor) {
        delete sensors[index].dhtSensor;
        sensors[index].dhtSensor = nullptr;
    }
}

// Configuration management
void SensorHandler::saveConfiguration() {
    if (!initialized) return;

    // Save sensor configurations
    for (int i = 0; i < sensorCount; i++) {
        if (sensors[i].id != 255) {
            String key = "sensor_" + String(i);
            String config = String(sensors[i].id) + "," +
                           String(sensors[i].type) + "," +
                           String(sensors[i].pin) + "," +
                           sensors[i].name + "," +
                           String(sensors[i].threshold, 2) + "," +
                           String(sensors[i].enabled ? 1 : 0) + "," +
                           String(sensors[i].alertEnabled ? 1 : 0);
            preferences.putString(key.c_str(), config);
        }
    }

    preferences.putInt("sensor_count", sensorCount);
}

void SensorHandler::loadConfiguration() {
    sensorCount = preferences.getInt("sensor_count", 0);

    for (int i = 0; i < sensorCount && i < MAX_SENSORS; i++) {
        String key = "sensor_" + String(i);
        String config = preferences.getString(key.c_str(), "");

        if (config.length() > 0) {
            // Parse configuration string
            int commaIndex1 = config.indexOf(',');
            int commaIndex2 = config.indexOf(',', commaIndex1 + 1);
            int commaIndex3 = config.indexOf(',', commaIndex2 + 1);
            int commaIndex4 = config.indexOf(',', commaIndex3 + 1);
            int commaIndex5 = config.indexOf(',', commaIndex4 + 1);
            int commaIndex6 = config.indexOf(',', commaIndex5 + 1);

            if (commaIndex1 > 0 && commaIndex2 > 0 && commaIndex3 > 0 &&
                commaIndex4 > 0 && commaIndex5 > 0 && commaIndex6 > 0) {

                sensors[i].id = config.substring(0, commaIndex1).toInt();
                sensors[i].type = (SensorType)config.substring(commaIndex1 + 1, commaIndex2).toInt();
                sensors[i].pin = config.substring(commaIndex2 + 1, commaIndex3).toInt();
                sensors[i].name = config.substring(commaIndex3 + 1, commaIndex4);
                sensors[i].threshold = config.substring(commaIndex4 + 1, commaIndex5).toFloat();
                sensors[i].enabled = config.substring(commaIndex5 + 1, commaIndex6).toInt() == 1;
                sensors[i].alertEnabled = config.substring(commaIndex6 + 1).toInt() == 1;
                sensors[i].value = 0.0;
                sensors[i].lastRead = 0;
                sensors[i].dhtSensor = nullptr;
            }
        }
    }
}

// Sensor type helpers
SensorType SensorHandler::parseSensorType(const String& typeStr) {
    String type = typeStr;
    type.toLowerCase();

    if (type == "dht11") return SENSOR_DHT11;
    if (type == "dht22") return SENSOR_DHT22;
    if (type == "ldr") return SENSOR_LDR;
    if (type == "pir") return SENSOR_PIR;
    if (type == "ir") return SENSOR_IR;

    return SENSOR_NONE;
}

String SensorHandler::sensorTypeToString(SensorType type) {
    switch (type) {
        case SENSOR_DHT11: return "DHT11";
        case SENSOR_DHT22: return "DHT22";
        case SENSOR_LDR: return "LDR";
        case SENSOR_PIR: return "PIR";
        case SENSOR_IR: return "IR";
        default: return "Unknown";
    }
}

// Status and information methods
String SensorHandler::getStatusReport() {
    String report = "";

    if (sensorCount == 0) {
        report = "No sensors configured.\n\n";
        report += "Use `/sensor add <type> <pin> [name]` to add sensors.\n";
        report += "Available types: dht11, dht22, ldr, pir, ir";
        return report;
    }

    for (int i = 0; i < sensorCount; i++) {
        if (sensors[i].id != 255) {
            report += "*" + sensors[i].name + "* (ID: " + String(sensors[i].id) + ")\n";
            report += "• Type: " + sensorTypeToString(sensors[i].type) + "\n";
            report += "• Pin: " + String(sensors[i].pin) + "\n";
            report += "• Value: " + String(sensors[i].value, 2);

            // Add units
            switch (sensors[i].type) {
                case SENSOR_DHT11:
                case SENSOR_DHT22:
                    report += "°C";
                    break;
                case SENSOR_LDR:
                    report += "%";
                    break;
                case SENSOR_PIR:
                case SENSOR_IR:
                    report += (sensors[i].value > 0 ? " (Detected)" : " (Clear)");
                    break;
                default:
                    break;
            }

            report += "\n• Status: " + String(sensors[i].enabled ? "Enabled" : "Disabled");

            if (sensors[i].alertEnabled) {
                report += "\n• Alert: " + String(sensors[i].threshold, 2);
            }

            report += "\n\n";
        }
    }

    return report;
}

// Command interface
String SensorHandler::executeCommand(const String& command) {
    String cmd = command;
    cmd.toLowerCase();
    cmd.trim();

    if (cmd.startsWith("add ")) {
        return parseAddCommand(cmd.substring(4));
    }
    else if (cmd.startsWith("read ")) {
        return parseReadCommand(cmd.substring(5));
    }
    else if (cmd.startsWith("remove ")) {
        return parseRemoveCommand(cmd.substring(7));
    }
    else if (cmd.startsWith("threshold ")) {
        return parseThresholdCommand(cmd.substring(10));
    }
    else if (cmd == "list") {
        return parseListCommand();
    }
    else if (cmd == "readall") {
        return parseReadAllCommand();
    }
    else {
        return "❓ Unknown sensor command. Available: add, read, remove, threshold, list, readall";
    }
}

String SensorHandler::parseAddCommand(const String& args) {
    // Format: add <type> <pin> [name]
    int spaceIndex1 = args.indexOf(' ');
    if (spaceIndex1 == -1) {
        return "❌ Usage: add <type> <pin> [name]\nTypes: dht11, dht22, ldr, pir, ir";
    }

    String typeStr = args.substring(0, spaceIndex1);
    String remaining = args.substring(spaceIndex1 + 1);

    int spaceIndex2 = remaining.indexOf(' ');
    String pinStr = (spaceIndex2 == -1) ? remaining : remaining.substring(0, spaceIndex2);
    String name = (spaceIndex2 == -1) ? "" : remaining.substring(spaceIndex2 + 1);

    SensorType type = parseSensorType(typeStr);
    if (type == SENSOR_NONE) {
        return "❌ Invalid sensor type. Use: dht11, dht22, ldr, pir, ir";
    }

    int pin = pinStr.toInt();
    if (!isValidSensorPin(pin, type)) {
        return "❌ Invalid pin " + String(pin) + " for sensor type " + typeStr;
    }

    if (addSensor(type, pin, name)) {
        return "✅ Sensor " + typeStr + " added on pin " + String(pin) +
               (name.length() > 0 ? " (" + name + ")" : "");
    } else {
        return "❌ Failed to add sensor (pin may be in use or no free slots)";
    }
}

String SensorHandler::parseReadCommand(const String& args) {
    int id = args.toInt();
    int index = findSensorById(id);

    if (index == -1) {
        return "❌ Sensor ID " + String(id) + " not found";
    }

    if (!sensors[index].enabled) {
        return "❌ Sensor ID " + String(id) + " is disabled";
    }

    readSensor(index);

    String result = "📖 " + sensors[index].name + " (ID: " + String(id) + "): ";
    result += String(sensors[index].value, 2);

    // Add units
    switch (sensors[index].type) {
        case SENSOR_DHT11:
        case SENSOR_DHT22:
            result += "°C";
            break;
        case SENSOR_LDR:
            result += "% light";
            break;
        case SENSOR_PIR:
        case SENSOR_IR:
            result += (sensors[index].value > 0 ? " (Detected)" : " (Clear)");
            break;
        default:
            break;
    }

    return result;
}

String SensorHandler::parseRemoveCommand(const String& args) {
    int id = args.toInt();

    if (removeSensor(id)) {
        return "🗑️ Sensor ID " + String(id) + " removed";
    } else {
        return "❌ Sensor ID " + String(id) + " not found";
    }
}

String SensorHandler::parseThresholdCommand(const String& args) {
    int spaceIndex = args.indexOf(' ');
    if (spaceIndex == -1) {
        return "❌ Usage: threshold <id> <value>";
    }

    int id = args.substring(0, spaceIndex).toInt();
    float threshold = args.substring(spaceIndex + 1).toFloat();

    if (setSensorThreshold(id, threshold)) {
        return "✅ Sensor ID " + String(id) + " threshold set to " + String(threshold, 2);
    } else {
        return "❌ Sensor ID " + String(id) + " not found";
    }
}

String SensorHandler::parseListCommand() {
    return getStatusReport();
}

String SensorHandler::parseReadAllCommand() {
    return readAllSensors();
}

// Utility methods
String SensorHandler::getSensorInfo(uint8_t id) {
    int index = findSensorById(id);
    if (index == -1) {
        return "Sensor ID " + String(id) + " not found";
    }

    String info = sensors[index].name + " (ID: " + String(id) + ")\n";
    info += "Type: " + sensorTypeToString(sensors[index].type) + "\n";
    info += "Pin: " + String(sensors[index].pin) + "\n";
    info += "Value: " + String(sensors[index].value, 2) + "\n";
    info += "Status: " + String(sensors[index].enabled ? "Enabled" : "Disabled") + "\n";

    if (sensors[index].alertEnabled) {
        info += "Threshold: " + String(sensors[index].threshold, 2) + "\n";
    }

    if (sensors[index].lastRead > 0) {
        info += "Last Read: " + String((millis() - sensors[index].lastRead) / 1000) + "s ago";
    }

    return info;
}

bool SensorHandler::isSensorAvailable(uint8_t pin, SensorType type) {
    return isValidSensorPin(pin, type) && findSensorByPin(pin) == -1;
}

void SensorHandler::resetAllSensors() {
    for (int i = 0; i < sensorCount; i++) {
        if (sensors[i].id != 255) {
            removeSensor(sensors[i].id);
        }
    }
    sensorCount = 0;
    saveConfiguration();
}

String SensorHandler::getSensorName(uint8_t id) {
    int index = findSensorById(id);
    return (index != -1) ? sensors[index].name : "";
}

SensorType SensorHandler::getSensorType(uint8_t id) {
    int index = findSensorById(id);
    return (index != -1) ? sensors[index].type : SENSOR_NONE;
}

float SensorHandler::getSensorValue(uint8_t id) {
    int index = findSensorById(id);
    return (index != -1) ? sensors[index].value : 0.0;
}

float SensorHandler::getSensorThreshold(uint8_t id) {
    int index = findSensorById(id);
    return (index != -1) ? sensors[index].threshold : 0.0;
}

bool SensorHandler::isSensorEnabled(uint8_t id) {
    int index = findSensorById(id);
    return (index != -1) ? sensors[index].enabled : false;
}
