#include "sensor_handler.h"

SensorHandler::SensorHandler() : sensorCount(0), lastUpdate(0) {
    // Initialize sensors array
    for (int i = 0; i < MAX_SENSORS; i++) {
        sensors[i] = SensorConfig();
        dhtSensors[i] = nullptr;
    }
}

SensorHandler::~SensorHandler() {
    // Cleanup DHT sensors
    for (int i = 0; i < MAX_SENSORS; i++) {
        cleanupDHTSensor(i);
    }
}

void SensorHandler::begin() {
    loadConfiguration();
    
    // Initialize configured sensors
    for (int i = 0; i < sensorCount; i++) {
        if (sensors[i].enabled) {
            switch (sensors[i].type) {
                case SENSOR_DHT11:
                case SENSOR_DHT22:
                    initializeDHTSensor(i);
                    break;
                case SENSOR_PIR:
                case SENSOR_DIGITAL:
                    pinMode(sensors[i].pin, INPUT_PULLUP);
                    break;
                case SENSOR_LDR:
                    // LDR typically uses analog input (A0)
                    break;
                default:
                    break;
            }
        }
    }
    
    Serial.println("Sensor Handler initialized with " + String(sensorCount) + " sensors");
}

bool SensorHandler::isValidSensorPin(uint8_t pin, SensorType type) {
    // Check if pin is valid for the sensor type
    if (type == SENSOR_LDR && pin != A0) {
        return false; // LDR requires analog input
    }
    
    // Valid digital pins for ESP8266
    uint8_t validPins[] = {0, 1, 2, 3, 4, 5, 12, 13, 14, 15, 16};
    int validPinCount = sizeof(validPins) / sizeof(validPins[0]);
    
    for (int i = 0; i < validPinCount; i++) {
        if (validPins[i] == pin) {
            return true;
        }
    }
    
    return pin == A0; // Allow A0 for analog sensors
}

int SensorHandler::findSensorIndex(uint8_t pin) {
    for (int i = 0; i < sensorCount; i++) {
        if (sensors[i].pin == pin) {
            return i;
        }
    }
    return -1;
}

bool SensorHandler::addSensor(uint8_t pin, SensorType type, String name, float threshold) {
    if (!isValidSensorPin(pin, type)) {
        Serial.println("Invalid pin for sensor type: " + String(pin));
        return false;
    }
    
    // Check if sensor already exists
    int index = findSensorIndex(pin);
    if (index == -1) {
        // Add new sensor
        if (sensorCount >= MAX_SENSORS) {
            Serial.println("Maximum sensor count reached");
            return false;
        }
        index = sensorCount++;
    } else {
        // Cleanup existing sensor if it's a DHT
        cleanupDHTSensor(index);
    }
    
    // Configure sensor
    sensors[index].pin = pin;
    sensors[index].type = type;
    sensors[index].name = name.length() > 0 ? name : sensorTypeToString(type) + "_" + String(pin);
    sensors[index].enabled = true;
    sensors[index].threshold = threshold;
    sensors[index].lastValue = 0;
    sensors[index].lastRead = 0;
    
    // Initialize hardware
    switch (type) {
        case SENSOR_DHT11:
        case SENSOR_DHT22:
            initializeDHTSensor(index);
            break;
        case SENSOR_PIR:
        case SENSOR_DIGITAL:
            pinMode(pin, INPUT_PULLUP);
            break;
        case SENSOR_LDR:
            // No special initialization needed for analog
            break;
        default:
            sensors[index].enabled = false;
            return false;
    }
    
    saveConfiguration();
    Serial.println("Sensor added: " + sensors[index].name + " on pin " + String(pin));
    return true;
}

bool SensorHandler::removeSensor(uint8_t pin) {
    int index = findSensorIndex(pin);
    if (index == -1) {
        return false;
    }
    
    // Cleanup DHT sensor if needed
    cleanupDHTSensor(index);
    
    // Shift remaining sensors
    for (int i = index; i < sensorCount - 1; i++) {
        sensors[i] = sensors[i + 1];
        dhtSensors[i] = dhtSensors[i + 1];
    }
    sensorCount--;
    dhtSensors[sensorCount] = nullptr;
    
    saveConfiguration();
    Serial.println("Sensor removed from pin " + String(pin));
    return true;
}

bool SensorHandler::enableSensor(uint8_t pin) {
    int index = findSensorIndex(pin);
    if (index == -1) {
        return false;
    }
    
    sensors[index].enabled = true;
    saveConfiguration();
    return true;
}

bool SensorHandler::disableSensor(uint8_t pin) {
    int index = findSensorIndex(pin);
    if (index == -1) {
        return false;
    }
    
    sensors[index].enabled = false;
    saveConfiguration();
    return true;
}

bool SensorHandler::setSensorThreshold(uint8_t pin, float threshold) {
    int index = findSensorIndex(pin);
    if (index == -1) {
        return false;
    }
    
    sensors[index].threshold = threshold;
    saveConfiguration();
    return true;
}

float SensorHandler::readSensor(uint8_t pin) {
    int index = findSensorIndex(pin);
    if (index == -1 || !sensors[index].enabled) {
        return NAN;
    }
    
    float value = readSensorValue(index);
    sensors[index].lastValue = value;
    sensors[index].lastRead = millis();
    
    return value;
}

String SensorHandler::readSensorFormatted(uint8_t pin) {
    int index = findSensorIndex(pin);
    if (index == -1 || !sensors[index].enabled) {
        return "Sensor not found or disabled";
    }
    
    float value = readSensor(pin);
    if (isnan(value)) {
        return "Error reading sensor";
    }
    
    String formatted = sensors[index].name + ": ";
    
    switch (sensors[index].type) {
        case SENSOR_DHT11:
        case SENSOR_DHT22:
            // For DHT, we need to read both temperature and humidity
            if (dhtSensors[index]) {
                float temp = dhtSensors[index]->readTemperature();
                float hum = dhtSensors[index]->readHumidity();
                if (!isnan(temp) && !isnan(hum)) {
                    formatted = sensors[index].name + ":\n";
                    formatted += "🌡️ Temperature: " + String(temp, 1) + "°C\n";
                    formatted += "💧 Humidity: " + String(hum, 1) + "%";
                } else {
                    formatted += "Error reading DHT sensor";
                }
            }
            break;
        case SENSOR_LDR:
            formatted += String(value, 0) + " (Light level)";
            break;
        case SENSOR_PIR:
            formatted += value > 0 ? "Motion detected" : "No motion";
            break;
        case SENSOR_DIGITAL:
            formatted += value > 0 ? "HIGH" : "LOW";
            break;
        default:
            formatted += String(value, 2);
            break;
    }
    
    return formatted;
}

void SensorHandler::updateAllSensors() {
    for (int i = 0; i < sensorCount; i++) {
        if (sensors[i].enabled) {
            readSensor(sensors[i].pin);
        }
    }
    lastUpdate = millis();
}

String SensorHandler::readAllSensors() {
    if (sensorCount == 0) {
        return "📝 No sensors configured.";
    }
    
    String readings = "📊 *Sensor Readings:*\n\n";
    bool hasReadings = false;
    
    for (int i = 0; i < sensorCount; i++) {
        if (sensors[i].enabled) {
            hasReadings = true;
            String reading = readSensorFormatted(sensors[i].pin);
            readings += "📍 " + reading + "\n\n";
        }
    }
    
    if (!hasReadings) {
        return "📝 No enabled sensors.";
    }
    
    readings += "🕒 Last updated: " + String((millis() - lastUpdate) / 1000) + "s ago";
    return readings;
}

String SensorHandler::listSensors() {
    if (sensorCount == 0) {
        return "📝 No sensors configured.";
    }
    
    String list = "📊 *Configured Sensors:*\n\n";
    
    for (int i = 0; i < sensorCount; i++) {
        String status = sensors[i].enabled ? "✅" : "❌";
        list += status + " **" + sensors[i].name + "** (Pin " + String(sensors[i].pin) + ")\n";
        list += "   📋 Type: " + sensorTypeToString(sensors[i].type) + "\n";
        list += "   📊 Last Value: " + String(sensors[i].lastValue, 2) + "\n";
        if (sensors[i].threshold > 0) {
            list += "   ⚠️ Threshold: " + String(sensors[i].threshold, 2) + "\n";
        }
        list += "\n";
    }
    
    return list;
}

String SensorHandler::getSensorStatus(uint8_t pin) {
    int index = findSensorIndex(pin);
    if (index == -1) {
        return "Sensor not configured";
    }
    
    String status = "📊 **" + sensors[index].name + "** (Pin " + String(pin) + ")\n";
    status += "📋 Type: " + sensorTypeToString(sensors[index].type) + "\n";
    status += "📊 Last Value: " + String(sensors[index].lastValue, 2) + "\n";
    status += "⚡ Status: " + String(sensors[index].enabled ? "Enabled" : "Disabled") + "\n";
    if (sensors[index].threshold > 0) {
        status += "⚠️ Threshold: " + String(sensors[index].threshold, 2) + "\n";
    }
    status += "🕒 Last Read: " + String((millis() - sensors[index].lastRead) / 1000) + "s ago";
    
    return status;
}

float SensorHandler::readSensorValue(int index) {
    switch (sensors[index].type) {
        case SENSOR_DHT11:
        case SENSOR_DHT22:
            if (dhtSensors[index]) {
                return dhtSensors[index]->readTemperature();
            }
            break;
        case SENSOR_LDR:
            return analogRead(sensors[index].pin);
        case SENSOR_PIR:
        case SENSOR_DIGITAL:
            return digitalRead(sensors[index].pin) ? 1.0 : 0.0;
        default:
            break;
    }
    return NAN;
}

void SensorHandler::initializeDHTSensor(int index) {
    cleanupDHTSensor(index); // Cleanup existing if any
    
    uint8_t dhtType = (sensors[index].type == SENSOR_DHT11) ? DHT11 : DHT22;
    dhtSensors[index] = new DHT(sensors[index].pin, dhtType);
    dhtSensors[index]->begin();
}

void SensorHandler::cleanupDHTSensor(int index) {
    if (dhtSensors[index]) {
        delete dhtSensors[index];
        dhtSensors[index] = nullptr;
    }
}

String SensorHandler::sensorTypeToString(SensorType type) {
    switch (type) {
        case SENSOR_DHT11: return "DHT11";
        case SENSOR_DHT22: return "DHT22";
        case SENSOR_LDR: return "LDR";
        case SENSOR_PIR: return "PIR";
        case SENSOR_DIGITAL: return "Digital";
        default: return "Unknown";
    }
}

SensorType SensorHandler::stringToSensorType(String typeStr) {
    typeStr.toLowerCase();
    if (typeStr == "dht11") return SENSOR_DHT11;
    if (typeStr == "dht22") return SENSOR_DHT22;
    if (typeStr == "ldr") return SENSOR_LDR;
    if (typeStr == "pir") return SENSOR_PIR;
    if (typeStr == "digital") return SENSOR_DIGITAL;
    return SENSOR_NONE;
}

void SensorHandler::saveConfiguration() {
    // Save sensor count
    EEPROM.put(SENSOR_CONFIG_ADDR, sensorCount);

    // Save sensor configurations (simplified structure)
    for (int i = 0; i < sensorCount && i < MAX_SENSORS; i++) {
        int addr = SENSOR_CONFIG_ADDR + 4 + (i * 40); // Fixed size per sensor
        EEPROM.put(addr, sensors[i].pin);
        EEPROM.put(addr + 1, (uint8_t)sensors[i].type);
        EEPROM.put(addr + 2, sensors[i].enabled);
        EEPROM.put(addr + 4, sensors[i].threshold);
        EEPROM.put(addr + 8, sensors[i].lastValue);
        // Store name as fixed 20 char array
        char nameBuffer[20] = {0};
        sensors[i].name.toCharArray(nameBuffer, 20);
        for (int j = 0; j < 20; j++) {
            EEPROM.put(addr + 12 + j, nameBuffer[j]);
        }
    }

    EEPROM.commit();
}

void SensorHandler::loadConfiguration() {
    // Load sensor count
    EEPROM.get(SENSOR_CONFIG_ADDR, sensorCount);

    // Validate sensor count
    if (sensorCount < 0 || sensorCount > MAX_SENSORS) {
        sensorCount = 0;
        return;
    }

    // Load sensor configurations (simplified structure)
    for (int i = 0; i < sensorCount; i++) {
        int addr = SENSOR_CONFIG_ADDR + 4 + (i * 40); // Fixed size per sensor
        EEPROM.get(addr, sensors[i].pin);
        uint8_t type;
        EEPROM.get(addr + 1, type);
        sensors[i].type = (SensorType)type;
        EEPROM.get(addr + 2, sensors[i].enabled);
        EEPROM.get(addr + 4, sensors[i].threshold);
        EEPROM.get(addr + 8, sensors[i].lastValue);
        // Load name from fixed 20 char array
        char nameBuffer[20] = {0};
        for (int j = 0; j < 20; j++) {
            EEPROM.get(addr + 12 + j, nameBuffer[j]);
        }
        sensors[i].name = String(nameBuffer);
        sensors[i].lastRead = 0; // Reset timestamp
    }
}

void SensorHandler::clearConfiguration() {
    // Cleanup all DHT sensors
    for (int i = 0; i < sensorCount; i++) {
        cleanupDHTSensor(i);
    }
    
    sensorCount = 0;
    saveConfiguration();
}

bool SensorHandler::checkThresholds() {
    bool alertTriggered = false;
    
    for (int i = 0; i < sensorCount; i++) {
        if (!sensors[i].enabled || sensors[i].threshold <= 0) {
            continue;
        }
        
        float currentValue = readSensorValue(i);
        if (!isnan(currentValue) && currentValue > sensors[i].threshold) {
            alertTriggered = true;
        }
    }
    
    return alertTriggered;
}

String SensorHandler::getThresholdAlerts() {
    String alerts = "";
    
    for (int i = 0; i < sensorCount; i++) {
        if (!sensors[i].enabled || sensors[i].threshold <= 0) {
            continue;
        }
        
        float currentValue = readSensorValue(i);
        if (!isnan(currentValue) && currentValue > sensors[i].threshold) {
            alerts += "⚠️ " + sensors[i].name + ": " + String(currentValue, 2);
            alerts += " > " + String(sensors[i].threshold, 2) + "\n";
        }
    }
    
    return alerts;
}

int SensorHandler::getConfiguredSensorCount() {
    return sensorCount;
}

String SensorHandler::getAvailableSensorTypes() {
    String types = "📊 *Available Sensor Types:*\n\n";
    types += "🌡️ **DHT11** - Temperature & Humidity\n";
    types += "🌡️ **DHT22** - Temperature & Humidity (Higher precision)\n";
    types += "💡 **LDR** - Light sensor (A0 only)\n";
    types += "🚶 **PIR** - Motion sensor\n";
    types += "🔘 **Digital** - Generic digital input\n";
    
    return types;
}

void SensorHandler::loop() {
    if (millis() - lastUpdate >= SENSOR_UPDATE_INTERVAL) {
        updateAllSensors();
    }
}
