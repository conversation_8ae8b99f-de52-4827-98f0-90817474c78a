#ifndef AUTOMATION_H
#define AUTOMATION_H

#include <Arduino.h>
#include <Preferences.h>
#include "config.h"

// Forward declarations
class GPIOManager;
class SensorHandler;
class TelegramBot;

struct AutomationRule {
    uint8_t id;
    String name;
    bool enabled;

    // Trigger
    TriggerType triggerType;
    uint8_t triggerPin;
    uint8_t triggerSensorId;
    float triggerValue;
    unsigned long triggerInterval; // For time-based triggers
    unsigned long lastTriggered;

    // Action
    ActionType actionType;
    uint8_t actionPin;
    int actionValue;
    String actionMessage;

    // Conditions
    bool hasCondition;
    uint8_t conditionPin;
    int conditionValue;

    unsigned long createdTime;
};

class AutomationEngine {
private:
    Preferences preferences;
    AutomationRule rules[MAX_AUTOMATIONS];
    int ruleCount;
    bool initialized;

    // Module references
    GPIOManager* gpioManager;
    SensorHandler* sensorHandler;
    TelegramBot* telegramBot;

    // Timing
    unsigned long lastRuleCheck;
    const unsigned long RULE_CHECK_INTERVAL = 1000; // 1 second

    // Private methods
    int findFreeRuleSlot();
    int findRuleById(uint8_t id);
    bool evaluateTrigger(int ruleIndex);
    bool evaluateCondition(int ruleIndex);
    void executeAction(int ruleIndex);
    void saveConfiguration();
    void loadConfiguration();

    // Command parsing
    String parseAddCommand(const String& args);
    String parseEnableCommand(const String& args);
    String parseDisableCommand(const String& args);
    String parseRemoveCommand(const String& args);
    String parseTriggerCommand(const String& args);
    String parseListCommand();

    // Helper methods
    String triggerTypeToString(TriggerType type);
    String actionTypeToString(ActionType type);
    TriggerType parseTriggerType(const String& typeStr);
    ActionType parseActionType(const String& typeStr);

public:
    AutomationEngine();
    ~AutomationEngine();

    // Initialization
    bool begin();
    void loop();
    void setModuleReferences(GPIOManager* gpio, SensorHandler* sensor, TelegramBot* bot);

    // Rule management
    bool addRule(const String& name, TriggerType triggerType, ActionType actionType);
    bool removeRule(uint8_t id);
    bool enableRule(uint8_t id, bool enable = true);
    bool triggerRule(uint8_t id); // Manual trigger

    // Configuration
    bool setRuleTrigger(uint8_t id, TriggerType type, uint8_t pin, float value);
    bool setRuleAction(uint8_t id, ActionType type, uint8_t pin, int value, const String& message = "");
    bool setRuleCondition(uint8_t id, uint8_t pin, int value);
    bool setRuleInterval(uint8_t id, unsigned long interval);

    // Information methods
    String getStatusReport();
    String getRuleInfo(uint8_t id);
    int getActiveRuleCount() const;
    int getTotalRuleCount() const { return ruleCount; }

    // Command interface
    String executeCommand(const String& command);

    // Utility methods
    void resetAllRules();
    bool isRuleEnabled(uint8_t id);
    String getRuleName(uint8_t id);
};

#endif // AUTOMATION_H
