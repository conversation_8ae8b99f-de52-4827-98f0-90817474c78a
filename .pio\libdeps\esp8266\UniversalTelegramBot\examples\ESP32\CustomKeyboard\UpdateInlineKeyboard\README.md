#ESP32 - LED button update

This is an example of how one can update inline keyboard messages (buttons).
The message_id of the specific message is sent when a message is received.
This message_id can be used to UPDATE that message.
One can update text inside a message, but also buttons can be updated.
This way one can build menu's, like the menu the botfather uses.

In this simple example we use a inlinekeyboard button to toggle (and update) the state of a LED.

NOTE: You will need to enter your SSID, password and bot Token for the example to work.

Example and update to Universal-Arduino-Telegram-Bot originally written by 
[<PERSON><PERSON> <PERSON>] (https://github.com/fritsjan) with inspiration from [<PERSON><PERSON><PERSON>] (https://github.com/RomeHein)

Adapted by [<PERSON>](https://github.com/witnessmenow)

## License

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
