#include "telegram_bot.h"
#include "gpio_manager.h"
#include "sensor_handler.h"
#include "automation.h"

TelegramBot::TelegramBot() : bot(nullptr), lastBotRan(0), initialized(false),
                             gpioManager(nullptr), sensor<PERSON>andler(nullptr), automationEngine(nullptr) {
    client.setInsecure(); // For simplicity, use insecure connection
}

TelegramBot::~TelegramBot() {
    if (bot) {
        delete bot;
    }
}

bool TelegramBot::begin(const String& token, const String& chat_id) {
    botToken = token;
    chatID = chat_id;
    
    if (botToken.length() == 0 || chatID.length() == 0) {
        DEBUG_PRINTLN("TelegramBot: Invalid token or chat ID");
        return false;
    }
    
    // Initialize bot
    bot = new UniversalTelegramBot(botToken, client);
    bot->longPoll = BOT_LONG_POLL_TIMEOUT;
    
    DEBUG_PRINTLN("TelegramBot: Initializing...");
    
    // Test connection
    String botName = getBotUsername();
    if (botName.length() > 0) {
        DEBUG_PRINTF("TelegramBot: Connected as @%s\n", botName.c_str());
        initialized = true;
        return true;
    } else {
        DEBUG_PRINTLN("TelegramBot: Failed to connect");
        return false;
    }
}

void TelegramBot::setModuleReferences(GPIOManager* gpio, SensorHandler* sensor, AutomationEngine* automation) {
    gpioManager = gpio;
    sensorHandler = sensor;
    automationEngine = automation;
}

void TelegramBot::loop() {
    if (!initialized || !bot) {
        return;
    }
    
    if (millis() - lastBotRan > BOT_CHECK_INTERVAL) {
        int numNewMessages = bot->getUpdates(bot->last_message_received + 1);
        
        while (numNewMessages) {
            DEBUG_PRINTF("TelegramBot: Processing %d new messages\n", numNewMessages);
            handleNewMessages(numNewMessages);
            numNewMessages = bot->getUpdates(bot->last_message_received + 1);
        }
        
        lastBotRan = millis();
    }
}

void TelegramBot::handleNewMessages(int numNewMessages) {
    for (int i = 0; i < numNewMessages; i++) {
        String chatId = String(bot->messages[i].chat_id);
        String text = bot->messages[i].text;
        String fromName = bot->messages[i].from_name;
        
        // Check for callback query (inline keyboard button press)
        if (bot->messages[i].type == "callback_query") {
            handleCallbackQuery(bot->messages[i].text, chatId, bot->messages[i].text);
        } else {
            handleMessage(chatId, text, fromName);
        }
    }
}

void TelegramBot::handleMessage(String chatId, String text, String fromName) {
    DEBUG_PRINTF("TelegramBot: Message from %s (%s): %s\n", fromName.c_str(), chatId.c_str(), text.c_str());
    
    // Check authorization
    if (!isAuthorizedUser(chatId)) {
        sendMessage(chatId, "❌ Unauthorized access. This bot is configured for a specific user only.");
        return;
    }
    
    // Send typing indicator
    sendTypingAction(chatId);
    
    // Convert to lowercase for command matching
    text.toLowerCase();
    text.trim();
    
    // Handle commands
    if (text == "/start" || text == "start") {
        processStartCommand(chatId);
    }
    else if (text == "/help" || text == "help" || text == "?") {
        processHelpCommand(chatId);
    }
    else if (text == "/status" || text == "status") {
        processStatusCommand(chatId);
    }
    else if (text.startsWith("/gpio") || text.startsWith("gpio")) {
        String args = text.substring(text.indexOf(' ') + 1);
        processGPIOCommand(chatId, args);
    }
    else if (text.startsWith("/sensor") || text.startsWith("sensor")) {
        String args = text.substring(text.indexOf(' ') + 1);
        processSensorCommand(chatId, args);
    }
    else if (text.startsWith("/automation") || text.startsWith("automation")) {
        String args = text.substring(text.indexOf(' ') + 1);
        processAutomationCommand(chatId, args);
    }
    else if (text.startsWith("/system") || text.startsWith("system")) {
        String args = text.substring(text.indexOf(' ') + 1);
        processSystemCommand(chatId, args);
    }
    else {
        // Unknown command - show help
        String response = "❓ Unknown command: `" + text + "`\n\n";
        response += "Type /help to see available commands.";
        sendMessage(chatId, response);
    }
}

void TelegramBot::handleCallbackQuery(String queryId, String chatId, String data) {
    DEBUG_PRINTF("TelegramBot: Callback query: %s\n", data.c_str());
    
    // Answer the callback query to remove loading state
    bot->answerCallbackQuery(queryId);
    
    // Process the callback data
    if (data.startsWith("gpio_")) {
        processGPIOCommand(chatId, data.substring(5));
    }
    else if (data.startsWith("sensor_")) {
        processSensorCommand(chatId, data.substring(7));
    }
    else if (data.startsWith("auto_")) {
        processAutomationCommand(chatId, data.substring(5));
    }
    else if (data.startsWith("sys_")) {
        processSystemCommand(chatId, data.substring(4));
    }
}

void TelegramBot::processStartCommand(String chatId) {
    String welcome = "🤖 *Welcome to ESPgram!*\n\n";
    welcome += "Your ESP32 is ready for Telegram control.\n\n";
    welcome += "*Available Commands:*\n";
    welcome += "• `/gpio` - Control GPIO pins\n";
    welcome += "• `/sensor` - Manage sensors\n";
    welcome += "• `/automation` - Setup automations\n";
    welcome += "• `/status` - System status\n";
    welcome += "• `/help` - Full command list\n\n";
    welcome += "*Examples:*\n";
    welcome += "• `/gpio set 2 output LED`\n";
    welcome += "• `/gpio write 2 1`\n";
    welcome += "• `/sensor add dht22 4 Room`\n";
    welcome += "• `/automation add`\n\n";
    welcome += "Type any command to get started!";

    sendMessage(chatId, welcome);
}

void TelegramBot::processHelpCommand(String chatId) {
    String help = "📚 *ESPgram Command Reference*\n\n";

    help += "*🔌 GPIO Commands:*\n";
    help += "• `/gpio list` - Show all pins\n";
    help += "• `/gpio set <pin> <mode> [name]` - Configure pin\n";
    help += "• `/gpio read <pin>` - Read pin value\n";
    help += "• `/gpio write <pin> <value>` - Write to pin (0/1)\n";
    help += "• `/gpio pwm <pin> <value>` - Set PWM (0-255)\n";
    help += "• `/gpio toggle <pin>` - Toggle digital output\n";
    help += "• `/gpio remove <pin>` - Remove pin config\n\n";

    help += "*🌡️ Sensor Commands:*\n";
    help += "• `/sensor list` - Show all sensors\n";
    help += "• `/sensor add <type> <pin> [name]` - Add sensor\n";
    help += "• `/sensor read <id>` - Read sensor value\n";
    help += "• `/sensor readall` - Read all sensors\n";
    help += "• `/sensor remove <id>` - Remove sensor\n";
    help += "• `/sensor threshold <id> <value>` - Set alert threshold\n\n";

    help += "*⚙️ Automation Commands:*\n";
    help += "• `/automation list` - Show all rules\n";
    help += "• `/automation add` - Create new rule\n";
    help += "• `/automation enable <id>` - Enable rule\n";
    help += "• `/automation disable <id>` - Disable rule\n";
    help += "• `/automation remove <id>` - Delete rule\n";
    help += "• `/automation trigger <id>` - Manual trigger\n\n";

    help += "*🔧 System Commands:*\n";
    help += "• `/status` - Complete system status\n";
    help += "• `/system restart` - Restart ESP32\n";
    help += "• `/system reset` - Factory reset\n";
    help += "• `/system wifi` - WiFi information\n\n";

    help += "*📝 Examples:*\n";
    help += "• `/gpio set 2 output LED`\n";
    help += "• `/sensor add dht22 4 Room`\n";
    help += "• `/sensor threshold 1 25.0`\n";
    help += "• `/automation add`\n\n";

    help += "*🔧 Sensor Types:*\n";
    help += "• `dht11` - Temperature/Humidity\n";
    help += "• `dht22` - Temperature/Humidity (better)\n";
    help += "• `ldr` - Light sensor (analog)\n";
    help += "• `pir` - Motion sensor (digital)\n";
    help += "• `ir` - Infrared sensor (digital)\n\n";

    help += "*🔌 GPIO Modes:*\n";
    help += "• `output` - Digital output (0/1)\n";
    help += "• `input` - Digital input\n";
    help += "• `pwm` - PWM output (0-255)\n";
    help += "• `analog` - Analog input (ADC)\n\n";

    help += "💡 *Tip:* All commands work without buttons!";

    sendMessage(chatId, help);
}

void TelegramBot::processStatusCommand(String chatId) {
    String status = "📊 *System Status*\n\n";
    
    // System info
    status += "*🔧 System:*\n";
    status += "• Firmware: " + String(FW_NAME) + " v" + String(FW_VERSION) + "\n";
    status += "• Uptime: " + formatUptime() + "\n";
    status += "• Free Memory: " + formatMemoryInfo() + "\n\n";
    
    // WiFi info
    status += "*📶 WiFi:*\n";
    status += "• Status: ";
    status += WiFi.isConnected() ? "Connected ✅" : "Disconnected ❌";
    status += "\n";
    if (WiFi.isConnected()) {
        status += "• SSID: " + WiFi.SSID() + "\n";
        status += "• IP: " + WiFi.localIP().toString() + "\n";
        status += "• Signal: " + String(WiFi.RSSI()) + " dBm\n";
    }
    status += "\n";
    
    // Module status
    if (gpioManager) {
        status += "*🔌 GPIO:* " + String(gpioManager->getConfiguredPinCount()) + " pins configured\n";
    }
    if (sensorHandler) {
        status += "*🌡️ Sensors:* " + String(sensorHandler->getSensorCount()) + " sensors active\n";
    }
    if (automationEngine) {
        status += "*⚙️ Automations:* " + String(automationEngine->getActiveRuleCount()) + " rules active\n";
    }
    
    sendMessage(chatId, status);
}

void TelegramBot::processGPIOCommand(String chatId, String args) {
    if (!gpioManager) {
        sendMessage(chatId, "❌ GPIO manager not available");
        return;
    }

    if (args == "list" || args.length() == 0) {
        String response = "🔌 *GPIO Pin Status*\n\n";
        response += gpioManager->getStatusReport();
        response += "\n*Commands:*\n";
        response += "• `/gpio set <pin> <mode> [name]`\n";
        response += "• `/gpio write <pin> <value>`\n";
        response += "• `/gpio read <pin>`\n";
        response += "• `/gpio pwm <pin> <value>`\n";
        response += "• `/gpio toggle <pin>`\n";
        response += "• `/gpio remove <pin>`";
        sendMessage(chatId, response);
    }
    else {
        // Parse and execute GPIO command
        String result = gpioManager->executeCommand(args);
        sendMessage(chatId, result);
    }
}

void TelegramBot::processSensorCommand(String chatId, String args) {
    if (!sensorHandler) {
        sendMessage(chatId, "❌ Sensor handler not available");
        return;
    }

    if (args == "list" || args.length() == 0) {
        String response = "🌡️ *Sensor Status*\n\n";
        response += sensorHandler->getStatusReport();
        response += "\n*Commands:*\n";
        response += "• `/sensor add <type> <pin> [name]`\n";
        response += "• `/sensor read <id>`\n";
        response += "• `/sensor readall`\n";
        response += "• `/sensor remove <id>`\n";
        response += "• `/sensor threshold <id> <value>`\n";
        response += "\n*Types:* dht11, dht22, ldr, pir, ir";
        sendMessage(chatId, response);
    }
    else {
        // Parse and execute sensor command
        String result = sensorHandler->executeCommand(args);
        sendMessage(chatId, result);
    }
}

void TelegramBot::processAutomationCommand(String chatId, String args) {
    if (!automationEngine) {
        sendMessage(chatId, "❌ Automation engine not available");
        return;
    }

    if (args == "list" || args.length() == 0) {
        String response = "⚙️ *Automation Rules*\n\n";
        response += automationEngine->getStatusReport();
        response += "\n*Commands:*\n";
        response += "• `/automation add`\n";
        response += "• `/automation enable <id>`\n";
        response += "• `/automation disable <id>`\n";
        response += "• `/automation remove <id>`\n";
        response += "• `/automation trigger <id>`\n";
        response += "\n*Example:* Create rule with guided setup";
        sendMessage(chatId, response);
    }
    else {
        // Parse and execute automation command
        String result = automationEngine->executeCommand(args);
        sendMessage(chatId, result);
    }
}

void TelegramBot::processSystemCommand(String chatId, String args) {
    if (args == "restart") {
        sendMessage(chatId, "🔄 Restarting ESP32...");
        delay(1000);
        ESP.restart();
    }
    else if (args == "reset") {
        sendMessage(chatId, "⚠️ Factory reset will erase all settings. Send 'CONFIRM RESET' to proceed.");
    }
    else if (args == "CONFIRM RESET") {
        sendMessage(chatId, "🏭 Performing factory reset...");
        // Clear all preferences and restart
        Preferences prefs;
        prefs.begin(PREFS_NAMESPACE, false);
        prefs.clear();
        prefs.end();
        delay(1000);
        ESP.restart();
    }
    else if (args == "wifi") {
        String wifiInfo = "📶 *WiFi Information*\n\n";
        wifiInfo += "• Status: ";
        wifiInfo += WiFi.isConnected() ? "Connected" : "Disconnected";
        wifiInfo += "\n";
        if (WiFi.isConnected()) {
            wifiInfo += "• SSID: " + WiFi.SSID() + "\n";
            wifiInfo += "• IP Address: " + WiFi.localIP().toString() + "\n";
            wifiInfo += "• Gateway: " + WiFi.gatewayIP().toString() + "\n";
            wifiInfo += "• DNS: " + WiFi.dnsIP().toString() + "\n";
            wifiInfo += "• Signal Strength: " + String(WiFi.RSSI()) + " dBm\n";
            wifiInfo += "• MAC Address: " + WiFi.macAddress() + "\n";
        }
        sendMessage(chatId, wifiInfo);
    }
    else {
        String sysInfo = "🔧 *System Commands*\n\n";
        sysInfo += "*Available Commands:*\n";
        sysInfo += "• `/system restart` - Restart ESP32\n";
        sysInfo += "• `/system reset` - Factory reset\n";
        sysInfo += "• `/system wifi` - WiFi information\n";
        sysInfo += "• `/status` - Full system status\n\n";
        sysInfo += "Type any command above to execute.";
        sendMessage(chatId, sysInfo);
    }
}

// Removed keyboard generators - using simple commands instead

// Message sending methods
bool TelegramBot::sendMessage(const String& message, bool useMarkdown) {
    return sendMessage(chatID, message, useMarkdown);
}

bool TelegramBot::sendMessage(const String& chatId, const String& message, bool useMarkdown) {
    if (!initialized || !bot) {
        return false;
    }

    String parseMode = useMarkdown ? "Markdown" : "";
    return bot->sendMessage(chatId, message, parseMode);
}

bool TelegramBot::sendMessageWithKeyboard(const String& message, const String& keyboard) {
    if (!initialized || !bot) {
        return false;
    }

    return bot->sendMessageWithInlineKeyboard(chatID, message, "Markdown", keyboard);
}

bool TelegramBot::sendPhoto(const String& chatId, const String& photo, const String& caption) {
    if (!initialized || !bot) {
        return false;
    }

    return bot->sendPhoto(chatId, photo, caption);
}

// Utility methods
bool TelegramBot::isAuthorizedUser(String chatId) {
    return chatId == chatID;
}

String TelegramBot::formatUptime() {
    unsigned long uptime = millis() / 1000;
    unsigned long days = uptime / 86400;
    uptime %= 86400;
    unsigned long hours = uptime / 3600;
    uptime %= 3600;
    unsigned long minutes = uptime / 60;
    unsigned long seconds = uptime % 60;

    String result = "";
    if (days > 0) result += String(days) + "d ";
    if (hours > 0) result += String(hours) + "h ";
    if (minutes > 0) result += String(minutes) + "m ";
    result += String(seconds) + "s";

    return result;
}

String TelegramBot::formatMemoryInfo() {
    uint32_t freeHeap = ESP.getFreeHeap();
    uint32_t totalHeap = ESP.getHeapSize();
    uint32_t usedHeap = totalHeap - freeHeap;

    String result = String(freeHeap / 1024) + " KB free";
    result += " (" + String((freeHeap * 100) / totalHeap) + "%)";

    return result;
}

String TelegramBot::escapeMarkdown(String text) {
    text.replace("_", "\\_");
    text.replace("*", "\\*");
    text.replace("[", "\\[");
    text.replace("]", "\\]");
    text.replace("(", "\\(");
    text.replace(")", "\\)");
    text.replace("~", "\\~");
    text.replace("`", "\\`");
    text.replace(">", "\\>");
    text.replace("#", "\\#");
    text.replace("+", "\\+");
    text.replace("-", "\\-");
    text.replace("=", "\\=");
    text.replace("|", "\\|");
    text.replace("{", "\\{");
    text.replace("}", "\\}");
    text.replace(".", "\\.");
    text.replace("!", "\\!");
    return text;
}

void TelegramBot::sendTypingAction(String chatId) {
    if (initialized && bot) {
        bot->sendChatAction(chatId, "typing");
    }
}

String TelegramBot::getBotUsername() {
    if (!bot) {
        return "";
    }

    // Try to get bot info - this is a simple implementation
    // In a real scenario, you might want to call getMe API
    return "ESPgram_Bot";
}

// Notification methods
void TelegramBot::sendStartupMessage() {
    String message = "🚀 *ESPgram Started!*\n\n";
    message += "• Firmware: " + String(FW_NAME) + " v" + String(FW_VERSION) + "\n";
    message += "• WiFi: " + WiFi.SSID() + "\n";
    message += "• IP: " + WiFi.localIP().toString() + "\n\n";
    message += "Type /start to begin or /help for commands.";

    sendMessage(message);
}

void TelegramBot::sendErrorNotification(const String& error) {
    String message = "⚠️ *System Error*\n\n";
    message += "Error: " + escapeMarkdown(error) + "\n";
    message += "Time: " + formatUptime();

    sendMessage(message);
}

void TelegramBot::sendSensorAlert(const String& sensorName, const String& value, const String& threshold) {
    String message = "🚨 *Sensor Alert*\n\n";
    message += "Sensor: " + escapeMarkdown(sensorName) + "\n";
    message += "Value: " + escapeMarkdown(value) + "\n";
    message += "Threshold: " + escapeMarkdown(threshold) + "\n";
    message += "Time: " + formatUptime();

    sendMessage(message);
}

void TelegramBot::sendAutomationTriggered(const String& ruleName, const String& action) {
    String message = "⚙️ *Automation Triggered*\n\n";
    message += "Rule: " + escapeMarkdown(ruleName) + "\n";
    message += "Action: " + escapeMarkdown(action) + "\n";
    message += "Time: " + formatUptime();

    sendMessage(message);
}

void TelegramBot::notifyWiFiConnected() {
    String message = "📶 WiFi Connected\n";
    message += "SSID: " + WiFi.SSID() + "\n";
    message += "IP: " + WiFi.localIP().toString();

    sendMessage(message);
}

void TelegramBot::notifyWiFiDisconnected() {
    sendMessage("📶 WiFi Disconnected - Attempting reconnection...");
}

void TelegramBot::notifySystemRestart() {
    sendMessage("🔄 System restarting...");
}

void TelegramBot::notifyLowMemory() {
    String message = "⚠️ Low Memory Warning\n";
    message += "Free: " + formatMemoryInfo();

    sendMessage(message);
}
