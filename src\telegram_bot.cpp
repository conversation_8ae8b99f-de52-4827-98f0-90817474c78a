#include "telegram_bot.h"
#include "gpio_manager.h"
#include "sensor_handler.h"
#include "automation.h"
#include <ESP8266WiFi.h>

TelegramBot::TelegramBot() : lastBotRan(0), bot(nullptr), initialized(false),
                             gpioManager(nullptr), sensor<PERSON>andler(nullptr), automationEngine(nullptr) {
    client.setInsecure(); // For simplicity, use insecure connection
}

TelegramBot::~TelegramBot() {
    if (bot) {
        delete bot;
    }
}

bool TelegramBot::begin(String token, String chat_id) {
    botToken = token;
    chatId = chat_id;
    
    if (bot) {
        delete bot;
    }
    
    bot = new UniversalTelegramBot(botToken, client);
    
    // Test connection
    if (bot->getMe()) {
        initialized = true;
        Serial.println("Telegram bot initialized successfully");
        return true;
    } else {
        Serial.println("Failed to initialize Telegram bot");
        initialized = false;
        return false;
    }
}

void TelegramBot::setModuleReferences(GPIOManager* gpio, SensorHandler* sensor, AutomationEngine* automation) {
    gpioManager = gpio;
    sensorHandler = sensor;
    automationEngine = automation;
}

void TelegramBot::handleMessages() {
    if (!initialized || millis() - lastBotRan < BOT_REQUEST_DELAY) {
        return;
    }
    
    int numNewMessages = bot->getUpdates(bot->last_message_received + 1);
    
    while (numNewMessages) {
        handleNewMessages(numNewMessages);
        numNewMessages = bot->getUpdates(bot->last_message_received + 1);
    }
    
    lastBotRan = millis();
}

void TelegramBot::handleNewMessages(int numNewMessages) {
    for (int i = 0; i < numNewMessages; i++) {
        String chat_id = String(bot->messages[i].chat_id);
        String text = bot->messages[i].text;
        String from_name = bot->messages[i].from_name;
        
        if (!isAuthorized(chat_id)) {
            sendMessage(chat_id, "❌ Unauthorized access. This bot is configured for a specific user.");
            continue;
        }
        
        processCommand(chat_id, text, from_name);
    }
}

void TelegramBot::processCommand(String chatId, String text, String from_name) {
    text.toLowerCase();
    text.trim();
    
    // Main commands
    if (text == "/start" || text == "start" || text == "menu") {
        sendMainMenu(chatId);
    }
    else if (text == "/help" || text == "help") {
        String helpText = "🤖 *ESPgram Bot Commands*\n\n";
        helpText += "📱 *Main Menu:* /start or 'menu'\n";
        helpText += "🔌 *GPIO Control:* Configure and control pins\n";
        helpText += "📊 *Sensors:* Monitor DHT11, LDR, PIR sensors\n";
        helpText += "⚙️ *Automation:* Create if-then rules\n";
        helpText += "🔧 *System:* Status, restart, reset\n\n";
        helpText += "💡 Use the inline buttons for easy navigation!";
        sendMessage(chatId, helpText);
    }
    else if (text.startsWith("gpio_")) {
        handleGPIOCommand(chatId, text);
    }
    else if (text.startsWith("sensor_")) {
        handleSensorCommand(chatId, text);
    }
    else if (text.startsWith("auto_")) {
        handleAutomationCommand(chatId, text);
    }
    else if (text.startsWith("sys_")) {
        handleSystemCommand(chatId, text);
    }
    else {
        sendMessage(chatId, "❓ Unknown command. Type /help for available commands or use the menu buttons.");
    }
}

void TelegramBot::sendMainMenu(String chatId) {
    String buttons[][2] = {
        {"🔌 GPIO Control", "gpio_menu"},
        {"📊 Sensors", "sensor_menu"},
        {"⚙️ Automation", "auto_menu"},
        {"🔧 System", "sys_menu"}
    };
    
    String keyboard = createInlineKeyboard(buttons, 4);
    String message = "🏠 *ESPgram Control Panel*\n\n";
    message += "Welcome! Choose an option below to control your ESP8266:\n\n";
    message += "🔌 *GPIO:* Control digital pins, PWM, read inputs\n";
    message += "📊 *Sensors:* Monitor temperature, humidity, light\n";
    message += "⚙️ *Automation:* Create smart rules\n";
    message += "🔧 *System:* Device status and settings";
    
    sendMessageWithKeyboard(chatId, message, keyboard);
}

void TelegramBot::sendGPIOMenu(String chatId) {
    String buttons[][2] = {
        {"📋 List Pins", "gpio_list"},
        {"➕ Add Pin", "gpio_add"},
        {"🔄 Toggle Pin", "gpio_toggle"},
        {"📖 Read Pin", "gpio_read"},
        {"🔙 Back", "menu"}
    };
    
    String keyboard = createInlineKeyboard(buttons, 5);
    String message = "🔌 *GPIO Control Menu*\n\n";
    message += "Manage your ESP8266 GPIO pins:";
    
    sendMessageWithKeyboard(chatId, message, keyboard);
}

void TelegramBot::sendSensorMenu(String chatId) {
    String buttons[][2] = {
        {"📋 List Sensors", "sensor_list"},
        {"➕ Add Sensor", "sensor_add"},
        {"📊 Read All", "sensor_read_all"},
        {"🔄 Refresh", "sensor_refresh"},
        {"🔙 Back", "menu"}
    };
    
    String keyboard = createInlineKeyboard(buttons, 5);
    String message = "📊 *Sensor Menu*\n\n";
    message += "Monitor your connected sensors:";
    
    sendMessageWithKeyboard(chatId, message, keyboard);
}

void TelegramBot::sendAutomationMenu(String chatId) {
    String buttons[][2] = {
        {"📋 List Rules", "auto_list"},
        {"➕ Add Rule", "auto_add"},
        {"▶️ Enable Rule", "auto_enable"},
        {"⏸️ Disable Rule", "auto_disable"},
        {"🔙 Back", "menu"}
    };
    
    String keyboard = createInlineKeyboard(buttons, 5);
    String message = "⚙️ *Automation Menu*\n\n";
    message += "Create and manage automation rules:";
    
    sendMessageWithKeyboard(chatId, message, keyboard);
}

void TelegramBot::sendSystemMenu(String chatId) {
    String buttons[][2] = {
        {"📊 Status", "sys_status"},
        {"🔄 Restart", "sys_restart"},
        {"🗑️ Reset Config", "sys_reset"},
        {"ℹ️ Info", "sys_info"},
        {"🔙 Back", "menu"}
    };
    
    String keyboard = createInlineKeyboard(buttons, 5);
    String message = "🔧 *System Menu*\n\n";
    message += "System information and controls:";
    
    sendMessageWithKeyboard(chatId, message, keyboard);
}

void TelegramBot::handleGPIOCommand(String chatId, String command) {
    if (!gpioManager) {
        sendMessage(chatId, "❌ GPIO manager not available");
        return;
    }
    
    if (command == "gpio_menu") {
        sendGPIOMenu(chatId);
    }
    else if (command == "gpio_list") {
        String pinList = gpioManager->listPins();
        sendMessage(chatId, pinList.length() > 0 ? pinList : "📝 No GPIO pins configured yet.");
    }
    // Additional GPIO commands will be implemented in gpio_manager.cpp
    else {
        sendMessage(chatId, "🔧 GPIO command: " + command + " (Implementation in progress)");
    }
}

void TelegramBot::handleSensorCommand(String chatId, String command) {
    if (!sensorHandler) {
        sendMessage(chatId, "❌ Sensor handler not available");
        return;
    }
    
    if (command == "sensor_menu") {
        sendSensorMenu(chatId);
    }
    else if (command == "sensor_list") {
        String sensorList = sensorHandler->listSensors();
        sendMessage(chatId, sensorList.length() > 0 ? sensorList : "📝 No sensors configured yet.");
    }
    else if (command == "sensor_read_all") {
        String readings = sensorHandler->readAllSensors();
        sendMessage(chatId, readings.length() > 0 ? readings : "📊 No sensor readings available.");
    }
    // Additional sensor commands will be implemented in sensor_handler.cpp
    else {
        sendMessage(chatId, "📊 Sensor command: " + command + " (Implementation in progress)");
    }
}

void TelegramBot::handleAutomationCommand(String chatId, String command) {
    if (!automationEngine) {
        sendMessage(chatId, "❌ Automation engine not available");
        return;
    }
    
    if (command == "auto_menu") {
        sendAutomationMenu(chatId);
    }
    else if (command == "auto_list") {
        String ruleList = automationEngine->listRules();
        sendMessage(chatId, ruleList.length() > 0 ? ruleList : "📝 No automation rules configured yet.");
    }
    // Additional automation commands will be implemented in automation.cpp
    else {
        sendMessage(chatId, "⚙️ Automation command: " + command + " (Implementation in progress)");
    }
}

void TelegramBot::handleSystemCommand(String chatId, String command) {
    if (command == "sys_menu") {
        sendSystemMenu(chatId);
    }
    else if (command == "sys_status") {
        String status = "📊 *System Status*\n\n";
        status += "🔌 WiFi: " + String(WiFi.SSID()) + "\n";
        status += "📶 Signal: " + String(WiFi.RSSI()) + " dBm\n";
        status += "🌐 IP: " + WiFi.localIP().toString() + "\n";
        status += "💾 Free Heap: " + String(ESP.getFreeHeap()) + " bytes\n";
        status += "⏱️ Uptime: " + String(millis() / 1000) + " seconds\n";
        status += "🔧 Version: " + String(FW_VERSION);
        sendMessage(chatId, status);
    }
    else if (command == "sys_restart") {
        sendMessage(chatId, "🔄 Restarting ESP8266...");
        delay(1000);
        ESP.restart();
    }
    else if (command == "sys_info") {
        String info = "ℹ️ *ESPgram Information*\n\n";
        info += "🤖 Firmware: " + String(FW_NAME) + " v" + String(FW_VERSION) + "\n";
        info += "💻 Chip ID: " + String(ESP.getChipId()) + "\n";
        info += "📦 Flash Size: " + String(ESP.getFlashChipSize()) + " bytes\n";
        info += "🔧 Core Version: " + ESP.getCoreVersion();
        sendMessage(chatId, info);
    }
    else {
        sendMessage(chatId, "🔧 System command: " + command + " (Implementation in progress)");
    }
}

String TelegramBot::createInlineKeyboard(String buttons[][2], int buttonCount) {
    String keyboard = "{\"inline_keyboard\":[";
    
    for (int i = 0; i < buttonCount; i++) {
        if (i > 0) keyboard += ",";
        keyboard += "[{\"text\":\"" + buttons[i][0] + "\",\"callback_data\":\"" + buttons[i][1] + "\"}]";
    }
    
    keyboard += "]}";
    return keyboard;
}

void TelegramBot::sendMessage(String chatId, String text) {
    if (initialized && bot) {
        bot->sendMessage(chatId, text, "Markdown");
    }
}

void TelegramBot::sendMessageWithKeyboard(String chatId, String text, String keyboard) {
    if (initialized && bot) {
        bot->sendMessageWithInlineKeyboard(chatId, text, "Markdown", keyboard);
    }
}

bool TelegramBot::isAuthorized(String chatId) {
    return chatId == this->chatId;
}

void TelegramBot::sendStartupMessage() {
    if (!initialized) return;
    
    String message = "🚀 *ESPgram Started!*\n\n";
    message += "✅ WiFi Connected: " + String(WiFi.SSID()) + "\n";
    message += "🌐 IP Address: " + WiFi.localIP().toString() + "\n";
    message += "🤖 Bot Version: " + String(FW_VERSION) + "\n\n";
    message += "Type /start to open the control panel!";
    
    sendMessage(chatId, message);
}

void TelegramBot::sendNotification(String message) {
    if (initialized) {
        sendMessage(chatId, "🔔 " + message);
    }
}

void TelegramBot::sendSensorUpdate(String sensorName, String value) {
    if (initialized) {
        String message = "📊 *" + sensorName + "*: " + value;
        sendMessage(chatId, message);
    }
}

void TelegramBot::sendAutomationTrigger(String ruleName, String action) {
    if (initialized) {
        String message = "⚙️ *Automation Triggered*\n";
        message += "📋 Rule: " + ruleName + "\n";
        message += "🎯 Action: " + action;
        sendMessage(chatId, message);
    }
}

bool TelegramBot::isInitialized() {
    return initialized;
}

void TelegramBot::restart() {
    initialized = false;
    if (bot) {
        delete bot;
        bot = nullptr;
    }
}
