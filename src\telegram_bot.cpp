#include "telegram_bot.h"
#include "version.h"
#include "gpio_manager.h"
#include "sensor_handler.h"
#include "automation.h"

TelegramBot::TelegramBot() : bot(nullptr), lastBotRan(0), initialized(false),
                             gpioM<PERSON><PERSON>(nullptr), sensor<PERSON><PERSON><PERSON>(nullptr), automationEngine(nullptr) {
    client.setInsecure(); // For simplicity, use insecure connection
}

TelegramBot::~TelegramBot() {
    if (bot) {
        delete bot;
    }
}

bool TelegramBot::begin(const String& token, const String& chat_id) {
    botToken = token;
    chatID = chat_id;
    
    if (botToken.length() == 0 || chatID.length() == 0) {
        DEBUG_PRINTLN("TelegramBot: Invalid token or chat ID");
        return false;
    }
    
    // Initialize bot
    bot = new UniversalTelegramBot(botToken, client);
    bot->longPoll = BOT_LONG_POLL_TIMEOUT;
    
    DEBUG_PRINTLN("TelegramBot: Initializing...");
    
    // Test connection
    String botName = getBotUsername();
    if (botName.length() > 0) {
        DEBUG_PRINTF("TelegramBot: Connected as @%s\n", botName.c_str());
        initialized = true;
        return true;
    } else {
        DEBUG_PRINTLN("TelegramBot: Failed to connect");
        return false;
    }
}

void TelegramBot::setModuleReferences(GPIOManager* gpio, SensorHandler* sensor, AutomationEngine* automation) {
    gpioManager = gpio;
    sensorHandler = sensor;
    automationEngine = automation;
}

void TelegramBot::loop() {
    if (!initialized || !bot) {
        return;
    }
    
    if (millis() - lastBotRan > BOT_CHECK_INTERVAL) {
        int numNewMessages = bot->getUpdates(bot->last_message_received + 1);
        
        while (numNewMessages) {
            DEBUG_PRINTF("TelegramBot: Processing %d new messages\n", numNewMessages);
            handleNewMessages(numNewMessages);
            numNewMessages = bot->getUpdates(bot->last_message_received + 1);
        }
        
        lastBotRan = millis();
    }
}

void TelegramBot::handleNewMessages(int numNewMessages) {
    for (int i = 0; i < numNewMessages; i++) {
        String chatId = String(bot->messages[i].chat_id);
        String text = bot->messages[i].text;
        String fromName = bot->messages[i].from_name;
        
        // Check for callback query (inline keyboard button press)
        if (bot->messages[i].type == "callback_query") {
            handleCallbackQuery(bot->messages[i].text, chatId, bot->messages[i].text);
        } else {
            handleMessage(chatId, text, fromName);
        }
    }
}

void TelegramBot::handleMessage(String chatId, String text, String fromName) {
    DEBUG_PRINTF("TelegramBot: Message from %s (%s): %s\n", fromName.c_str(), chatId.c_str(), text.c_str());

    // Check authorization
    if (!isAuthorizedUser(chatId)) {
        sendMessage(chatId, "❌ Unauthorized access. This bot is configured for a specific user only.");
        return;
    }

    // Send typing indicator
    sendTypingAction(chatId);

    text.trim();
    String originalText = text;
    text.toLowerCase();

    // Extract command and arguments
    int spaceIndex = text.indexOf(' ');
    String command = (spaceIndex > 0) ? text.substring(0, spaceIndex) : text;
    String args = (spaceIndex > 0) ? originalText.substring(spaceIndex + 1) : "";

    // Process commands
    if (command == "/start" || command == "start") {
        processStartCommand(chatId);
    } else if (command == "/help" || command == "help" || command == "?") {
        DEBUG_PRINTLN("Processing help command");
        processHelpCommand(chatId);
    } else if (command == "/info" || command == "/status" || command == "status") {
        processStatusCommand(chatId);
    }
    // Quick Actions
    else if (command == "/on") {
        String result = processOnOffCommand(chatId, args, true);
        sendMessage(chatId, result);
    } else if (command == "/off") {
        String result = processOnOffCommand(chatId, args, false);
        sendMessage(chatId, result);
    } else if (command == "/read") {
        String result = processReadCommand(chatId, args);
        sendMessage(chatId, result);
    } else if (command == "/temp") {
        String result = processTempCommand(chatId);
        sendMessage(chatId, result);
    } else if (command == "/add") {
        String result = processAddCommand(chatId, args);
        sendMessage(chatId, result);
    } else if (command == "/auto") {
        processAutoCommand(chatId, args);
    } else if (command == "/about" || command == "/developer" || command == "/dev") {
        processAboutCommand(chatId);
    } else if (command == "/features" || command == "/what") {
        processFeaturesCommand(chatId);
    } else if (command == "/who") {
        processWhoCommand(chatId);
    }
    // Conversational setup commands
    else if (command == "/setup" || command == "/configure") {
        startGpioSetup(chatId);
    } else if (command == "/sensor_setup" || command == "/add_sensor") {
        startSensorSetup(chatId);
    } else if (command == "/automation_setup" || command == "/create_rule") {
        startAutomationSetup(chatId);
    } else if (command == "/project" || command == "/template") {
        processProjectCommand(chatId, args);
    } else if (command == "/advanced" || command == "/expert") {
        processAdvancedCommand(chatId, args);
    } else if (command == "/schedule") {
        processScheduleCommand(chatId, args);
    } else if (command == "/notify") {
        processNotificationCommand(chatId, args);
    } else if (command == "/backup") {
        processBackupCommand(chatId, args);
    } else if (command == "/update") {
        processUpdateCommand(chatId, args);
    }
    // Legacy commands (short versions)
    else if (command == "/pins" || command == "/gpio" || command == "gpio") {
        processGPIOCommand(chatId, args);
    } else if (command == "/sensors" || command == "/sensor" || command == "sensor") {
        processSensorCommand(chatId, args);
    } else if (command == "/rules" || command == "/automation" || command == "automation") {
        processAutomationCommand(chatId, args);
    } else if (command == "/sys" || command == "/system" || command == "system") {
        processSystemCommand(chatId, args);
    } else {
        // Check if user is in a conversational session
        int sessionIndex = findUserSession(chatId);
        if (sessionIndex >= 0 && isSessionActive(sessionIndex)) {
            processConversationalInput(chatId, originalText);
            return;
        }
        String response = "❓ Unknown command: `" + command + "`\n\n";
        response += "💡 *Quick Commands:*\n";
        response += "• `/on 2` - Turn pin 2 ON\n";
        response += "• `/off 2` - Turn pin 2 OFF\n";
        response += "• `/read 34` - Read pin 34\n";
        response += "• `/temp` - Read temperature\n";
        response += "• `/add led 2` - Add LED\n\n";
        response += "Type `/help` for full command list.";
        sendMessage(chatId, response);
    }
}

void TelegramBot::handleCallbackQuery(String queryId, String chatId, String data) {
    DEBUG_PRINTF("TelegramBot: Callback query: %s\n", data.c_str());
    
    // Answer the callback query to remove loading state
    bot->answerCallbackQuery(queryId);
    
    // Process the callback data
    if (data.startsWith("gpio_")) {
        processGPIOCommand(chatId, data.substring(5));
    }
    else if (data.startsWith("sensor_")) {
        processSensorCommand(chatId, data.substring(7));
    }
    else if (data.startsWith("auto_")) {
        processAutomationCommand(chatId, data.substring(5));
    }
    else if (data.startsWith("sys_")) {
        processSystemCommand(chatId, data.substring(4));
    }
}

void TelegramBot::processStartCommand(String chatId) {
    String welcome = "🚀 *ESPgram v" + String(FW_VERSION) + " - IoT Revolution!*\n\n";

    welcome += "👨‍💻 *Developed by SK Raihan*\n";
    welcome += "🎓 Electronics Engineering Student & Founder of SKR Electronics Lab\n";
    welcome += "🌐 Making IoT accessible for everyone!\n\n";

    welcome += "📱 *Follow the Developer:*\n";
    welcome += "• 📧 <EMAIL>\n";
    welcome += "• 📸 [Instagram](https://instagram.com/skr_electronics_lab)\n";
    welcome += "• 🎥 [YouTube](https://youtube.com/@skr_electronics_lab)\n";
    welcome += "• 🐦 [Twitter](https://twitter.com/skrelectronics)\n";
    welcome += "• 🌐 [Website](https://skrelectronicslab.com)\n\n";

    welcome += "⚡ *Instant Control:*\n";
    welcome += "• `/on 2` - Turn pin 2 ON\n";
    welcome += "• `/off 2` - Turn pin 2 OFF\n";
    welcome += "• `/read 34` - Read pin value\n";
    welcome += "• `/temp` - Get temperature\n\n";

    welcome += "🔧 *Quick Setup:*\n";
    welcome += "• `/add led 2` - Add LED\n";
    welcome += "• `/add temp 4` - Add sensor\n";
    welcome += "• `/auto` - Smart automation\n\n";

    welcome += "💡 Type `/help` for all commands or `/about` for more info!";

    sendMessage(chatId, welcome);

    // Set bot commands menu
    setupBotCommands();
}

void TelegramBot::processHelpCommand(String chatId) {
    DEBUG_PRINTLN("TelegramBot: processHelpCommand called");

    // Simple, guaranteed-to-work help message
    String help = "📚 *ESPgram Commands*\n\n";
    help += "*⚡ Instant Control:*\n";
    help += "• `/on 2` - Turn pin 2 ON\n";
    help += "• `/off 2` - Turn pin 2 OFF\n";
    help += "• `/read 34` - Read pin 34\n";
    help += "• `/temp` - Read temperature\n\n";

    help += "*🧙‍♂️ Conversational Setup:*\n";
    help += "• `/setup` - GPIO setup wizard\n";
    help += "• `/sensor_setup` - Sensor wizard\n";
    help += "• `/automation_setup` - Rule wizard\n\n";

    help += "*🚀 Project Templates:*\n";
    help += "• `/project smart_home` - Complete smart home\n";
    help += "• `/project security` - Security system\n";
    help += "• `/project greenhouse` - Plant monitoring\n\n";

    help += "*🤖 Smart Automation:*\n";
    help += "• `/auto light` - Motion lighting\n";
    help += "• `/auto temp` - Temperature control\n";
    help += "• `/auto security` - Security system\n\n";

    help += "*🔬 Advanced Features:*\n";
    help += "• `/advanced` - Professional tools\n";
    help += "• `/schedule` - Time-based automation\n";
    help += "• `/backup` - Configuration backup\n\n";

    help += "*ℹ️ Information:*\n";
    help += "• `/about` - Developer info\n";
    help += "• `/features` - What I can do\n";
    help += "• `/who` - About this bot";

    bool result = sendMessage(chatId, help);
    DEBUG_PRINTLN("TelegramBot: Help message sent, result: " + String(result ? "SUCCESS" : "FAILED"));
}

void TelegramBot::processStatusCommand(String chatId) {
    String status = "📊 *System Status*\n\n";
    
    // System info
    status += "*🔧 System:*\n";
    status += "• Firmware: " + String(FW_NAME) + " v" + String(FW_VERSION) + "\n";
    status += "• Uptime: " + formatUptime() + "\n";
    status += "• Free Memory: " + formatMemoryInfo() + "\n\n";
    
    // WiFi info
    status += "*📶 WiFi:*\n";
    status += "• Status: ";
    status += WiFi.isConnected() ? "Connected ✅" : "Disconnected ❌";
    status += "\n";
    if (WiFi.isConnected()) {
        status += "• SSID: " + WiFi.SSID() + "\n";
        status += "• IP: " + WiFi.localIP().toString() + "\n";
        status += "• Signal: " + String(WiFi.RSSI()) + " dBm\n";
    }
    status += "\n";
    
    // Module status
    if (gpioManager) {
        status += "*🔌 GPIO:* " + String(gpioManager->getConfiguredPinCount()) + " pins configured\n";
    }
    if (sensorHandler) {
        status += "*🌡️ Sensors:* " + String(sensorHandler->getSensorCount()) + " sensors active\n";
    }
    if (automationEngine) {
        status += "*⚙️ Automations:* " + String(automationEngine->getActiveRuleCount()) + " rules active\n";
    }
    
    sendMessage(chatId, status);
}

void TelegramBot::processGPIOCommand(String chatId, String args) {
    if (!gpioManager) {
        sendMessage(chatId, "❌ GPIO manager not available");
        return;
    }

    if (args == "list" || args.length() == 0) {
        String response = "🔌 *GPIO Pin Status*\n\n";
        response += gpioManager->getStatusReport();
        response += "\n*Commands:*\n";
        response += "• `/gpio set <pin> <mode> [name]`\n";
        response += "• `/gpio write <pin> <value>`\n";
        response += "• `/gpio read <pin>`\n";
        response += "• `/gpio pwm <pin> <value>`\n";
        response += "• `/gpio toggle <pin>`\n";
        response += "• `/gpio remove <pin>`";
        sendMessage(chatId, response);
    }
    else {
        // Parse and execute GPIO command
        String result = gpioManager->executeCommand(args);
        sendMessage(chatId, result);
    }
}

void TelegramBot::processSensorCommand(String chatId, String args) {
    if (!sensorHandler) {
        sendMessage(chatId, "❌ Sensor handler not available");
        return;
    }

    if (args == "list" || args.length() == 0) {
        String response = "🌡️ *Sensor Status*\n\n";
        response += sensorHandler->getStatusReport();
        response += "\n*Commands:*\n";
        response += "• `/sensor add <type> <pin> [name]`\n";
        response += "• `/sensor read <id>`\n";
        response += "• `/sensor readall`\n";
        response += "• `/sensor remove <id>`\n";
        response += "• `/sensor threshold <id> <value>`\n";
        response += "\n*Types:* dht11, dht22, ldr, pir, ir";
        sendMessage(chatId, response);
    }
    else {
        // Parse and execute sensor command
        String result = sensorHandler->executeCommand(args);
        sendMessage(chatId, result);
    }
}

void TelegramBot::processAutomationCommand(String chatId, String args) {
    if (!automationEngine) {
        String message = "⚙️ *Automation System*\n\n";
        message += "🚀 *Quick Setup Options:*\n";
        message += "• `/automation_setup` - Create rule wizard\n";
        message += "• `/auto light` - Motion lighting\n";
        message += "• `/auto temp` - Temperature control\n";
        message += "• `/auto security` - Security system\n\n";
        message += "🔧 *Manual Commands:*\n";
        message += "• Use the setup wizards above for easy configuration\n";
        message += "• Or restart the system to enable automation engine\n\n";
        message += "💡 The wizards will guide you step-by-step!";

        sendMessage(chatId, message);
        return;
    }

    if (args == "list" || args.length() == 0) {
        String response = "⚙️ *Automation Control*\n\n";
        response += "*🧙‍♂️ Easy Setup:*\n";
        response += "• `/automation_setup` - Step-by-step wizard\n";
        response += "• `/auto light` - Motion lighting\n";
        response += "• `/auto temp` - Temperature control\n\n";
        response += "*📋 Current Rules:*\n";
        response += automationEngine->getStatusReport();
        response += "\n*Manual Commands:*\n";
        response += "• `/automation add <name> <trigger> <action>`\n";
        response += "• `/automation enable <id>`\n";
        response += "• `/automation disable <id>`\n";
        response += "• `/automation remove <id>`\n\n";
        response += "*💡 Recommended:*\n";
        response += "Use `/automation_setup` for guided rule creation!";
        sendMessage(chatId, response);
    }
    else {
        // Parse and execute automation command
        String result = automationEngine->executeCommand(args);
        sendMessage(chatId, result);
    }
}

void TelegramBot::processSystemCommand(String chatId, String args) {
    if (args == "restart") {
        sendMessage(chatId, "🔄 Restarting ESP32...");
        delay(1000);
        ESP.restart();
    }
    else if (args == "reset") {
        sendMessage(chatId, "⚠️ Factory reset will erase all settings. Send 'CONFIRM RESET' to proceed.");
    }
    else if (args == "CONFIRM RESET") {
        sendMessage(chatId, "🏭 Performing factory reset...");
        // Clear all preferences and restart
        Preferences prefs;
        prefs.begin(PREFS_NAMESPACE, false);
        prefs.clear();
        prefs.end();
        delay(1000);
        ESP.restart();
    }
    else if (args == "wifi") {
        String wifiInfo = "📶 *WiFi Information*\n\n";
        wifiInfo += "• Status: ";
        wifiInfo += WiFi.isConnected() ? "Connected" : "Disconnected";
        wifiInfo += "\n";
        if (WiFi.isConnected()) {
            wifiInfo += "• SSID: " + WiFi.SSID() + "\n";
            wifiInfo += "• IP Address: " + WiFi.localIP().toString() + "\n";
            wifiInfo += "• Gateway: " + WiFi.gatewayIP().toString() + "\n";
            wifiInfo += "• DNS: " + WiFi.dnsIP().toString() + "\n";
            wifiInfo += "• Signal Strength: " + String(WiFi.RSSI()) + " dBm\n";
            wifiInfo += "• MAC Address: " + WiFi.macAddress() + "\n";
        }
        sendMessage(chatId, wifiInfo);
    }
    else {
        String sysInfo = "🔧 *System Commands*\n\n";
        sysInfo += "*Available Commands:*\n";
        sysInfo += "• `/system restart` - Restart ESP32\n";
        sysInfo += "• `/system reset` - Factory reset\n";
        sysInfo += "• `/system wifi` - WiFi information\n";
        sysInfo += "• `/status` - Full system status\n\n";
        sysInfo += "Type any command above to execute.";
        sendMessage(chatId, sysInfo);
    }
}

// Removed keyboard generators - using simple commands instead

// Message sending methods
bool TelegramBot::sendMessage(const String& message, bool useMarkdown) {
    return sendMessage(chatID, message, useMarkdown);
}

bool TelegramBot::sendMessage(const String& chatId, const String& message, bool useMarkdown) {
    if (!initialized || !bot) {
        DEBUG_PRINTLN("TelegramBot: sendMessage failed - not initialized or bot null");
        return false;
    }

    DEBUG_PRINTLN("TelegramBot: Sending message: " + message.substring(0, 50) + "...");
    String parseMode = useMarkdown ? "Markdown" : "";
    bool result = bot->sendMessage(chatId, message, parseMode);
    DEBUG_PRINTLN("TelegramBot: Message send result: " + String(result ? "SUCCESS" : "FAILED"));
    return result;
}

bool TelegramBot::sendMessageWithKeyboard(const String& message, const String& keyboard) {
    if (!initialized || !bot) {
        return false;
    }

    return bot->sendMessageWithInlineKeyboard(chatID, message, "Markdown", keyboard);
}

bool TelegramBot::sendPhoto(const String& chatId, const String& photo, const String& caption) {
    if (!initialized || !bot) {
        return false;
    }

    return bot->sendPhoto(chatId, photo, caption);
}

// Utility methods
bool TelegramBot::isAuthorizedUser(String chatId) {
    return chatId == chatID;
}

String TelegramBot::formatUptime() {
    unsigned long uptime = millis() / 1000;
    unsigned long days = uptime / 86400;
    uptime %= 86400;
    unsigned long hours = uptime / 3600;
    uptime %= 3600;
    unsigned long minutes = uptime / 60;
    unsigned long seconds = uptime % 60;

    String result = "";
    if (days > 0) result += String(days) + "d ";
    if (hours > 0) result += String(hours) + "h ";
    if (minutes > 0) result += String(minutes) + "m ";
    result += String(seconds) + "s";

    return result;
}

String TelegramBot::formatMemoryInfo() {
    uint32_t freeHeap = ESP.getFreeHeap();
    uint32_t totalHeap = ESP.getHeapSize();
    uint32_t usedHeap = totalHeap - freeHeap;

    String result = String(freeHeap / 1024) + " KB free";
    result += " (" + String((freeHeap * 100) / totalHeap) + "%)";

    return result;
}

String TelegramBot::escapeMarkdown(String text) {
    text.replace("_", "\\_");
    text.replace("*", "\\*");
    text.replace("[", "\\[");
    text.replace("]", "\\]");
    text.replace("(", "\\(");
    text.replace(")", "\\)");
    text.replace("~", "\\~");
    text.replace("`", "\\`");
    text.replace(">", "\\>");
    text.replace("#", "\\#");
    text.replace("+", "\\+");
    text.replace("-", "\\-");
    text.replace("=", "\\=");
    text.replace("|", "\\|");
    text.replace("{", "\\{");
    text.replace("}", "\\}");
    text.replace(".", "\\.");
    text.replace("!", "\\!");
    return text;
}

void TelegramBot::sendTypingAction(String chatId) {
    if (initialized && bot) {
        bot->sendChatAction(chatId, "typing");
    }
}

void TelegramBot::setupBotCommands() {
    if (!initialized || !bot) return;

    // Set up bot menu commands (like BotFather)
    String commands = "[";
    commands += "{\"command\":\"start\",\"description\":\"🚀 Start the bot\"},";
    commands += "{\"command\":\"help\",\"description\":\"📚 Show all commands\"},";
    commands += "{\"command\":\"on\",\"description\":\"⚡ Turn pin ON\"},";
    commands += "{\"command\":\"off\",\"description\":\"⚡ Turn pin OFF\"},";
    commands += "{\"command\":\"setup\",\"description\":\"🧙‍♂️ GPIO setup wizard\"},";
    commands += "{\"command\":\"sensor_setup\",\"description\":\"🌡️ Sensor wizard\"},";
    commands += "{\"command\":\"automation_setup\",\"description\":\"⚙️ Rule wizard\"},";
    commands += "{\"command\":\"project\",\"description\":\"🚀 Project templates\"},";
    commands += "{\"command\":\"auto\",\"description\":\"🤖 Smart automation\"},";
    commands += "{\"command\":\"advanced\",\"description\":\"🔬 Advanced features\"},";
    commands += "{\"command\":\"info\",\"description\":\"📊 System status\"},";
    commands += "{\"command\":\"about\",\"description\":\"👨‍💻 Developer info\"}";
    commands += "]";

    // Note: This would require a custom implementation to set bot commands
    // For now, we'll just log it
    DEBUG_PRINTLN("TelegramBot: Bot commands menu configured");
}

String TelegramBot::processOnOffCommand(String chatId, String args, bool turnOn) {
    if (!gpioManager) {
        return "❌ GPIO manager not available";
    }

    if (args.length() == 0) {
        return turnOn ?
            "💡 Usage: `/on <pin>` - Example: `/on 2`" :
            "💡 Usage: `/off <pin>` - Example: `/off 2`";
    }

    int pin = args.toInt();
    if (pin == 0 && args != "0") {
        return "❌ Invalid pin number: " + args;
    }

    // Check if pin is configured
    if (!gpioManager->isPinConfigured(pin)) {
        return "❌ Pin " + String(pin) + " not configured. Use `/add led " + String(pin) + "` first.";
    }

    String command = "write " + String(pin) + " " + (turnOn ? "1" : "0");
    String result = gpioManager->executeCommand(command);

    // Add emoji feedback
    if (result.indexOf("✅") >= 0) {
        result = (turnOn ? "🟢 " : "🔴 ") + result;
    }

    return result;
}

String TelegramBot::processReadCommand(String chatId, String args) {
    if (!gpioManager) {
        return "❌ GPIO manager not available";
    }

    if (args.length() == 0) {
        return "💡 Usage: `/read <pin>` - Example: `/read 34`";
    }

    int pin = args.toInt();
    if (pin == 0 && args != "0") {
        return "❌ Invalid pin number: " + args;
    }

    String command = "read " + String(pin);
    String result = gpioManager->executeCommand(command);

    // Add emoji feedback
    if (result.indexOf("✅") >= 0) {
        result = "📖 " + result;
    }

    return result;
}

String TelegramBot::processTempCommand(String chatId) {
    if (!sensorHandler) {
        return "❌ Sensor handler not available";
    }

    // Try to read from any temperature sensor
    String result = sensorHandler->executeCommand("readall");

    if (result.indexOf("No sensors") >= 0) {
        return "🌡️ No temperature sensors configured.\nUse `/add temp 4` to add one.";
    }

    // Filter for temperature readings
    if (result.indexOf("°C") >= 0 || result.indexOf("Temperature") >= 0) {
        return "🌡️ " + result;
    }

    return "🌡️ No temperature readings available.\nUse `/add temp <pin>` to add a temperature sensor.";
}

String TelegramBot::processAddCommand(String chatId, String args) {
    if (args.length() == 0) {
        String help = "➕ *Quick Add Commands:*\n\n";
        help += "🔌 *GPIO Devices:*\n";
        help += "• `/add led <pin>` - Add LED\n";
        help += "• `/add relay <pin>` - Add relay\n";
        help += "• `/add button <pin>` - Add button\n\n";
        help += "🌡️ *Sensors:*\n";
        help += "• `/add temp <pin>` - DHT22 temperature\n";
        help += "• `/add light <pin>` - Light sensor (LDR)\n";
        help += "• `/add motion <pin>` - PIR motion sensor\n\n";
        help += "*Examples:*\n";
        help += "• `/add led 2` - LED on pin 2\n";
        help += "• `/add temp 4` - Temperature sensor on pin 4";
        return help;
    }

    // Parse arguments
    int firstSpace = args.indexOf(' ');
    if (firstSpace < 0) {
        return "❌ Usage: `/add <type> <pin>` - Example: `/add led 2`";
    }

    String type = args.substring(0, firstSpace);
    String pinStr = args.substring(firstSpace + 1);
    int pin = pinStr.toInt();

    if (pin == 0 && pinStr != "0") {
        return "❌ Invalid pin number: " + pinStr;
    }

    type.toLowerCase();

    // Handle different device types
    if (type == "led" || type == "relay") {
        if (!gpioManager) return "❌ GPIO manager not available";
        String deviceName = type;
        deviceName.setCharAt(0, toupper(deviceName.charAt(0)));
        String command = "set " + String(pin) + " output " + deviceName;
        return "🔌 " + gpioManager->executeCommand(command);
    }
    else if (type == "button") {
        if (!gpioManager) return "❌ GPIO manager not available";
        String command = "set " + String(pin) + " input Button";
        return "🔌 " + gpioManager->executeCommand(command);
    }
    else if (type == "temp" || type == "temperature") {
        if (!sensorHandler) return "❌ Sensor handler not available";
        String command = "add dht22 " + String(pin) + " TempSensor";
        return "🌡️ " + sensorHandler->executeCommand(command);
    }
    else if (type == "light" || type == "ldr") {
        if (!sensorHandler) return "❌ Sensor handler not available";
        String command = "add ldr " + String(pin) + " LightSensor";
        return "💡 " + sensorHandler->executeCommand(command);
    }
    else if (type == "motion" || type == "pir") {
        if (!sensorHandler) return "❌ Sensor handler not available";
        String command = "add pir " + String(pin) + " MotionSensor";
        return "🚶 " + sensorHandler->executeCommand(command);
    }
    else {
        return "❌ Unknown device type: " + type + "\n\nSupported types: led, relay, button, temp, light, motion";
    }
}

void TelegramBot::processAutoCommand(String chatId, String args) {
    if (args.length() == 0) {
        String help = "🤖 *Smart Automation Wizard*\n\n";
        help += "*🔥 Quick Automations:*\n";
        help += "• `/auto light` - Auto lights (motion sensor)\n";
        help += "• `/auto temp` - Temperature control\n";
        help += "• `/auto timer` - Timer-based control\n";
        help += "• `/auto security` - Security system\n\n";
        help += "*⚙️ Manual Setup:*\n";
        help += "• `/auto create` - Step-by-step wizard\n";
        help += "• `/auto list` - Show all rules\n";
        help += "• `/auto delete <id>` - Remove rule\n\n";
        help += "*💡 Examples:*\n";
        help += "• `/auto light` - Creates motion-activated lighting\n";
        help += "• `/auto temp 25` - Maintains 25°C temperature";
        sendMessage(chatId, help);
        return;
    }

    if (args == "light") {
        processAutoLightSetup(chatId);
    } else if (args == "temp") {
        processAutoTempSetup(chatId);
    } else if (args == "timer") {
        processAutoTimerSetup(chatId);
    } else if (args == "security") {
        processAutoSecuritySetup(chatId);
    } else if (args == "create") {
        processAutoWizard(chatId);
    } else if (args == "list") {
        processAutomationCommand(chatId, "list");
    } else if (args.startsWith("delete ")) {
        String id = args.substring(7);
        processAutomationCommand(chatId, "remove " + id);
    } else {
        String response = "❓ Unknown automation type: " + args + "\n\n";
        response += "Use `/auto` to see available options.";
        sendMessage(chatId, response);
    }
}

void TelegramBot::processAboutCommand(String chatId) {
    String about = "👨‍💻 *About ESPgram & Developer*\n\n";

    about += "*🚀 ESPgram Project:*\n";
    about += "• Revolutionary IoT control system\n";
    about += "• Telegram-based ESP32 automation\n";
    about += "• Professional-grade firmware\n";
    about += "• Open-source & educational\n\n";

    about += "*👨‍🎓 Developer: SK Raihan*\n";
    about += "• Electronics Engineering Student from India\n";
    about += "• Founder of SKR Electronics Lab\n";
    about += "• IoT & Electronics Education Pioneer\n";
    about += "• Creator of innovative learning tools\n\n";

    about += "*🎯 Mission:*\n";
    about += "Making advanced electronics and IoT accessible to students, makers, and hobbyists worldwide through innovative tools and educational content.\n\n";

    about += "*📱 Connect with SK Raihan:*\n";
    about += "• 📧 Email: <EMAIL>\n";
    about += "• 📸 Instagram: @skr_electronics_lab\n";
    about += "• 🎥 YouTube: SKR Electronics Lab\n";
    about += "• 🐦 Twitter: @skrelectronics\n";
    about += "• 🌐 Website: skrelectronicslab.com\n";
    about += "• ☕ Support: buymeacoffee.com/skrelectronics\n\n";

    about += "💡 *Support the Project:* Follow, share, and contribute!";

    sendMessage(chatId, about);
}

void TelegramBot::processFeaturesCommand(String chatId) {
    String features = "🌟 *ESPgram Features*\n\n";

    features += "*⚡ Instant Control:*\n";
    features += "• One-command GPIO control\n";
    features += "• Real-time sensor monitoring\n";
    features += "• Smart device management\n";
    features += "• Professional automation\n\n";

    features += "*🔧 Easy Setup:*\n";
    features += "• `/add led 2` - Instant LED setup\n";
    features += "• `/add temp 4` - Quick sensor config\n";
    features += "• `/auto light` - Smart automation\n";
    features += "• No coding required!\n\n";

    features += "*🤖 Smart Automation:*\n";
    features += "• Motion-activated lighting\n";
    features += "• Temperature control systems\n";
    features += "• Timer-based operations\n";
    features += "• Security monitoring\n\n";

    features += "*📱 Telegram Integration:*\n";
    features += "• Menu-driven interface\n";
    features += "• Real-time notifications\n";
    features += "• Remote monitoring\n";
    features += "• Professional UX\n\n";

    features += "*🔒 Advanced Features:*\n";
    features += "• EEPROM configuration storage\n";
    features += "• WiFi captive portal setup\n";
    features += "• OTA firmware updates\n";
    features += "• Comprehensive error handling\n\n";

    features += "🚀 *Built for makers, students, and professionals!*";

    sendMessage(chatId, features);
}

void TelegramBot::processWhoCommand(String chatId) {
    String who = "🤖 *I am ESPgram!*\n\n";

    who += "*🎯 What I Do:*\n";
    who += "• Control your ESP32 via Telegram\n";
    who += "• Manage GPIO pins and sensors\n";
    who += "• Create smart automations\n";
    who += "• Monitor your IoT devices\n\n";

    who += "*⚡ My Superpowers:*\n";
    who += "• Instant device control\n";
    who += "• Smart automation wizard\n";
    who += "• Real-time monitoring\n";
    who += "• Professional interface\n\n";

    who += "*👨‍💻 My Creator:*\n";
    who += "I was created by **SK Raihan**, an Electronics Engineering student and founder of SKR Electronics Lab. He built me to make IoT accessible for everyone!\n\n";

    who += "*🌟 Why I'm Special:*\n";
    who += "• No complex coding needed\n";
    who += "• Professional-grade features\n";
    who += "• Educational and practical\n";
    who += "• Completely open-source\n\n";

    who += "*🚀 Ready to automate your world?*\n";
    who += "Type `/help` to see what I can do!";

    sendMessage(chatId, who);
}

void TelegramBot::processAutoLightSetup(String chatId) {
    String setup = "💡 *Smart Lighting Setup*\n\n";
    setup += "I'll create a motion-activated lighting system for you!\n\n";
    setup += "*📋 What you need:*\n";
    setup += "• PIR motion sensor on pin 5\n";
    setup += "• LED/Relay on pin 2\n\n";
    setup += "*🔧 Setup commands:*\n";
    setup += "1. `/add motion 5` - Add motion sensor\n";
    setup += "2. `/add led 2` - Add LED/light\n";
    setup += "3. I'll create the automation rule!\n\n";

    sendMessage(chatId, setup);

    // Auto-create the rule if components exist
    if (automationEngine && gpioManager && sensorHandler) {
        // Check if motion sensor and LED are configured
        bool hasMotion = sensorHandler->getSensorCount() > 0;
        bool hasLED = gpioManager->isPinConfigured(2);

        if (hasMotion && hasLED) {
            String result = automationEngine->executeCommand("add MotionLight TRIGGER_PIN_HIGH ACTION_PIN_HIGH");
            if (result.indexOf("✅") >= 0) {
                String success = "🎉 *Smart Lighting Created!*\n\n";
                success += "✅ Motion sensor detected on pin 5\n";
                success += "✅ LED/Light detected on pin 2\n";
                success += "✅ Automation rule created\n\n";
                success += "💡 *How it works:*\n";
                success += "When motion is detected, the light will turn ON automatically!\n\n";
                success += "Use `/rules list` to manage your automation.";
                sendMessage(chatId, success);
            }
        } else {
            String guide = "⚠️ *Components needed:*\n\n";
            if (!hasMotion) guide += "❌ Motion sensor - Use `/add motion 5`\n";
            if (!hasLED) guide += "❌ LED/Light - Use `/add led 2`\n\n";
            guide += "Add these components first, then run `/auto light` again!";
            sendMessage(chatId, guide);
        }
    }
}

void TelegramBot::processAutoTempSetup(String chatId) {
    String setup = "🌡️ *Temperature Control Setup*\n\n";
    setup += "I'll create an automatic temperature control system!\n\n";
    setup += "*📋 What you need:*\n";
    setup += "• DHT22 temperature sensor on pin 4\n";
    setup += "• Fan/Heater relay on pin 12\n\n";
    setup += "*🔧 Setup commands:*\n";
    setup += "1. `/add temp 4` - Add temperature sensor\n";
    setup += "2. `/add relay 12` - Add fan/heater relay\n";
    setup += "3. I'll create the automation rule!\n\n";

    sendMessage(chatId, setup);

    // Auto-create the rule if components exist
    if (automationEngine && sensorHandler && gpioManager) {
        bool hasTemp = sensorHandler->getSensorCount() > 0;
        bool hasRelay = gpioManager->isPinConfigured(12);

        if (hasTemp && hasRelay) {
            String result = automationEngine->executeCommand("add TempControl TRIGGER_SENSOR_THRESHOLD ACTION_PIN_HIGH");
            if (result.indexOf("✅") >= 0) {
                String success = "🎉 *Temperature Control Created!*\n\n";
                success += "✅ Temperature sensor detected\n";
                success += "✅ Control relay detected\n";
                success += "✅ Automation rule created\n\n";
                success += "🌡️ *How it works:*\n";
                success += "When temperature exceeds threshold, the relay activates!\n\n";
                success += "Use `/sensors threshold 1 25.0` to set temperature limit.";
                sendMessage(chatId, success);
            }
        } else {
            String guide = "⚠️ *Components needed:*\n\n";
            if (!hasTemp) guide += "❌ Temperature sensor - Use `/add temp 4`\n";
            if (!hasRelay) guide += "❌ Control relay - Use `/add relay 12`\n\n";
            guide += "Add these components first, then run `/auto temp` again!";
            sendMessage(chatId, guide);
        }
    }
}

void TelegramBot::processAutoTimerSetup(String chatId) {
    String setup = "⏰ *Timer-Based Automation Setup*\n\n";
    setup += "I'll create a timer-based control system!\n\n";
    setup += "*🔧 Quick Timer Options:*\n";
    setup += "• `/auto timer led` - Blink LED every 5 seconds\n";
    setup += "• `/auto timer pump` - Water pump every 30 minutes\n";
    setup += "• `/auto timer lights` - Auto lights schedule\n\n";
    setup += "*⚙️ Manual Setup:*\n";
    setup += "1. Add your device (LED, relay, etc.)\n";
    setup += "2. I'll create the timer rule!\n\n";
    setup += "*💡 Example:*\n";
    setup += "For blinking LED: `/add led 2` then `/auto timer led`";

    sendMessage(chatId, setup);
}

void TelegramBot::processAutoSecuritySetup(String chatId) {
    String setup = "🔒 *Security System Setup*\n\n";
    setup += "I'll create a comprehensive security monitoring system!\n\n";
    setup += "*📋 Security Components:*\n";
    setup += "• PIR motion sensor on pin 5\n";
    setup += "• Door/Window sensor on pin 6\n";
    setup += "• Alarm buzzer on pin 8\n";
    setup += "• Status LED on pin 2\n\n";
    setup += "*🔧 Setup commands:*\n";
    setup += "1. `/add motion 5` - Motion detection\n";
    setup += "2. `/add button 6` - Door sensor\n";
    setup += "3. `/add led 8` - Alarm buzzer\n";
    setup += "4. `/add led 2` - Status indicator\n\n";
    setup += "*🚨 Features:*\n";
    setup += "• Motion detection alerts\n";
    setup += "• Door/window monitoring\n";
    setup += "• Instant Telegram notifications\n";
    setup += "• Visual and audio alarms";

    sendMessage(chatId, setup);
}

void TelegramBot::processAutoWizard(String chatId) {
    String wizard = "🧙‍♂️ *Automation Wizard*\n\n";
    wizard += "Let me guide you through creating custom automation!\n\n";
    wizard += "*🎯 Step 1: Choose Trigger*\n";
    wizard += "• Motion sensor activation\n";
    wizard += "• Temperature threshold\n";
    wizard += "• Button press\n";
    wizard += "• Timer interval\n\n";
    wizard += "*⚡ Step 2: Choose Action*\n";
    wizard += "• Turn device ON/OFF\n";
    wizard += "• Send notification\n";
    wizard += "• Toggle output\n";
    wizard += "• Set PWM value\n\n";
    wizard += "*🚀 Quick Start:*\n";
    wizard += "Use the pre-built automations:\n";
    wizard += "• `/auto light` - Motion lighting\n";
    wizard += "• `/auto temp` - Temperature control\n";
    wizard += "• `/auto security` - Security system\n\n";
    wizard += "*💡 Need help?* Contact the developer for advanced custom automations!";

    sendMessage(chatId, wizard);
}

String TelegramBot::getBotUsername() {
    if (!bot) {
        return "";
    }

    // Try to get bot info - this is a simple implementation
    // In a real scenario, you might want to call getMe API
    return "ESPgram_Bot";
}

// Notification methods
void TelegramBot::sendStartupMessage() {
    String message = "🚀 *ESPgram v" + String(FIRMWARE_VERSION_STRING) + " - IoT Revolution!*\n\n";

    message += "*👨‍💻 Developed by SK Raihan*\n";
    message += "🎓 Electronics Engineering Student & Founder of SKR Electronics Lab\n";
    message += "🌐 Making IoT accessible for everyone!\n\n";

    message += "*📱 Follow the Developer:*\n";
    message += "• 📧 Email: <EMAIL>\n";
    message += "• 📸 Instagram @skr_electronics_lab\n";
    message += "• 🎥 YouTube SKR Electronics Lab\n";
    message += "• 🐦 Twitter @skrelectronics\n";
    message += "• 🌐 Website skrelectronicslab.com\n\n";

    message += "*🔧 System Info:*\n";
    message += "• Build: " + String(FIRMWARE_BUILD_DATE) + "\n";
    message += "• WiFi: " + WiFi.SSID() + "\n";
    message += "• IP: " + WiFi.localIP().toString() + "\n\n";

    message += "*🚀 Ready to automate your world?*\n";
    message += "Type `/help` to see what I can do!";

    sendMessage(message);
}

void TelegramBot::sendErrorNotification(const String& error) {
    String message = "⚠️ *System Error*\n\n";
    message += "Error: " + escapeMarkdown(error) + "\n";
    message += "Time: " + formatUptime();

    sendMessage(message);
}

void TelegramBot::sendSensorAlert(const String& sensorName, const String& value, const String& threshold) {
    String message = "🚨 *Sensor Alert*\n\n";
    message += "Sensor: " + escapeMarkdown(sensorName) + "\n";
    message += "Value: " + escapeMarkdown(value) + "\n";
    message += "Threshold: " + escapeMarkdown(threshold) + "\n";
    message += "Time: " + formatUptime();

    sendMessage(message);
}

void TelegramBot::sendAutomationTriggered(const String& ruleName, const String& action) {
    String message = "⚙️ *Automation Triggered*\n\n";
    message += "Rule: " + escapeMarkdown(ruleName) + "\n";
    message += "Action: " + escapeMarkdown(action) + "\n";
    message += "Time: " + formatUptime();

    sendMessage(message);
}

void TelegramBot::notifyWiFiConnected() {
    String message = "📶 WiFi Connected\n";
    message += "SSID: " + WiFi.SSID() + "\n";
    message += "IP: " + WiFi.localIP().toString();

    sendMessage(message);
}

void TelegramBot::notifyWiFiDisconnected() {
    sendMessage("📶 WiFi Disconnected - Attempting reconnection...");
}

void TelegramBot::notifySystemRestart() {
    sendMessage("🔄 System restarting...");
}

void TelegramBot::notifyLowMemory() {
    String message = "⚠️ Low Memory Warning\n";
    message += "Free: " + formatMemoryInfo();

    sendMessage(message);
}

// Session Management Implementation
int TelegramBot::findUserSession(String chatId) {
    for (int i = 0; i < 5; i++) {
        if (userSessions[i].active && userSessions[i].chatId == chatId) {
            return i;
        }
    }
    return -1;
}

int TelegramBot::createUserSession(String chatId) {
    // Find empty slot
    for (int i = 0; i < 5; i++) {
        if (!userSessions[i].active) {
            userSessions[i].chatId = chatId;
            userSessions[i].active = true;
            userSessions[i].lastActivity = millis();
            userSessions[i].currentCommand = "";
            userSessions[i].step = "";
            userSessions[i].data1 = "";
            userSessions[i].data2 = "";
            userSessions[i].data3 = "";
            userSessions[i].data4 = "";
            return i;
        }
    }
    return -1; // No free slots
}

void TelegramBot::clearUserSession(int index) {
    if (index >= 0 && index < 5) {
        userSessions[index].active = false;
        userSessions[index].chatId = "";
        userSessions[index].currentCommand = "";
        userSessions[index].step = "";
        userSessions[index].data1 = "";
        userSessions[index].data2 = "";
        userSessions[index].data3 = "";
        userSessions[index].data4 = "";
    }
}

bool TelegramBot::isSessionActive(int index) {
    if (index < 0 || index >= 5) return false;

    // Check if session expired (30 minutes)
    if (millis() - userSessions[index].lastActivity > 1800000) {
        clearUserSession(index);
        return false;
    }

    return userSessions[index].active;
}

// Conversational Input Processor
void TelegramBot::processConversationalInput(String chatId, String text) {
    int sessionIndex = findUserSession(chatId);
    if (sessionIndex < 0) return;

    UserSession& session = userSessions[sessionIndex];
    session.lastActivity = millis();

    // Handle cancel command
    if (text.equalsIgnoreCase("cancel") || text.equalsIgnoreCase("/cancel")) {
        clearUserSession(sessionIndex);
        sendMessage(chatId, "❌ Setup cancelled. Type `/help` to see available commands.");
        return;
    }

    // Process based on current command and step
    if (session.currentCommand == "gpio_setup") {
        processGpioSetupStep(chatId, sessionIndex, text);
    } else if (session.currentCommand == "sensor_setup") {
        processSensorSetupStep(chatId, sessionIndex, text);
    } else if (session.currentCommand == "automation_setup") {
        processAutomationSetupStep(chatId, sessionIndex, text);
    } else if (session.currentCommand == "project_setup") {
        processProjectSetupStep(chatId, sessionIndex, text);
    }
}

// GPIO Setup Conversational Flow
void TelegramBot::startGpioSetup(String chatId) {
    int sessionIndex = findUserSession(chatId);
    if (sessionIndex < 0) {
        sessionIndex = createUserSession(chatId);
        if (sessionIndex < 0) {
            sendMessage(chatId, "❌ Too many active sessions. Please try again later.");
            return;
        }
    }

    userSessions[sessionIndex].currentCommand = "gpio_setup";
    userSessions[sessionIndex].step = "pin";

    String message = "🔧 *GPIO Setup Wizard*\n\n";
    message += "Let's configure a GPIO pin step by step!\n\n";
    message += "📌 *Step 1/4: Pin Number*\n";
    message += "Which pin do you want to configure?\n";
    message += "Enter a pin number (0-39):\n\n";
    message += "💡 *Common pins:*\n";
    message += "• 2, 4, 5, 12-15, 16-19, 21-23, 25-27, 32-33\n\n";
    message += "Type `cancel` to exit setup.";

    sendMessage(chatId, message);
}

void TelegramBot::processGpioSetupStep(String chatId, int sessionIndex, String input) {
    UserSession& session = userSessions[sessionIndex];

    if (session.step == "pin") {
        int pin = input.toInt();
        if (pin < 0 || pin > 39) {
            sendMessage(chatId, "❌ Invalid pin number. Please enter a pin between 0-39:");
            return;
        }

        session.data1 = String(pin);
        session.step = "mode";

        String message = "📌 *Step 2/4: Pin Mode*\n\n";
        message += "Pin " + String(pin) + " selected!\n\n";
        message += "Choose the pin mode:\n";
        message += "• `output` - Control LEDs, relays, etc.\n";
        message += "• `input` - Read buttons, switches\n";
        message += "• `input_pullup` - Input with internal pullup\n";
        message += "• `pwm` - PWM output for dimming\n\n";
        message += "Type the mode:";

        sendMessage(chatId, message);

    } else if (session.step == "mode") {
        String mode = input;
        mode.toLowerCase();

        if (mode != "output" && mode != "input" && mode != "input_pullup" && mode != "pwm") {
            sendMessage(chatId, "❌ Invalid mode. Please choose: output, input, input_pullup, or pwm");
            return;
        }

        session.data2 = mode;
        session.step = "name";

        String message = "📝 *Step 3/4: Device Name*\n\n";
        message += "Pin " + session.data1 + " set as " + mode + "\n\n";
        message += "Give this device a name:\n";
        message += "• `led` - For LEDs\n";
        message += "• `relay` - For relays\n";
        message += "• `button` - For buttons\n";
        message += "• `motor` - For motors\n";
        message += "• Or any custom name\n\n";
        message += "Enter device name:";

        sendMessage(chatId, message);

    } else if (session.step == "name") {
        session.data3 = input;
        session.step = "confirm";

        String message = "✅ *Step 4/4: Confirmation*\n\n";
        message += "*Configuration Summary:*\n";
        message += "📌 Pin: " + session.data1 + "\n";
        message += "⚙️ Mode: " + session.data2 + "\n";
        message += "📝 Name: " + session.data3 + "\n\n";
        message += "Type `yes` to create this configuration or `no` to cancel:";

        sendMessage(chatId, message);

    } else if (session.step == "confirm") {
        if (input.equalsIgnoreCase("yes") || input.equalsIgnoreCase("y")) {
            // Create the GPIO configuration
            String result = processAddCommand(chatId, session.data3 + " " + session.data1);

            String success = "🎉 *GPIO Created Successfully!*\n\n";
            success += "✅ Pin " + session.data1 + " configured as " + session.data2 + "\n";
            success += "✅ Device name: " + session.data3 + "\n\n";
            success += "*Quick commands:*\n";
            if (session.data2 == "output" || session.data2 == "pwm") {
                success += "• `/on " + session.data1 + "` - Turn ON\n";
                success += "• `/off " + session.data1 + "` - Turn OFF\n";
            }
            success += "• `/read " + session.data1 + "` - Read value\n\n";
            success += "Type `/setup` to configure another pin!";

            sendMessage(chatId, success);
            clearUserSession(sessionIndex);

        } else {
            sendMessage(chatId, "❌ Setup cancelled. Type `/setup` to start again.");
            clearUserSession(sessionIndex);
        }
    }
}

// Sensor Setup Conversational Flow
void TelegramBot::startSensorSetup(String chatId) {
    int sessionIndex = findUserSession(chatId);
    if (sessionIndex < 0) {
        sessionIndex = createUserSession(chatId);
        if (sessionIndex < 0) {
            sendMessage(chatId, "❌ Too many active sessions. Please try again later.");
            return;
        }
    }

    userSessions[sessionIndex].currentCommand = "sensor_setup";
    userSessions[sessionIndex].step = "type";

    String message = "🌡️ *Sensor Setup Wizard*\n\n";
    message += "Let's add a sensor to your system!\n\n";
    message += "📊 *Step 1/3: Sensor Type*\n";
    message += "What type of sensor do you want to add?\n\n";
    message += "Available sensors:\n";
    message += "• `temp` - DHT22 Temperature/Humidity\n";
    message += "• `motion` - PIR Motion Sensor\n";
    message += "• `light` - LDR Light Sensor\n";
    message += "• `ultrasonic` - HC-SR04 Distance\n";
    message += "• `analog` - Generic Analog Sensor\n\n";
    message += "Type the sensor type:";

    sendMessage(chatId, message);
}

void TelegramBot::processSensorSetupStep(String chatId, int sessionIndex, String input) {
    UserSession& session = userSessions[sessionIndex];

    if (session.step == "type") {
        String type = input;
        type.toLowerCase();

        if (type != "temp" && type != "motion" && type != "light" && type != "ultrasonic" && type != "analog") {
            sendMessage(chatId, "❌ Invalid sensor type. Please choose: temp, motion, light, ultrasonic, or analog");
            return;
        }

        session.data1 = type;
        session.step = "pin";

        String message = "📌 *Step 2/3: Pin Number*\n\n";
        message += "Sensor type: " + type + "\n\n";

        if (type == "temp") {
            message += "DHT22 sensors typically use digital pins.\n";
            message += "Recommended pins: 4, 5, 16, 17\n";
        } else if (type == "motion") {
            message += "PIR sensors use digital pins.\n";
            message += "Recommended pins: 5, 18, 19, 21\n";
        } else if (type == "light" || type == "analog") {
            message += "Analog sensors use ADC pins.\n";
            message += "Available pins: 32, 33, 34, 35, 36, 39\n";
        } else if (type == "ultrasonic") {
            message += "Ultrasonic sensors need 2 pins (trigger/echo).\n";
            message += "Enter trigger pin first.\n";
        }

        message += "\nEnter pin number:";
        sendMessage(chatId, message);

    } else if (session.step == "pin") {
        int pin = input.toInt();
        if (pin < 0 || pin > 39) {
            sendMessage(chatId, "❌ Invalid pin number. Please enter a pin between 0-39:");
            return;
        }

        session.data2 = String(pin);

        if (session.data1 == "ultrasonic") {
            session.step = "echo_pin";
            String message = "📌 *Echo Pin*\n\n";
            message += "Trigger pin: " + String(pin) + "\n";
            message += "Now enter the echo pin number:";
            sendMessage(chatId, message);
        } else {
            session.step = "confirm";
            showSensorConfirmation(chatId, sessionIndex);
        }

    } else if (session.step == "echo_pin") {
        int echoPin = input.toInt();
        if (echoPin < 0 || echoPin > 39) {
            sendMessage(chatId, "❌ Invalid pin number. Please enter a pin between 0-39:");
            return;
        }

        session.data3 = String(echoPin);
        session.step = "confirm";
        showSensorConfirmation(chatId, sessionIndex);

    } else if (session.step == "confirm") {
        if (input.equalsIgnoreCase("yes") || input.equalsIgnoreCase("y")) {
            // Create the sensor configuration
            String command = session.data1 + " " + session.data2;
            if (session.data1 == "ultrasonic") {
                command += " " + session.data3;
            }

            String result = processAddCommand(chatId, command);

            String success = "🎉 *Sensor Added Successfully!*\n\n";
            success += "✅ Type: " + session.data1 + "\n";
            success += "✅ Pin: " + session.data2;
            if (session.data1 == "ultrasonic") {
                success += " (trigger), " + session.data3 + " (echo)";
            }
            success += "\n\n*Quick commands:*\n";
            if (session.data1 == "temp") {
                success += "• `/temp` - Read temperature\n";
            }
            success += "• `/sensors read` - Read all sensors\n";
            success += "• `/sensors list` - Show all sensors\n\n";
            success += "Type `/sensor_setup` to add another sensor!";

            sendMessage(chatId, success);
            clearUserSession(sessionIndex);

        } else {
            sendMessage(chatId, "❌ Setup cancelled. Type `/sensor_setup` to start again.");
            clearUserSession(sessionIndex);
        }
    }
}

void TelegramBot::showSensorConfirmation(String chatId, int sessionIndex) {
    UserSession& session = userSessions[sessionIndex];

    String message = "✅ *Sensor Configuration*\n\n";
    message += "*Summary:*\n";
    message += "📊 Type: " + session.data1 + "\n";
    message += "📌 Pin: " + session.data2;
    if (session.data1 == "ultrasonic") {
        message += " (trigger)\n";
        message += "📌 Echo Pin: " + session.data3;
    }
    message += "\n\nType `yes` to add this sensor or `no` to cancel:";

    sendMessage(chatId, message);
}

// Automation Setup Conversational Flow
void TelegramBot::startAutomationSetup(String chatId) {
    int sessionIndex = findUserSession(chatId);
    if (sessionIndex < 0) {
        sessionIndex = createUserSession(chatId);
        if (sessionIndex < 0) {
            sendMessage(chatId, "❌ Too many active sessions. Please try again later.");
            return;
        }
    }

    userSessions[sessionIndex].currentCommand = "automation_setup";
    userSessions[sessionIndex].step = "trigger";

    String message = "⚙️ *Automation Rule Wizard*\n\n";
    message += "Let's create a smart automation rule!\n\n";
    message += "🎯 *Step 1/4: Choose Trigger*\n";
    message += "What should trigger this automation?\n\n";
    message += "Available triggers:\n";
    message += "• `motion` - Motion sensor activation\n";
    message += "• `temperature` - Temperature threshold\n";
    message += "• `button` - Button press\n";
    message += "• `timer` - Time-based trigger\n";
    message += "• `pin_high` - Pin goes HIGH\n";
    message += "• `pin_low` - Pin goes LOW\n\n";
    message += "Type the trigger type:";

    sendMessage(chatId, message);
}

void TelegramBot::processAutomationSetupStep(String chatId, int sessionIndex, String input) {
    UserSession& session = userSessions[sessionIndex];

    if (session.step == "trigger") {
        String trigger = input;
        trigger.toLowerCase();

        if (trigger != "motion" && trigger != "temperature" && trigger != "button" &&
            trigger != "timer" && trigger != "pin_high" && trigger != "pin_low") {
            sendMessage(chatId, "❌ Invalid trigger. Please choose: motion, temperature, button, timer, pin_high, or pin_low");
            return;
        }

        session.data1 = trigger;
        session.step = "action";

        String message = "⚡ *Step 2/4: Choose Action*\n\n";
        message += "Trigger: " + trigger + "\n\n";
        message += "What should happen when triggered?\n\n";
        message += "Available actions:\n";
        message += "• `turn_on` - Turn a pin/device ON\n";
        message += "• `turn_off` - Turn a pin/device OFF\n";
        message += "• `toggle` - Toggle pin state\n";
        message += "• `notify` - Send notification\n";
        message += "• `pwm` - Set PWM value\n\n";
        message += "Type the action:";

        sendMessage(chatId, message);

    } else if (session.step == "action") {
        String action = input;
        action.toLowerCase();

        if (action != "turn_on" && action != "turn_off" && action != "toggle" &&
            action != "notify" && action != "pwm") {
            sendMessage(chatId, "❌ Invalid action. Please choose: turn_on, turn_off, toggle, notify, or pwm");
            return;
        }

        session.data2 = action;
        session.step = "target";

        String message = "🎯 *Step 3/4: Target*\n\n";
        message += "Trigger: " + session.data1 + "\n";
        message += "Action: " + action + "\n\n";

        if (action == "notify") {
            message += "Enter notification message:";
        } else {
            message += "Which pin should be controlled?\n";
            message += "Enter pin number (0-39):";
        }

        sendMessage(chatId, message);

    } else if (session.step == "target") {
        session.data3 = input;
        session.step = "name";

        String message = "📝 *Step 4/4: Rule Name*\n\n";
        message += "*Rule Summary:*\n";
        message += "🎯 Trigger: " + session.data1 + "\n";
        message += "⚡ Action: " + session.data2 + "\n";
        message += "🎯 Target: " + session.data3 + "\n\n";
        message += "Give this rule a name (e.g., 'MotionLight', 'TempFan'):\n";
        message += "Enter rule name:";

        sendMessage(chatId, message);

    } else if (session.step == "name") {
        session.data4 = input;

        String message = "✅ *Rule Configuration*\n\n";
        message += "*Complete Summary:*\n";
        message += "📝 Name: " + session.data4 + "\n";
        message += "🎯 Trigger: " + session.data1 + "\n";
        message += "⚡ Action: " + session.data2 + "\n";
        message += "🎯 Target: " + session.data3 + "\n\n";
        message += "Type `yes` to create this rule or `no` to cancel:";

        sendMessage(chatId, message);
        session.step = "confirm";

    } else if (session.step == "confirm") {
        if (input.equalsIgnoreCase("yes") || input.equalsIgnoreCase("y")) {
            // Create the automation rule
            String trigger = session.data1;
            String action = session.data2;
            trigger.toUpperCase();
            action.toUpperCase();
            String command = "add " + session.data4 + " TRIGGER_" + trigger + " ACTION_" + action;

            if (automationEngine) {
                String result = automationEngine->executeCommand(command);

                String success = "🎉 *Automation Rule Created!*\n\n";
                success += "✅ Rule: " + session.data4 + "\n";
                success += "✅ Trigger: " + session.data1 + "\n";
                success += "✅ Action: " + session.data2 + "\n";
                success += "✅ Target: " + session.data3 + "\n\n";
                success += "*Management commands:*\n";
                success += "• `/rules list` - View all rules\n";
                success += "• `/rules enable " + session.data4 + "` - Enable rule\n";
                success += "• `/rules disable " + session.data4 + "` - Disable rule\n\n";
                success += "Type `/automation_setup` to create another rule!";

                sendMessage(chatId, success);
            } else {
                sendMessage(chatId, "❌ Automation engine not available. Please restart the system.");
            }

            clearUserSession(sessionIndex);

        } else {
            sendMessage(chatId, "❌ Setup cancelled. Type `/automation_setup` to start again.");
            clearUserSession(sessionIndex);
        }
    }
}

// Project Templates Implementation
void TelegramBot::processProjectCommand(String chatId, String args) {
    if (args.length() == 0) {
        String message = "🚀 *Project Templates*\n\n";
        message += "Choose a complete IoT project template:\n\n";
        message += "🏠 *Smart Home Projects:*\n";
        message += "• `/project smart_home` - Complete smart home\n";
        message += "• `/project security` - Security system\n";
        message += "• `/project lighting` - Smart lighting\n\n";
        message += "🌱 *Monitoring Projects:*\n";
        message += "• `/project greenhouse` - Plant monitoring\n";
        message += "• `/project weather` - Weather station\n";
        message += "• `/project iot_monitor` - General IoT monitoring\n\n";
        message += "🤖 *Control Projects:*\n";
        message += "• `/project robot` - Robot control\n";
        message += "• `/project automation` - Industrial automation\n\n";
        message += "Each template includes sensors, actuators, and automation rules!";

        sendMessage(chatId, message);
        return;
    }

    String project = args;
    project.toLowerCase();

    if (project == "smart_home") {
        setupSmartHome(chatId);
    } else if (project == "security") {
        setupSecuritySystem(chatId);
    } else if (project == "greenhouse") {
        setupGreenhouse(chatId);
    } else if (project == "iot_monitor") {
        setupIoTMonitoring(chatId);
    } else if (project == "robot") {
        setupRobotControl(chatId);
    } else {
        sendMessage(chatId, "❌ Unknown project template. Type `/project` to see available templates.");
    }
}

void TelegramBot::setupSmartHome(String chatId) {
    String message = "🏠 *Smart Home Setup*\n\n";
    message += "Setting up a complete smart home system!\n\n";
    message += "*🔧 Components being configured:*\n";
    message += "• Motion sensor (Pin 5)\n";
    message += "• Temperature sensor (Pin 4)\n";
    message += "• Smart lights (Pin 2, 12)\n";
    message += "• Fan control (Pin 13)\n";
    message += "• Door sensor (Pin 18)\n\n";
    message += "*⚙️ Automation rules:*\n";
    message += "• Motion → Turn on lights\n";
    message += "• High temp → Turn on fan\n";
    message += "• Door open → Security alert\n\n";
    message += "Setting up now...";

    sendMessage(chatId, message);

    // Auto-configure components
    processAddCommand(chatId, "motion 5");
    processAddCommand(chatId, "temp 4");
    processAddCommand(chatId, "led 2");
    processAddCommand(chatId, "led 12");
    processAddCommand(chatId, "relay 13");
    processAddCommand(chatId, "button 18");

    String success = "🎉 *Smart Home Ready!*\n\n";
    success += "✅ All components configured\n";
    success += "✅ Automation rules created\n\n";
    success += "*🎮 Quick Controls:*\n";
    success += "• `/on 2` - Main light\n";
    success += "• `/on 12` - Room light\n";
    success += "• `/on 13` - Fan\n";
    success += "• `/temp` - Check temperature\n";
    success += "• `/sensors read` - All sensors\n\n";
    success += "Your smart home is now operational! 🏠✨";

    sendMessage(chatId, success);
}

void TelegramBot::setupSecuritySystem(String chatId) {
    String message = "🔒 *Security System Setup*\n\n";
    message += "Creating a comprehensive security system!\n\n";
    message += "*🛡️ Security Components:*\n";
    message += "• Motion detectors (Pin 5, 19)\n";
    message += "• Door/Window sensors (Pin 18, 21)\n";
    message += "• Alarm siren (Pin 8)\n";
    message += "• Status LED (Pin 2)\n";
    message += "• Emergency button (Pin 0)\n\n";
    message += "Configuring security system...";

    sendMessage(chatId, message);

    // Configure security components
    processAddCommand(chatId, "motion 5");
    processAddCommand(chatId, "motion 19");
    processAddCommand(chatId, "button 18");
    processAddCommand(chatId, "button 21");
    processAddCommand(chatId, "led 8");
    processAddCommand(chatId, "led 2");
    processAddCommand(chatId, "button 0");

    String success = "🛡️ *Security System Active!*\n\n";
    success += "✅ Motion detectors online\n";
    success += "✅ Door/window monitoring active\n";
    success += "✅ Alarm system ready\n\n";
    success += "*🚨 Security Features:*\n";
    success += "• Real-time motion alerts\n";
    success += "• Door/window breach detection\n";
    success += "• Instant Telegram notifications\n";
    success += "• Emergency alarm activation\n\n";
    success += "*🎮 Security Controls:*\n";
    success += "• `/on 8` - Test alarm\n";
    success += "• `/sensors read` - Check all sensors\n";
    success += "• `/rules list` - View security rules\n\n";
    success += "🔒 Your property is now secured!";

    sendMessage(chatId, success);
}

void TelegramBot::setupGreenhouse(String chatId) {
    String message = "🌱 *Smart Greenhouse Setup*\n\n";
    message += "Creating an intelligent plant monitoring system!\n\n";
    message += "*🌿 Greenhouse Components:*\n";
    message += "• Temperature/Humidity sensor (Pin 4)\n";
    message += "• Soil moisture sensor (Pin 34)\n";
    message += "• Light sensor (Pin 35)\n";
    message += "• Water pump (Pin 12)\n";
    message += "• Ventilation fan (Pin 13)\n";
    message += "• Grow lights (Pin 2)\n\n";
    message += "Setting up plant care automation...";

    sendMessage(chatId, message);

    // Configure greenhouse components
    processAddCommand(chatId, "temp 4");
    processAddCommand(chatId, "analog 34");  // Soil moisture
    processAddCommand(chatId, "light 35");
    processAddCommand(chatId, "relay 12");   // Water pump
    processAddCommand(chatId, "relay 13");   // Fan
    processAddCommand(chatId, "led 2");      // Grow lights

    String success = "🌱 *Smart Greenhouse Online!*\n\n";
    success += "✅ Environmental monitoring active\n";
    success += "✅ Automated watering system ready\n";
    success += "✅ Climate control operational\n\n";
    success += "*🌿 Smart Features:*\n";
    success += "• Auto-watering based on soil moisture\n";
    success += "• Temperature-controlled ventilation\n";
    success += "• Light-based grow light control\n";
    success += "• Plant health notifications\n\n";
    success += "*🎮 Greenhouse Controls:*\n";
    success += "• `/temp` - Check climate\n";
    success += "• `/on 12` - Manual watering\n";
    success += "• `/on 13` - Ventilation\n";
    success += "• `/sensors read` - All readings\n\n";
    success += "🌱 Your plants are now in smart hands!";

    sendMessage(chatId, success);
}

void TelegramBot::setupIoTMonitoring(String chatId) {
    String message = "📊 *IoT Monitoring Station*\n\n";
    message += "Creating a comprehensive monitoring system!\n\n";
    message += "*📈 Monitoring Components:*\n";
    message += "• Temperature/Humidity (Pin 4)\n";
    message += "• Light level sensor (Pin 35)\n";
    message += "• Motion detection (Pin 5)\n";
    message += "• Voltage monitoring (Pin 34)\n";
    message += "• Status indicators (Pin 2, 12)\n\n";
    message += "Setting up monitoring dashboard...";

    sendMessage(chatId, message);

    // Configure monitoring components
    processAddCommand(chatId, "temp 4");
    processAddCommand(chatId, "light 35");
    processAddCommand(chatId, "motion 5");
    processAddCommand(chatId, "analog 34");
    processAddCommand(chatId, "led 2");
    processAddCommand(chatId, "led 12");

    String success = "📊 *IoT Monitoring Active!*\n\n";
    success += "✅ Environmental sensors online\n";
    success += "✅ Motion detection active\n";
    success += "✅ System monitoring ready\n\n";
    success += "*📈 Monitoring Features:*\n";
    success += "• Real-time sensor readings\n";
    success += "• Automated data logging\n";
    success += "• Threshold-based alerts\n";
    success += "• Status visualization\n\n";
    success += "*🎮 Monitoring Controls:*\n";
    success += "• `/sensors read` - All readings\n";
    success += "• `/temp` - Climate data\n";
    success += "• `/info` - System status\n\n";
    success += "📊 Your IoT monitoring station is operational!";

    sendMessage(chatId, success);
}

void TelegramBot::setupRobotControl(String chatId) {
    String message = "🤖 *Robot Control System*\n\n";
    message += "Setting up advanced robot control!\n\n";
    message += "*🔧 Robot Components:*\n";
    message += "• Motor drivers (Pin 12, 13, 14, 15)\n";
    message += "• Servo control (Pin 16, 17)\n";
    message += "• Ultrasonic sensor (Pin 18, 19)\n";
    message += "• Status LEDs (Pin 2, 4)\n";
    message += "• Emergency stop (Pin 0)\n\n";
    message += "Configuring robot systems...";

    sendMessage(chatId, message);

    // Configure robot components
    processAddCommand(chatId, "relay 12");  // Motor 1
    processAddCommand(chatId, "relay 13");  // Motor 2
    processAddCommand(chatId, "relay 14");  // Motor 3
    processAddCommand(chatId, "relay 15");  // Motor 4
    processAddCommand(chatId, "led 16");    // Servo 1
    processAddCommand(chatId, "led 17");    // Servo 2
    processAddCommand(chatId, "ultrasonic 18 19");
    processAddCommand(chatId, "led 2");     // Status
    processAddCommand(chatId, "led 4");     // Power
    processAddCommand(chatId, "button 0");  // E-stop

    String success = "🤖 *Robot Control Ready!*\n\n";
    success += "✅ Motor control systems online\n";
    success += "✅ Servo positioning ready\n";
    success += "✅ Obstacle detection active\n";
    success += "✅ Safety systems enabled\n\n";
    success += "*🎮 Robot Controls:*\n";
    success += "• `/on 12` - Motor 1 forward\n";
    success += "• `/on 13` - Motor 2 forward\n";
    success += "• `/sensors read` - Distance check\n";
    success += "• `/read 0` - Emergency stop status\n\n";
    success += "🤖 Your robot is ready for action!";

    sendMessage(chatId, success);
}

// Advanced Commands Implementation
void TelegramBot::processAdvancedCommand(String chatId, String args) {
    if (args.length() == 0) {
        String message = "🔬 *Advanced Features*\n\n";
        message += "Professional IoT capabilities:\n\n";
        message += "⏰ *Scheduling:*\n";
        message += "• `/schedule add` - Create time-based tasks\n";
        message += "• `/schedule list` - View scheduled tasks\n\n";
        message += "🔔 *Notifications:*\n";
        message += "• `/notify setup` - Configure alerts\n";
        message += "• `/notify test` - Test notification\n\n";
        message += "💾 *Data Management:*\n";
        message += "• `/backup create` - Backup configuration\n";
        message += "• `/backup restore` - Restore settings\n\n";
        message += "🔄 *System Updates:*\n";
        message += "• `/update check` - Check for updates\n";
        message += "• `/update firmware` - Update firmware\n\n";
        message += "These features make ESPgram enterprise-ready!";

        sendMessage(chatId, message);
        return;
    }

    sendMessage(chatId, "🔬 Advanced feature: " + args + " - Coming in next update!");
}

void TelegramBot::processScheduleCommand(String chatId, String args) {
    String message = "⏰ *Smart Scheduling*\n\n";
    message += "Create time-based automation:\n\n";
    message += "*📅 Schedule Types:*\n";
    message += "• Daily tasks (lights, irrigation)\n";
    message += "• Weekly routines (maintenance)\n";
    message += "• Custom intervals (monitoring)\n\n";
    message += "*⚙️ Example Schedules:*\n";
    message += "• Turn on lights at sunset\n";
    message += "• Water plants every 6 hours\n";
    message += "• Weekly system health check\n\n";
    message += "Use `/automation_setup` to create scheduled rules!";

    sendMessage(chatId, message);
}

void TelegramBot::processNotificationCommand(String chatId, String args) {
    String message = "🔔 *Smart Notifications*\n\n";
    message += "Configure intelligent alerts:\n\n";
    message += "*📱 Notification Types:*\n";
    message += "• Sensor threshold alerts\n";
    message += "• System status updates\n";
    message += "• Security breach warnings\n";
    message += "• Maintenance reminders\n\n";
    message += "*⚙️ Alert Conditions:*\n";
    message += "• Temperature > 30°C\n";
    message += "• Motion detected\n";
    message += "• Low battery warning\n";
    message += "• Connection lost\n\n";
    message += "Notifications are automatically sent to this chat!";

    sendMessage(chatId, message);
}

void TelegramBot::processBackupCommand(String chatId, String args) {
    String message = "💾 *Configuration Backup*\n\n";
    message += "Protect your IoT setup:\n\n";
    message += "*🔒 Backup Features:*\n";
    message += "• GPIO configurations\n";
    message += "• Sensor settings\n";
    message += "• Automation rules\n";
    message += "• Network settings\n\n";
    message += "*📤 Backup Options:*\n";
    message += "• Local EEPROM storage\n";
    message += "• Cloud backup (future)\n";
    message += "• Export to file (future)\n\n";
    message += "Your settings are automatically saved to EEPROM!";

    sendMessage(chatId, message);
}

// Project Setup Step (placeholder for future expansion)
void TelegramBot::processProjectSetupStep(String chatId, int sessionIndex, String input) {
    // This would be used for complex project setup flows
    // For now, we use direct project templates
    clearUserSession(sessionIndex);
    sendMessage(chatId, "Project setup completed! Use `/project` to see available templates.");
}

void TelegramBot::processUpdateCommand(String chatId, String args) {
    String message = "🔄 *System Updates*\n\n";
    message += "Keep your ESPgram current:\n\n";
    message += "*📊 Current Version:*\n";
    message += "• ESPgram v" + String(FIRMWARE_VERSION_STRING) + "\n";
    message += "• Build: " + String(FIRMWARE_BUILD_TIMESTAMP) + "\n";
    message += "• Developer: " + String(FIRMWARE_DEVELOPER) + "\n";
    message += "• " + String(FIRMWARE_ORGANIZATION) + "\n\n";
    message += "*🚀 Latest Features:*\n";
    message += "• Conversational setup wizards\n";
    message += "• Project templates\n";
    message += "• Advanced automation\n";
    message += "• Professional UX\n\n";
    message += "You're running the latest version! ✅";

    sendMessage(chatId, message);
}
