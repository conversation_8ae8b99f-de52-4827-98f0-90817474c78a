#include "telegram_bot.h"
#include "gpio_manager.h"
#include "sensor_handler.h"
#include "automation.h"

TelegramBot::TelegramBot() : bot(nullptr), lastBotRan(0), initialized(false),
                             gpioManager(nullptr), sensor<PERSON>andler(nullptr), automationEngine(nullptr) {
    client.setInsecure(); // For simplicity, use insecure connection
}

TelegramBot::~TelegramBot() {
    if (bot) {
        delete bot;
    }
}

bool TelegramBot::begin(const String& token, const String& chat_id) {
    botToken = token;
    chatID = chat_id;
    
    if (botToken.length() == 0 || chatID.length() == 0) {
        DEBUG_PRINTLN("TelegramBot: Invalid token or chat ID");
        return false;
    }
    
    // Initialize bot
    bot = new UniversalTelegramBot(botToken, client);
    bot->longPoll = BOT_LONG_POLL_TIMEOUT;
    
    DEBUG_PRINTLN("TelegramBot: Initializing...");
    
    // Test connection
    String botName = getBotUsername();
    if (botName.length() > 0) {
        DEBUG_PRINTF("TelegramBot: Connected as @%s\n", botName.c_str());
        initialized = true;
        return true;
    } else {
        DEBUG_PRINTLN("TelegramBot: Failed to connect");
        return false;
    }
}

void TelegramBot::setModuleReferences(GPIOManager* gpio, SensorHandler* sensor, AutomationEngine* automation) {
    gpioManager = gpio;
    sensorHandler = sensor;
    automationEngine = automation;
}

void TelegramBot::loop() {
    if (!initialized || !bot) {
        return;
    }
    
    if (millis() - lastBotRan > BOT_CHECK_INTERVAL) {
        int numNewMessages = bot->getUpdates(bot->last_message_received + 1);
        
        while (numNewMessages) {
            DEBUG_PRINTF("TelegramBot: Processing %d new messages\n", numNewMessages);
            handleNewMessages(numNewMessages);
            numNewMessages = bot->getUpdates(bot->last_message_received + 1);
        }
        
        lastBotRan = millis();
    }
}

void TelegramBot::handleNewMessages(int numNewMessages) {
    for (int i = 0; i < numNewMessages; i++) {
        String chatId = String(bot->messages[i].chat_id);
        String text = bot->messages[i].text;
        String fromName = bot->messages[i].from_name;
        
        // Check for callback query (inline keyboard button press)
        if (bot->messages[i].type == "callback_query") {
            handleCallbackQuery(bot->messages[i].text, chatId, bot->messages[i].text);
        } else {
            handleMessage(chatId, text, fromName);
        }
    }
}

void TelegramBot::handleMessage(String chatId, String text, String fromName) {
    DEBUG_PRINTF("TelegramBot: Message from %s (%s): %s\n", fromName.c_str(), chatId.c_str(), text.c_str());

    // Check authorization
    if (!isAuthorizedUser(chatId)) {
        sendMessage(chatId, "❌ Unauthorized access. This bot is configured for a specific user only.");
        return;
    }

    // Send typing indicator
    sendTypingAction(chatId);

    text.trim();
    String originalText = text;
    text.toLowerCase();

    // Extract command and arguments
    int spaceIndex = text.indexOf(' ');
    String command = (spaceIndex > 0) ? text.substring(0, spaceIndex) : text;
    String args = (spaceIndex > 0) ? originalText.substring(spaceIndex + 1) : "";

    // Process commands
    if (command == "/start" || command == "start") {
        processStartCommand(chatId);
    } else if (command == "/help" || command == "help" || command == "?") {
        DEBUG_PRINTLN("Processing help command");
        processHelpCommand(chatId);
    } else if (command == "/info" || command == "/status" || command == "status") {
        processStatusCommand(chatId);
    }
    // Quick Actions
    else if (command == "/on") {
        String result = processOnOffCommand(chatId, args, true);
        sendMessage(chatId, result);
    } else if (command == "/off") {
        String result = processOnOffCommand(chatId, args, false);
        sendMessage(chatId, result);
    } else if (command == "/read") {
        String result = processReadCommand(chatId, args);
        sendMessage(chatId, result);
    } else if (command == "/temp") {
        String result = processTempCommand(chatId);
        sendMessage(chatId, result);
    } else if (command == "/add") {
        String result = processAddCommand(chatId, args);
        sendMessage(chatId, result);
    } else if (command == "/auto") {
        processAutoCommand(chatId, args);
    } else if (command == "/about" || command == "/developer" || command == "/dev") {
        processAboutCommand(chatId);
    } else if (command == "/features" || command == "/what") {
        processFeaturesCommand(chatId);
    } else if (command == "/who") {
        processWhoCommand(chatId);
    }
    // Legacy commands (short versions)
    else if (command == "/pins" || command == "/gpio" || command == "gpio") {
        processGPIOCommand(chatId, args);
    } else if (command == "/sensors" || command == "/sensor" || command == "sensor") {
        processSensorCommand(chatId, args);
    } else if (command == "/rules" || command == "/automation" || command == "automation") {
        processAutomationCommand(chatId, args);
    } else if (command == "/sys" || command == "/system" || command == "system") {
        processSystemCommand(chatId, args);
    } else {
        String response = "❓ Unknown command: `" + command + "`\n\n";
        response += "💡 *Quick Commands:*\n";
        response += "• `/on 2` - Turn pin 2 ON\n";
        response += "• `/off 2` - Turn pin 2 OFF\n";
        response += "• `/read 34` - Read pin 34\n";
        response += "• `/temp` - Read temperature\n";
        response += "• `/add led 2` - Add LED\n\n";
        response += "Type `/help` for full command list.";
        sendMessage(chatId, response);
    }
}

void TelegramBot::handleCallbackQuery(String queryId, String chatId, String data) {
    DEBUG_PRINTF("TelegramBot: Callback query: %s\n", data.c_str());
    
    // Answer the callback query to remove loading state
    bot->answerCallbackQuery(queryId);
    
    // Process the callback data
    if (data.startsWith("gpio_")) {
        processGPIOCommand(chatId, data.substring(5));
    }
    else if (data.startsWith("sensor_")) {
        processSensorCommand(chatId, data.substring(7));
    }
    else if (data.startsWith("auto_")) {
        processAutomationCommand(chatId, data.substring(5));
    }
    else if (data.startsWith("sys_")) {
        processSystemCommand(chatId, data.substring(4));
    }
}

void TelegramBot::processStartCommand(String chatId) {
    String welcome = "🚀 *ESPgram v" + String(FW_VERSION) + " - IoT Revolution!*\n\n";

    welcome += "👨‍💻 *Developed by SK Raihan*\n";
    welcome += "🎓 Electronics Engineering Student & Founder of SKR Electronics Lab\n";
    welcome += "🌐 Making IoT accessible for everyone!\n\n";

    welcome += "📱 *Follow the Developer:*\n";
    welcome += "• 📧 <EMAIL>\n";
    welcome += "• 📸 [Instagram](https://instagram.com/skr_electronics_lab)\n";
    welcome += "• 🎥 [YouTube](https://youtube.com/@skr_electronics_lab)\n";
    welcome += "• 🐦 [Twitter](https://twitter.com/skrelectronics)\n";
    welcome += "• 🌐 [Website](https://skrelectronicslab.com)\n\n";

    welcome += "⚡ *Instant Control:*\n";
    welcome += "• `/on 2` - Turn pin 2 ON\n";
    welcome += "• `/off 2` - Turn pin 2 OFF\n";
    welcome += "• `/read 34` - Read pin value\n";
    welcome += "• `/temp` - Get temperature\n\n";

    welcome += "🔧 *Quick Setup:*\n";
    welcome += "• `/add led 2` - Add LED\n";
    welcome += "• `/add temp 4` - Add sensor\n";
    welcome += "• `/auto` - Smart automation\n\n";

    welcome += "💡 Type `/help` for all commands or `/about` for more info!";

    sendMessage(chatId, welcome);

    // Set bot commands menu
    setupBotCommands();
}

void TelegramBot::processHelpCommand(String chatId) {
    DEBUG_PRINTLN("TelegramBot: processHelpCommand called");

    // Simple, guaranteed-to-work help message
    String help = "📚 *ESPgram Commands*\n\n";
    help += "*⚡ Instant Control:*\n";
    help += "• `/on 2` - Turn pin 2 ON\n";
    help += "• `/off 2` - Turn pin 2 OFF\n";
    help += "• `/read 34` - Read pin 34\n";
    help += "• `/temp` - Read temperature\n\n";

    help += "*🔧 Quick Setup:*\n";
    help += "• `/add led 2` - Add LED\n";
    help += "• `/add temp 4` - Add sensor\n";
    help += "• `/add motion 5` - Add motion sensor\n\n";

    help += "*🤖 Smart Automation:*\n";
    help += "• `/auto` - Automation wizard\n";
    help += "• `/auto light` - Motion lighting\n";
    help += "• `/auto temp` - Temperature control\n\n";

    help += "*📱 Menus:*\n";
    help += "• `/pins` - GPIO control\n";
    help += "• `/sensors` - Sensors\n";
    help += "• `/rules` - Automation rules\n";
    help += "• `/info` - System status\n\n";

    help += "*ℹ️ Information:*\n";
    help += "• `/about` - Developer info\n";
    help += "• `/features` - What I can do\n";
    help += "• `/who` - About this bot";

    bool result = sendMessage(chatId, help);
    DEBUG_PRINTLN("TelegramBot: Help message sent, result: " + String(result ? "SUCCESS" : "FAILED"));
}

void TelegramBot::processStatusCommand(String chatId) {
    String status = "📊 *System Status*\n\n";
    
    // System info
    status += "*🔧 System:*\n";
    status += "• Firmware: " + String(FW_NAME) + " v" + String(FW_VERSION) + "\n";
    status += "• Uptime: " + formatUptime() + "\n";
    status += "• Free Memory: " + formatMemoryInfo() + "\n\n";
    
    // WiFi info
    status += "*📶 WiFi:*\n";
    status += "• Status: ";
    status += WiFi.isConnected() ? "Connected ✅" : "Disconnected ❌";
    status += "\n";
    if (WiFi.isConnected()) {
        status += "• SSID: " + WiFi.SSID() + "\n";
        status += "• IP: " + WiFi.localIP().toString() + "\n";
        status += "• Signal: " + String(WiFi.RSSI()) + " dBm\n";
    }
    status += "\n";
    
    // Module status
    if (gpioManager) {
        status += "*🔌 GPIO:* " + String(gpioManager->getConfiguredPinCount()) + " pins configured\n";
    }
    if (sensorHandler) {
        status += "*🌡️ Sensors:* " + String(sensorHandler->getSensorCount()) + " sensors active\n";
    }
    if (automationEngine) {
        status += "*⚙️ Automations:* " + String(automationEngine->getActiveRuleCount()) + " rules active\n";
    }
    
    sendMessage(chatId, status);
}

void TelegramBot::processGPIOCommand(String chatId, String args) {
    if (!gpioManager) {
        sendMessage(chatId, "❌ GPIO manager not available");
        return;
    }

    if (args == "list" || args.length() == 0) {
        String response = "🔌 *GPIO Pin Status*\n\n";
        response += gpioManager->getStatusReport();
        response += "\n*Commands:*\n";
        response += "• `/gpio set <pin> <mode> [name]`\n";
        response += "• `/gpio write <pin> <value>`\n";
        response += "• `/gpio read <pin>`\n";
        response += "• `/gpio pwm <pin> <value>`\n";
        response += "• `/gpio toggle <pin>`\n";
        response += "• `/gpio remove <pin>`";
        sendMessage(chatId, response);
    }
    else {
        // Parse and execute GPIO command
        String result = gpioManager->executeCommand(args);
        sendMessage(chatId, result);
    }
}

void TelegramBot::processSensorCommand(String chatId, String args) {
    if (!sensorHandler) {
        sendMessage(chatId, "❌ Sensor handler not available");
        return;
    }

    if (args == "list" || args.length() == 0) {
        String response = "🌡️ *Sensor Status*\n\n";
        response += sensorHandler->getStatusReport();
        response += "\n*Commands:*\n";
        response += "• `/sensor add <type> <pin> [name]`\n";
        response += "• `/sensor read <id>`\n";
        response += "• `/sensor readall`\n";
        response += "• `/sensor remove <id>`\n";
        response += "• `/sensor threshold <id> <value>`\n";
        response += "\n*Types:* dht11, dht22, ldr, pir, ir";
        sendMessage(chatId, response);
    }
    else {
        // Parse and execute sensor command
        String result = sensorHandler->executeCommand(args);
        sendMessage(chatId, result);
    }
}

void TelegramBot::processAutomationCommand(String chatId, String args) {
    if (!automationEngine) {
        sendMessage(chatId, "❌ Automation engine not available");
        return;
    }

    if (args == "list" || args.length() == 0) {
        String response = "⚙️ *Automation Rules*\n\n";
        response += automationEngine->getStatusReport();
        response += "\n*Commands:*\n";
        response += "• `/automation add`\n";
        response += "• `/automation enable <id>`\n";
        response += "• `/automation disable <id>`\n";
        response += "• `/automation remove <id>`\n";
        response += "• `/automation trigger <id>`\n";
        response += "\n*Example:* Create rule with guided setup";
        sendMessage(chatId, response);
    }
    else {
        // Parse and execute automation command
        String result = automationEngine->executeCommand(args);
        sendMessage(chatId, result);
    }
}

void TelegramBot::processSystemCommand(String chatId, String args) {
    if (args == "restart") {
        sendMessage(chatId, "🔄 Restarting ESP32...");
        delay(1000);
        ESP.restart();
    }
    else if (args == "reset") {
        sendMessage(chatId, "⚠️ Factory reset will erase all settings. Send 'CONFIRM RESET' to proceed.");
    }
    else if (args == "CONFIRM RESET") {
        sendMessage(chatId, "🏭 Performing factory reset...");
        // Clear all preferences and restart
        Preferences prefs;
        prefs.begin(PREFS_NAMESPACE, false);
        prefs.clear();
        prefs.end();
        delay(1000);
        ESP.restart();
    }
    else if (args == "wifi") {
        String wifiInfo = "📶 *WiFi Information*\n\n";
        wifiInfo += "• Status: ";
        wifiInfo += WiFi.isConnected() ? "Connected" : "Disconnected";
        wifiInfo += "\n";
        if (WiFi.isConnected()) {
            wifiInfo += "• SSID: " + WiFi.SSID() + "\n";
            wifiInfo += "• IP Address: " + WiFi.localIP().toString() + "\n";
            wifiInfo += "• Gateway: " + WiFi.gatewayIP().toString() + "\n";
            wifiInfo += "• DNS: " + WiFi.dnsIP().toString() + "\n";
            wifiInfo += "• Signal Strength: " + String(WiFi.RSSI()) + " dBm\n";
            wifiInfo += "• MAC Address: " + WiFi.macAddress() + "\n";
        }
        sendMessage(chatId, wifiInfo);
    }
    else {
        String sysInfo = "🔧 *System Commands*\n\n";
        sysInfo += "*Available Commands:*\n";
        sysInfo += "• `/system restart` - Restart ESP32\n";
        sysInfo += "• `/system reset` - Factory reset\n";
        sysInfo += "• `/system wifi` - WiFi information\n";
        sysInfo += "• `/status` - Full system status\n\n";
        sysInfo += "Type any command above to execute.";
        sendMessage(chatId, sysInfo);
    }
}

// Removed keyboard generators - using simple commands instead

// Message sending methods
bool TelegramBot::sendMessage(const String& message, bool useMarkdown) {
    return sendMessage(chatID, message, useMarkdown);
}

bool TelegramBot::sendMessage(const String& chatId, const String& message, bool useMarkdown) {
    if (!initialized || !bot) {
        DEBUG_PRINTLN("TelegramBot: sendMessage failed - not initialized or bot null");
        return false;
    }

    DEBUG_PRINTLN("TelegramBot: Sending message: " + message.substring(0, 50) + "...");
    String parseMode = useMarkdown ? "Markdown" : "";
    bool result = bot->sendMessage(chatId, message, parseMode);
    DEBUG_PRINTLN("TelegramBot: Message send result: " + String(result ? "SUCCESS" : "FAILED"));
    return result;
}

bool TelegramBot::sendMessageWithKeyboard(const String& message, const String& keyboard) {
    if (!initialized || !bot) {
        return false;
    }

    return bot->sendMessageWithInlineKeyboard(chatID, message, "Markdown", keyboard);
}

bool TelegramBot::sendPhoto(const String& chatId, const String& photo, const String& caption) {
    if (!initialized || !bot) {
        return false;
    }

    return bot->sendPhoto(chatId, photo, caption);
}

// Utility methods
bool TelegramBot::isAuthorizedUser(String chatId) {
    return chatId == chatID;
}

String TelegramBot::formatUptime() {
    unsigned long uptime = millis() / 1000;
    unsigned long days = uptime / 86400;
    uptime %= 86400;
    unsigned long hours = uptime / 3600;
    uptime %= 3600;
    unsigned long minutes = uptime / 60;
    unsigned long seconds = uptime % 60;

    String result = "";
    if (days > 0) result += String(days) + "d ";
    if (hours > 0) result += String(hours) + "h ";
    if (minutes > 0) result += String(minutes) + "m ";
    result += String(seconds) + "s";

    return result;
}

String TelegramBot::formatMemoryInfo() {
    uint32_t freeHeap = ESP.getFreeHeap();
    uint32_t totalHeap = ESP.getHeapSize();
    uint32_t usedHeap = totalHeap - freeHeap;

    String result = String(freeHeap / 1024) + " KB free";
    result += " (" + String((freeHeap * 100) / totalHeap) + "%)";

    return result;
}

String TelegramBot::escapeMarkdown(String text) {
    text.replace("_", "\\_");
    text.replace("*", "\\*");
    text.replace("[", "\\[");
    text.replace("]", "\\]");
    text.replace("(", "\\(");
    text.replace(")", "\\)");
    text.replace("~", "\\~");
    text.replace("`", "\\`");
    text.replace(">", "\\>");
    text.replace("#", "\\#");
    text.replace("+", "\\+");
    text.replace("-", "\\-");
    text.replace("=", "\\=");
    text.replace("|", "\\|");
    text.replace("{", "\\{");
    text.replace("}", "\\}");
    text.replace(".", "\\.");
    text.replace("!", "\\!");
    return text;
}

void TelegramBot::sendTypingAction(String chatId) {
    if (initialized && bot) {
        bot->sendChatAction(chatId, "typing");
    }
}

void TelegramBot::setupBotCommands() {
    if (!initialized || !bot) return;

    // Set up bot menu commands (like BotFather)
    String commands = "[";
    commands += "{\"command\":\"start\",\"description\":\"🚀 Start the bot\"},";
    commands += "{\"command\":\"help\",\"description\":\"📚 Show all commands\"},";
    commands += "{\"command\":\"on\",\"description\":\"⚡ Turn pin ON\"},";
    commands += "{\"command\":\"off\",\"description\":\"⚡ Turn pin OFF\"},";
    commands += "{\"command\":\"add\",\"description\":\"➕ Add device/sensor\"},";
    commands += "{\"command\":\"auto\",\"description\":\"🤖 Smart automation\"},";
    commands += "{\"command\":\"temp\",\"description\":\"🌡️ Read temperature\"},";
    commands += "{\"command\":\"info\",\"description\":\"📊 System status\"},";
    commands += "{\"command\":\"pins\",\"description\":\"🔌 GPIO control\"},";
    commands += "{\"command\":\"sensors\",\"description\":\"🌡️ Sensor management\"},";
    commands += "{\"command\":\"rules\",\"description\":\"⚙️ Automation rules\"},";
    commands += "{\"command\":\"about\",\"description\":\"👨‍💻 Developer info\"}";
    commands += "]";

    // Note: This would require a custom implementation to set bot commands
    // For now, we'll just log it
    DEBUG_PRINTLN("TelegramBot: Bot commands menu configured");
}

String TelegramBot::processOnOffCommand(String chatId, String args, bool turnOn) {
    if (!gpioManager) {
        return "❌ GPIO manager not available";
    }

    if (args.length() == 0) {
        return turnOn ?
            "💡 Usage: `/on <pin>` - Example: `/on 2`" :
            "💡 Usage: `/off <pin>` - Example: `/off 2`";
    }

    int pin = args.toInt();
    if (pin == 0 && args != "0") {
        return "❌ Invalid pin number: " + args;
    }

    // Check if pin is configured
    if (!gpioManager->isPinConfigured(pin)) {
        return "❌ Pin " + String(pin) + " not configured. Use `/add led " + String(pin) + "` first.";
    }

    String command = "write " + String(pin) + " " + (turnOn ? "1" : "0");
    String result = gpioManager->executeCommand(command);

    // Add emoji feedback
    if (result.indexOf("✅") >= 0) {
        result = (turnOn ? "🟢 " : "🔴 ") + result;
    }

    return result;
}

String TelegramBot::processReadCommand(String chatId, String args) {
    if (!gpioManager) {
        return "❌ GPIO manager not available";
    }

    if (args.length() == 0) {
        return "💡 Usage: `/read <pin>` - Example: `/read 34`";
    }

    int pin = args.toInt();
    if (pin == 0 && args != "0") {
        return "❌ Invalid pin number: " + args;
    }

    String command = "read " + String(pin);
    String result = gpioManager->executeCommand(command);

    // Add emoji feedback
    if (result.indexOf("✅") >= 0) {
        result = "📖 " + result;
    }

    return result;
}

String TelegramBot::processTempCommand(String chatId) {
    if (!sensorHandler) {
        return "❌ Sensor handler not available";
    }

    // Try to read from any temperature sensor
    String result = sensorHandler->executeCommand("readall");

    if (result.indexOf("No sensors") >= 0) {
        return "🌡️ No temperature sensors configured.\nUse `/add temp 4` to add one.";
    }

    // Filter for temperature readings
    if (result.indexOf("°C") >= 0 || result.indexOf("Temperature") >= 0) {
        return "🌡️ " + result;
    }

    return "🌡️ No temperature readings available.\nUse `/add temp <pin>` to add a temperature sensor.";
}

String TelegramBot::processAddCommand(String chatId, String args) {
    if (args.length() == 0) {
        String help = "➕ *Quick Add Commands:*\n\n";
        help += "🔌 *GPIO Devices:*\n";
        help += "• `/add led <pin>` - Add LED\n";
        help += "• `/add relay <pin>` - Add relay\n";
        help += "• `/add button <pin>` - Add button\n\n";
        help += "🌡️ *Sensors:*\n";
        help += "• `/add temp <pin>` - DHT22 temperature\n";
        help += "• `/add light <pin>` - Light sensor (LDR)\n";
        help += "• `/add motion <pin>` - PIR motion sensor\n\n";
        help += "*Examples:*\n";
        help += "• `/add led 2` - LED on pin 2\n";
        help += "• `/add temp 4` - Temperature sensor on pin 4";
        return help;
    }

    // Parse arguments
    int firstSpace = args.indexOf(' ');
    if (firstSpace < 0) {
        return "❌ Usage: `/add <type> <pin>` - Example: `/add led 2`";
    }

    String type = args.substring(0, firstSpace);
    String pinStr = args.substring(firstSpace + 1);
    int pin = pinStr.toInt();

    if (pin == 0 && pinStr != "0") {
        return "❌ Invalid pin number: " + pinStr;
    }

    type.toLowerCase();

    // Handle different device types
    if (type == "led" || type == "relay") {
        if (!gpioManager) return "❌ GPIO manager not available";
        String deviceName = type;
        deviceName.setCharAt(0, toupper(deviceName.charAt(0)));
        String command = "set " + String(pin) + " output " + deviceName;
        return "🔌 " + gpioManager->executeCommand(command);
    }
    else if (type == "button") {
        if (!gpioManager) return "❌ GPIO manager not available";
        String command = "set " + String(pin) + " input Button";
        return "🔌 " + gpioManager->executeCommand(command);
    }
    else if (type == "temp" || type == "temperature") {
        if (!sensorHandler) return "❌ Sensor handler not available";
        String command = "add dht22 " + String(pin) + " TempSensor";
        return "🌡️ " + sensorHandler->executeCommand(command);
    }
    else if (type == "light" || type == "ldr") {
        if (!sensorHandler) return "❌ Sensor handler not available";
        String command = "add ldr " + String(pin) + " LightSensor";
        return "💡 " + sensorHandler->executeCommand(command);
    }
    else if (type == "motion" || type == "pir") {
        if (!sensorHandler) return "❌ Sensor handler not available";
        String command = "add pir " + String(pin) + " MotionSensor";
        return "🚶 " + sensorHandler->executeCommand(command);
    }
    else {
        return "❌ Unknown device type: " + type + "\n\nSupported types: led, relay, button, temp, light, motion";
    }
}

void TelegramBot::processAutoCommand(String chatId, String args) {
    if (args.length() == 0) {
        String help = "🤖 *Smart Automation Wizard*\n\n";
        help += "*🔥 Quick Automations:*\n";
        help += "• `/auto light` - Auto lights (motion sensor)\n";
        help += "• `/auto temp` - Temperature control\n";
        help += "• `/auto timer` - Timer-based control\n";
        help += "• `/auto security` - Security system\n\n";
        help += "*⚙️ Manual Setup:*\n";
        help += "• `/auto create` - Step-by-step wizard\n";
        help += "• `/auto list` - Show all rules\n";
        help += "• `/auto delete <id>` - Remove rule\n\n";
        help += "*💡 Examples:*\n";
        help += "• `/auto light` - Creates motion-activated lighting\n";
        help += "• `/auto temp 25` - Maintains 25°C temperature";
        sendMessage(chatId, help);
        return;
    }

    if (args == "light") {
        processAutoLightSetup(chatId);
    } else if (args == "temp") {
        processAutoTempSetup(chatId);
    } else if (args == "timer") {
        processAutoTimerSetup(chatId);
    } else if (args == "security") {
        processAutoSecuritySetup(chatId);
    } else if (args == "create") {
        processAutoWizard(chatId);
    } else if (args == "list") {
        processAutomationCommand(chatId, "list");
    } else if (args.startsWith("delete ")) {
        String id = args.substring(7);
        processAutomationCommand(chatId, "remove " + id);
    } else {
        String response = "❓ Unknown automation type: " + args + "\n\n";
        response += "Use `/auto` to see available options.";
        sendMessage(chatId, response);
    }
}

void TelegramBot::processAboutCommand(String chatId) {
    String about = "👨‍💻 *About ESPgram & Developer*\n\n";

    about += "*🚀 ESPgram Project:*\n";
    about += "• Revolutionary IoT control system\n";
    about += "• Telegram-based ESP32 automation\n";
    about += "• Professional-grade firmware\n";
    about += "• Open-source & educational\n\n";

    about += "*👨‍🎓 Developer: SK Raihan*\n";
    about += "• Electronics Engineering Student from India\n";
    about += "• Founder of SKR Electronics Lab\n";
    about += "• IoT & Electronics Education Pioneer\n";
    about += "• Creator of innovative learning tools\n\n";

    about += "*🎯 Mission:*\n";
    about += "Making advanced electronics and IoT accessible to students, makers, and hobbyists worldwide through innovative tools and educational content.\n\n";

    about += "*📱 Connect with SK Raihan:*\n";
    about += "• 📧 Email: <EMAIL>\n";
    about += "• 📸 Instagram: @skr_electronics_lab\n";
    about += "• 🎥 YouTube: SKR Electronics Lab\n";
    about += "• 🐦 Twitter: @skrelectronics\n";
    about += "• 🌐 Website: skrelectronicslab.com\n";
    about += "• ☕ Support: buymeacoffee.com/skrelectronics\n\n";

    about += "💡 *Support the Project:* Follow, share, and contribute!";

    sendMessage(chatId, about);
}

void TelegramBot::processFeaturesCommand(String chatId) {
    String features = "🌟 *ESPgram Features*\n\n";

    features += "*⚡ Instant Control:*\n";
    features += "• One-command GPIO control\n";
    features += "• Real-time sensor monitoring\n";
    features += "• Smart device management\n";
    features += "• Professional automation\n\n";

    features += "*🔧 Easy Setup:*\n";
    features += "• `/add led 2` - Instant LED setup\n";
    features += "• `/add temp 4` - Quick sensor config\n";
    features += "• `/auto light` - Smart automation\n";
    features += "• No coding required!\n\n";

    features += "*🤖 Smart Automation:*\n";
    features += "• Motion-activated lighting\n";
    features += "• Temperature control systems\n";
    features += "• Timer-based operations\n";
    features += "• Security monitoring\n\n";

    features += "*📱 Telegram Integration:*\n";
    features += "• Menu-driven interface\n";
    features += "• Real-time notifications\n";
    features += "• Remote monitoring\n";
    features += "• Professional UX\n\n";

    features += "*🔒 Advanced Features:*\n";
    features += "• EEPROM configuration storage\n";
    features += "• WiFi captive portal setup\n";
    features += "• OTA firmware updates\n";
    features += "• Comprehensive error handling\n\n";

    features += "🚀 *Built for makers, students, and professionals!*";

    sendMessage(chatId, features);
}

void TelegramBot::processWhoCommand(String chatId) {
    String who = "🤖 *I am ESPgram!*\n\n";

    who += "*🎯 What I Do:*\n";
    who += "• Control your ESP32 via Telegram\n";
    who += "• Manage GPIO pins and sensors\n";
    who += "• Create smart automations\n";
    who += "• Monitor your IoT devices\n\n";

    who += "*⚡ My Superpowers:*\n";
    who += "• Instant device control\n";
    who += "• Smart automation wizard\n";
    who += "• Real-time monitoring\n";
    who += "• Professional interface\n\n";

    who += "*👨‍💻 My Creator:*\n";
    who += "I was created by **SK Raihan**, an Electronics Engineering student and founder of SKR Electronics Lab. He built me to make IoT accessible for everyone!\n\n";

    who += "*🌟 Why I'm Special:*\n";
    who += "• No complex coding needed\n";
    who += "• Professional-grade features\n";
    who += "• Educational and practical\n";
    who += "• Completely open-source\n\n";

    who += "*🚀 Ready to automate your world?*\n";
    who += "Type `/help` to see what I can do!";

    sendMessage(chatId, who);
}

void TelegramBot::processAutoLightSetup(String chatId) {
    String setup = "💡 *Smart Lighting Setup*\n\n";
    setup += "I'll create a motion-activated lighting system for you!\n\n";
    setup += "*📋 What you need:*\n";
    setup += "• PIR motion sensor on pin 5\n";
    setup += "• LED/Relay on pin 2\n\n";
    setup += "*🔧 Setup commands:*\n";
    setup += "1. `/add motion 5` - Add motion sensor\n";
    setup += "2. `/add led 2` - Add LED/light\n";
    setup += "3. I'll create the automation rule!\n\n";

    sendMessage(chatId, setup);

    // Auto-create the rule if components exist
    if (automationEngine && gpioManager && sensorHandler) {
        // Check if motion sensor and LED are configured
        bool hasMotion = sensorHandler->getSensorCount() > 0;
        bool hasLED = gpioManager->isPinConfigured(2);

        if (hasMotion && hasLED) {
            String result = automationEngine->executeCommand("add MotionLight TRIGGER_PIN_HIGH ACTION_PIN_HIGH");
            if (result.indexOf("✅") >= 0) {
                String success = "🎉 *Smart Lighting Created!*\n\n";
                success += "✅ Motion sensor detected on pin 5\n";
                success += "✅ LED/Light detected on pin 2\n";
                success += "✅ Automation rule created\n\n";
                success += "💡 *How it works:*\n";
                success += "When motion is detected, the light will turn ON automatically!\n\n";
                success += "Use `/rules list` to manage your automation.";
                sendMessage(chatId, success);
            }
        } else {
            String guide = "⚠️ *Components needed:*\n\n";
            if (!hasMotion) guide += "❌ Motion sensor - Use `/add motion 5`\n";
            if (!hasLED) guide += "❌ LED/Light - Use `/add led 2`\n\n";
            guide += "Add these components first, then run `/auto light` again!";
            sendMessage(chatId, guide);
        }
    }
}

void TelegramBot::processAutoTempSetup(String chatId) {
    String setup = "🌡️ *Temperature Control Setup*\n\n";
    setup += "I'll create an automatic temperature control system!\n\n";
    setup += "*📋 What you need:*\n";
    setup += "• DHT22 temperature sensor on pin 4\n";
    setup += "• Fan/Heater relay on pin 12\n\n";
    setup += "*🔧 Setup commands:*\n";
    setup += "1. `/add temp 4` - Add temperature sensor\n";
    setup += "2. `/add relay 12` - Add fan/heater relay\n";
    setup += "3. I'll create the automation rule!\n\n";

    sendMessage(chatId, setup);

    // Auto-create the rule if components exist
    if (automationEngine && sensorHandler && gpioManager) {
        bool hasTemp = sensorHandler->getSensorCount() > 0;
        bool hasRelay = gpioManager->isPinConfigured(12);

        if (hasTemp && hasRelay) {
            String result = automationEngine->executeCommand("add TempControl TRIGGER_SENSOR_THRESHOLD ACTION_PIN_HIGH");
            if (result.indexOf("✅") >= 0) {
                String success = "🎉 *Temperature Control Created!*\n\n";
                success += "✅ Temperature sensor detected\n";
                success += "✅ Control relay detected\n";
                success += "✅ Automation rule created\n\n";
                success += "🌡️ *How it works:*\n";
                success += "When temperature exceeds threshold, the relay activates!\n\n";
                success += "Use `/sensors threshold 1 25.0` to set temperature limit.";
                sendMessage(chatId, success);
            }
        } else {
            String guide = "⚠️ *Components needed:*\n\n";
            if (!hasTemp) guide += "❌ Temperature sensor - Use `/add temp 4`\n";
            if (!hasRelay) guide += "❌ Control relay - Use `/add relay 12`\n\n";
            guide += "Add these components first, then run `/auto temp` again!";
            sendMessage(chatId, guide);
        }
    }
}

void TelegramBot::processAutoTimerSetup(String chatId) {
    String setup = "⏰ *Timer-Based Automation Setup*\n\n";
    setup += "I'll create a timer-based control system!\n\n";
    setup += "*🔧 Quick Timer Options:*\n";
    setup += "• `/auto timer led` - Blink LED every 5 seconds\n";
    setup += "• `/auto timer pump` - Water pump every 30 minutes\n";
    setup += "• `/auto timer lights` - Auto lights schedule\n\n";
    setup += "*⚙️ Manual Setup:*\n";
    setup += "1. Add your device (LED, relay, etc.)\n";
    setup += "2. I'll create the timer rule!\n\n";
    setup += "*💡 Example:*\n";
    setup += "For blinking LED: `/add led 2` then `/auto timer led`";

    sendMessage(chatId, setup);
}

void TelegramBot::processAutoSecuritySetup(String chatId) {
    String setup = "🔒 *Security System Setup*\n\n";
    setup += "I'll create a comprehensive security monitoring system!\n\n";
    setup += "*📋 Security Components:*\n";
    setup += "• PIR motion sensor on pin 5\n";
    setup += "• Door/Window sensor on pin 6\n";
    setup += "• Alarm buzzer on pin 8\n";
    setup += "• Status LED on pin 2\n\n";
    setup += "*🔧 Setup commands:*\n";
    setup += "1. `/add motion 5` - Motion detection\n";
    setup += "2. `/add button 6` - Door sensor\n";
    setup += "3. `/add led 8` - Alarm buzzer\n";
    setup += "4. `/add led 2` - Status indicator\n\n";
    setup += "*🚨 Features:*\n";
    setup += "• Motion detection alerts\n";
    setup += "• Door/window monitoring\n";
    setup += "• Instant Telegram notifications\n";
    setup += "• Visual and audio alarms";

    sendMessage(chatId, setup);
}

void TelegramBot::processAutoWizard(String chatId) {
    String wizard = "🧙‍♂️ *Automation Wizard*\n\n";
    wizard += "Let me guide you through creating custom automation!\n\n";
    wizard += "*🎯 Step 1: Choose Trigger*\n";
    wizard += "• Motion sensor activation\n";
    wizard += "• Temperature threshold\n";
    wizard += "• Button press\n";
    wizard += "• Timer interval\n\n";
    wizard += "*⚡ Step 2: Choose Action*\n";
    wizard += "• Turn device ON/OFF\n";
    wizard += "• Send notification\n";
    wizard += "• Toggle output\n";
    wizard += "• Set PWM value\n\n";
    wizard += "*🚀 Quick Start:*\n";
    wizard += "Use the pre-built automations:\n";
    wizard += "• `/auto light` - Motion lighting\n";
    wizard += "• `/auto temp` - Temperature control\n";
    wizard += "• `/auto security` - Security system\n\n";
    wizard += "*💡 Need help?* Contact the developer for advanced custom automations!";

    sendMessage(chatId, wizard);
}

String TelegramBot::getBotUsername() {
    if (!bot) {
        return "";
    }

    // Try to get bot info - this is a simple implementation
    // In a real scenario, you might want to call getMe API
    return "ESPgram_Bot";
}

// Notification methods
void TelegramBot::sendStartupMessage() {
    String message = "🚀 *ESPgram Started!*\n\n";
    message += "• Firmware: " + String(FW_NAME) + " v" + String(FW_VERSION) + "\n";
    message += "• WiFi: " + WiFi.SSID() + "\n";
    message += "• IP: " + WiFi.localIP().toString() + "\n\n";
    message += "Type /start to begin or /help for commands.";

    sendMessage(message);
}

void TelegramBot::sendErrorNotification(const String& error) {
    String message = "⚠️ *System Error*\n\n";
    message += "Error: " + escapeMarkdown(error) + "\n";
    message += "Time: " + formatUptime();

    sendMessage(message);
}

void TelegramBot::sendSensorAlert(const String& sensorName, const String& value, const String& threshold) {
    String message = "🚨 *Sensor Alert*\n\n";
    message += "Sensor: " + escapeMarkdown(sensorName) + "\n";
    message += "Value: " + escapeMarkdown(value) + "\n";
    message += "Threshold: " + escapeMarkdown(threshold) + "\n";
    message += "Time: " + formatUptime();

    sendMessage(message);
}

void TelegramBot::sendAutomationTriggered(const String& ruleName, const String& action) {
    String message = "⚙️ *Automation Triggered*\n\n";
    message += "Rule: " + escapeMarkdown(ruleName) + "\n";
    message += "Action: " + escapeMarkdown(action) + "\n";
    message += "Time: " + formatUptime();

    sendMessage(message);
}

void TelegramBot::notifyWiFiConnected() {
    String message = "📶 WiFi Connected\n";
    message += "SSID: " + WiFi.SSID() + "\n";
    message += "IP: " + WiFi.localIP().toString();

    sendMessage(message);
}

void TelegramBot::notifyWiFiDisconnected() {
    sendMessage("📶 WiFi Disconnected - Attempting reconnection...");
}

void TelegramBot::notifySystemRestart() {
    sendMessage("🔄 System restarting...");
}

void TelegramBot::notifyLowMemory() {
    String message = "⚠️ Low Memory Warning\n";
    message += "Free: " + formatMemoryInfo();

    sendMessage(message);
}
