#include "telegram_bot.h"
#include "gpio_manager.h"
#include "sensor_handler.h"
#include "automation.h"
#include <ESP8266WiFi.h>

TelegramBot::TelegramBot() : lastBotRan(0), bot(nullptr), initialized(false),
                             gpioManager(nullptr), sensor<PERSON>and<PERSON>(nullptr), automationEngine(nullptr) {
    client.setInsecure(); // For simplicity, use insecure connection
}

TelegramBot::~TelegramBot() {
    if (bot) {
        delete bot;
    }
}

bool TelegramBot::begin(String token, String chat_id) {
    botToken = token;
    chatId = chat_id;

    Serial.println("Initializing Telegram bot...");
    Serial.println("Bot Token: " + token.substring(0, 10) + "...");
    Serial.println("Chat ID: " + chat_id);

    if (bot) {
        delete bot;
    }

    bot = new UniversalTelegramBot(botToken, client);

    // Test connection
    Serial.println("Testing bot connection...");
    if (bot->getMe()) {
        initialized = true;
        Serial.println("Telegram bot initialized successfully");
        return true;
    } else {
        Serial.println("Failed to initialize Telegram bot - check token and internet connection");
        initialized = false;
        return false;
    }
}

void TelegramBot::setModuleReferences(GPIOManager* gpio, SensorHandler* sensor, AutomationEngine* automation) {
    gpioManager = gpio;
    sensorHandler = sensor;
    automationEngine = automation;
}

void TelegramBot::handleMessages() {
    if (!initialized || millis() - lastBotRan < BOT_REQUEST_DELAY) {
        return;
    }

    int numNewMessages = bot->getUpdates(bot->last_message_received + 1);

    while (numNewMessages) {
        handleNewMessages(numNewMessages);
        numNewMessages = bot->getUpdates(bot->last_message_received + 1);
    }

    lastBotRan = millis();
}

void TelegramBot::handleNewMessages(int numNewMessages) {
    for (int i = 0; i < numNewMessages; i++) {
        String chat_id = String(bot->messages[i].chat_id);
        String text = bot->messages[i].text;
        String from_name = bot->messages[i].from_name;

        if (!isAuthorized(chat_id)) {
            sendMessage(chat_id, "❌ Unauthorized access. This bot is configured for a specific user.");
            continue;
        }

        // Handle callback queries (button presses)
        if (bot->messages[i].type == "callback_query") {
            String callback_data = bot->messages[i].text;
            processCommand(chat_id, callback_data, from_name);
            // Answer callback query to remove loading state
            bot->answerCallbackQuery(bot->messages[i].query_id);
        } else {
            // Handle regular text messages
            processCommand(chat_id, text, from_name);
        }
    }
}

void TelegramBot::handleCallbackQuery(String chatId, String callbackData, String queryId) {
    // Answer the callback query to remove loading state
    bot->answerCallbackQuery(queryId, "");

    // Process the callback data as a command
    processCommand(chatId, callbackData, "callback");
}

void TelegramBot::processCommand(String chatId, String text, String from_name) {
    text.toLowerCase();
    text.trim();

    Serial.println("Processing command: '" + text + "' from " + from_name);

    // Handle callback data from inline buttons
    if (text == "main_menu" || text == "/start" || text == "start" || text == "menu") {
        Serial.println("Sending main menu");
        sendMainMenu(chatId);
    }
    else if (text == "menu_gpio") {
        sendGPIOMenu(chatId);
    }
    else if (text == "menu_sensors") {
        sendSensorMenu(chatId);
    }
    else if (text == "menu_automation") {
        sendAutomationMenu(chatId);
    }
    else if (text == "menu_status" || text == "menu_settings") {
        sendSystemMenu(chatId);
    }
    else if (text == "menu_help") {
        sendHelpMessage(chatId);
    }
    else if (text == "/help" || text == "help") {
        sendHelpMessage(chatId);
    }
    else if (text == "/status") {
        handleSystemCommand(chatId, "sys_status");
    }

    // GPIO Commands and Callbacks
    else if (text == "/gpio" || text == "gpio") {
        sendGPIOMenu(chatId);
    }
    else if (text == "gpio_list" || text == "/gpio_list") {
        handleGPIOCommand(chatId, "gpio_list");
    }
    else if (text == "gpio_add") {
        sendMessage(chatId, "➕ *Add GPIO Pin*\n\nSend command in format:\n`/gpio_add <pin> <type> <name>`\n\n*Types:* digital_out, digital_in, pwm, analog\n*Example:* `/gpio_add 2 digital_out LED`");
    }
    else if (text == "gpio_on") {
        sendMessage(chatId, "💡 *Turn Pin ON*\n\nSend command:\n`/gpio_on <pin>`\n\n*Example:* `/gpio_on 2`");
    }
    else if (text == "gpio_off") {
        sendMessage(chatId, "🔴 *Turn Pin OFF*\n\nSend command:\n`/gpio_off <pin>`\n\n*Example:* `/gpio_off 2`");
    }
    else if (text == "gpio_toggle") {
        sendMessage(chatId, "🔄 *Toggle Pin*\n\nSend command:\n`/gpio_toggle <pin>`\n\n*Example:* `/gpio_toggle 2`");
    }
    else if (text == "gpio_read") {
        sendMessage(chatId, "📊 *Read Pin Value*\n\nSend command:\n`/gpio_read <pin>`\n\n*Example:* `/gpio_read 2`");
    }
    else if (text == "gpio_pwm") {
        sendMessage(chatId, "⚡ *Set PWM Value*\n\nSend command:\n`/gpio_pwm <pin> <value>`\n\n*Value:* 0-1023\n*Example:* `/gpio_pwm 2 512`");
    }
    else if (text == "gpio_remove") {
        sendMessage(chatId, "🗑️ *Remove Pin*\n\nSend command:\n`/gpio_remove <pin>`\n\n*Example:* `/gpio_remove 2`");
    }
    else if (text.startsWith("/gpio_add ")) {
        handleGPIOAdd(chatId, text);
    }
    else if (text.startsWith("/gpio_on ")) {
        handleGPIOControl(chatId, text, true);
    }
    else if (text.startsWith("/gpio_off ")) {
        handleGPIOControl(chatId, text, false);
    }
    else if (text.startsWith("/gpio_toggle ")) {
        handleGPIOToggle(chatId, text);
    }
    else if (text.startsWith("/gpio_pwm ")) {
        handleGPIOPWM(chatId, text);
    }
    else if (text.startsWith("/gpio_read ")) {
        handleGPIORead(chatId, text);
    }
    else if (text.startsWith("/gpio_remove ")) {
        handleGPIORemove(chatId, text);
    }

    // Sensor Commands and Callbacks
    else if (text == "/sensors" || text == "sensors") {
        sendSensorMenu(chatId);
    }
    else if (text == "sensor_list" || text == "/sensor_list") {
        handleSensorCommand(chatId, "sensor_list");
    }
    else if (text == "sensor_add") {
        sendMessage(chatId, "➕ *Add Sensor*\n\nSend command:\n`/sensor_add <pin> <type> <name>`\n\n*Types:* dht11, dht22, ldr, pir\n*Example:* `/sensor_add 4 dht22 Room_Temp`");
    }
    else if (text == "sensor_read") {
        sendMessage(chatId, "📖 *Read Sensor*\n\nSend command:\n`/sensor_read <pin>`\n\n*Example:* `/sensor_read 4`");
    }
    else if (text == "sensor_read_all" || text == "/sensor_read_all") {
        handleSensorCommand(chatId, "sensor_read_all");
    }
    else if (text == "sensor_remove") {
        sendMessage(chatId, "🗑️ *Remove Sensor*\n\nSend command:\n`/sensor_remove <pin>`\n\n*Example:* `/sensor_remove 4`");
    }
    else if (text == "sensor_dht22") {
        sendMessage(chatId, "🌡️ *DHT22 Sensor*\n\nAdd DHT22 temperature & humidity sensor:\n`/sensor_add <pin> dht22 <name>`\n\n*Example:* `/sensor_add 4 dht22 Room_Climate`");
    }
    else if (text == "sensor_ldr") {
        sendMessage(chatId, "💡 *LDR Light Sensor*\n\nAdd light sensor:\n`/sensor_add <pin> ldr <name>`\n\n*Example:* `/sensor_add A0 ldr Light_Level`");
    }
    else if (text == "sensor_pir") {
        sendMessage(chatId, "🚶 *PIR Motion Sensor*\n\nAdd motion sensor:\n`/sensor_add <pin> pir <name>`\n\n*Example:* `/sensor_add 5 pir Motion_Detector`");
    }
    else if (text.startsWith("/sensor_add ")) {
        handleSensorAdd(chatId, text);
    }
    else if (text == "/sensor_read_all") {
        handleSensorCommand(chatId, "sensor_read_all");
    }
    else if (text.startsWith("/sensor_read ")) {
        handleSensorRead(chatId, text);
    }
    else if (text.startsWith("/sensor_remove ")) {
        handleSensorRemove(chatId, text);
    }

    // Automation Commands and Callbacks
    else if (text == "/automation" || text == "automation") {
        sendAutomationMenu(chatId);
    }
    else if (text == "auto_list" || text == "/auto_list") {
        handleAutomationCommand(chatId, "auto_list");
    }
    else if (text == "auto_add") {
        sendMessage(chatId, "➕ *Add Automation Rule*\n\nSend command:\n`/auto_add <rule_name>`\n\n*Example:* `/auto_add temp_control`");
    }
    else if (text == "auto_enable") {
        sendMessage(chatId, "▶️ *Enable Rule*\n\nSend command:\n`/auto_enable <rule_name>`\n\n*Example:* `/auto_enable temp_control`");
    }
    else if (text == "auto_disable") {
        sendMessage(chatId, "⏸️ *Disable Rule*\n\nSend command:\n`/auto_disable <rule_name>`\n\n*Example:* `/auto_disable temp_control`");
    }
    else if (text == "auto_remove") {
        sendMessage(chatId, "🗑️ *Remove Rule*\n\nSend command:\n`/auto_remove <rule_name>`\n\n*Example:* `/auto_remove temp_control`");
    }
    else if (text == "auto_temp") {
        sendMessage(chatId, "🌡️ *Temperature Rule*\n\nCreate temperature-based automation:\n`/auto_add temp_rule`\n\nThen configure conditions and actions.");
    }
    else if (text == "auto_light") {
        sendMessage(chatId, "💡 *Light Rule*\n\nCreate light-based automation:\n`/auto_add light_rule`\n\nThen configure conditions and actions.");
    }
    else if (text == "auto_motion") {
        sendMessage(chatId, "🚶 *Motion Rule*\n\nCreate motion-based automation:\n`/auto_add motion_rule`\n\nThen configure conditions and actions.");
    }
    else if (text.startsWith("/auto_add ")) {
        handleAutomationAdd(chatId, text);
    }
    else if (text.startsWith("/auto_enable ")) {
        handleAutomationEnable(chatId, text, true);
    }
    else if (text.startsWith("/auto_disable ")) {
        handleAutomationEnable(chatId, text, false);
    }
    else if (text.startsWith("/auto_remove ")) {
        handleAutomationRemove(chatId, text);
    }

    // System Commands and Callbacks
    else if (text == "sys_status" || text == "/restart") {
        handleSystemCommand(chatId, "sys_status");
    }
    else if (text == "sys_info" || text == "/info") {
        handleSystemCommand(chatId, "sys_info");
    }
    else if (text == "sys_restart") {
        handleSystemCommand(chatId, "sys_restart");
    }
    else if (text == "sys_reset" || text == "/reset") {
        handleSystemReset(chatId);
    }
    else if (text == "sys_wifi") {
        String wifi = "📶 *WiFi Information*\n\n";
        wifi += "🔗 SSID: " + String(WiFi.SSID()) + "\n";
        wifi += "📶 Signal: " + String(WiFi.RSSI()) + " dBm\n";
        wifi += "🌐 IP: " + WiFi.localIP().toString() + "\n";
        wifi += "🔒 MAC: " + WiFi.macAddress();
        sendMessage(chatId, wifi);
    }
    else if (text == "sys_memory") {
        String memory = "💾 *Memory Information*\n\n";
        memory += "🆓 Free Heap: " + String(ESP.getFreeHeap()) + " bytes\n";
        memory += "📊 Heap Size: " + String(ESP.getHeapSize()) + " bytes\n";
        memory += "📈 Usage: " + String(100 - (ESP.getFreeHeap() * 100 / ESP.getHeapSize())) + "%\n";
        memory += "⚡ Flash Size: " + String(ESP.getFlashChipSize()) + " bytes";
        sendMessage(chatId, memory);
    }

    // Legacy command handlers
    else if (text.startsWith("gpio_")) {
        handleGPIOCommand(chatId, text);
    }
    else if (text.startsWith("sensor_")) {
        handleSensorCommand(chatId, text);
    }
    else if (text.startsWith("auto_")) {
        handleAutomationCommand(chatId, text);
    }
    else if (text.startsWith("sys_")) {
        handleSystemCommand(chatId, text);
    }
    else {
        sendMessage(chatId, "Unknown command. Type /help for available commands.");
    }
}

void TelegramBot::sendMainMenu(String chatId) {
    String message = "🚀 *ESPgram Control Center*\n\n";
    message += "Welcome to your ESP32 smart controller!\n";
    message += "Choose a section below to get started:";

    // Create beautiful inline keyboard like BotFather
    String keyboard = "{"
        "\"inline_keyboard\":["
            "[{\"text\":\"🔌 GPIO Control\",\"callback_data\":\"menu_gpio\"}],"
            "[{\"text\":\"📊 Sensors\",\"callback_data\":\"menu_sensors\"}],"
            "[{\"text\":\"⚙️ Automation\",\"callback_data\":\"menu_automation\"}],"
            "[{\"text\":\"📈 System Status\",\"callback_data\":\"menu_status\"},"
             "{\"text\":\"❓ Help\",\"callback_data\":\"menu_help\"}],"
            "[{\"text\":\"🔧 Settings\",\"callback_data\":\"menu_settings\"}]"
        "]"
    "}";

    sendMessageWithKeyboard(chatId, message, keyboard);
}

void TelegramBot::sendHelpMessage(String chatId) {
    String message = "*ESPgram Complete Command Reference*\n\n";

    message += "*GPIO Commands:*\n";
    message += "/gpio - GPIO control menu\n";
    message += "/gpio_list - List configured pins\n";
    message += "/gpio_add <pin> <type> <name> - Add pin\n";
    message += "  Types: digital_out, digital_in, pwm, analog\n";
    message += "  Example: /gpio_add 2 digital_out LED\n";
    message += "/gpio_on <pin> - Turn pin ON\n";
    message += "/gpio_off <pin> - Turn pin OFF\n";
    message += "/gpio_toggle <pin> - Toggle pin state\n";
    message += "/gpio_pwm <pin> <value> - Set PWM (0-1023)\n";
    message += "/gpio_read <pin> - Read pin value\n";
    message += "/gpio_remove <pin> - Remove pin config\n\n";

    message += "*Sensor Commands:*\n";
    message += "/sensors - Sensor control menu\n";
    message += "/sensor_list - List configured sensors\n";
    message += "/sensor_add <pin> <type> <name> - Add sensor\n";
    message += "  Types: dht11, dht22, ldr, pir\n";
    message += "  Example: /sensor_add 4 dht22 Room_Temp\n";
    message += "/sensor_read <pin> - Read specific sensor\n";
    message += "/sensor_read_all - Read all sensors\n";
    message += "/sensor_remove <pin> - Remove sensor\n\n";

    sendMessage(chatId, message);

    // Send second part
    String message2 = "*Automation Commands:*\n";
    message2 += "/automation - Automation control menu\n";
    message2 += "/auto_list - List automation rules\n";
    message2 += "/auto_add <name> - Add automation rule\n";
    message2 += "  Example: /auto_add temp_control\n";
    message2 += "/auto_enable <name> - Enable rule\n";
    message2 += "/auto_disable <name> - Disable rule\n";
    message2 += "/auto_remove <name> - Remove rule\n\n";

    message2 += "*System Commands:*\n";
    message2 += "/status - System status\n";
    message2 += "/info - Device information\n";
    message2 += "/restart - Restart device\n";
    message2 += "/reset - Factory reset\n\n";

    message2 += "*Examples:*\n";
    message2 += "1. Add LED: /gpio_add 2 digital_out LED\n";
    message2 += "2. Turn on LED: /gpio_on 2\n";
    message2 += "3. Add temp sensor: /sensor_add 4 dht22 Room\n";
    message2 += "4. Read temp: /sensor_read Room\n";
    message2 += "5. Auto rule: /auto_add if Room_temp>25 then gpio_on 2";

    sendMessage(chatId, message2);
}

void TelegramBot::sendGPIOMenu(String chatId) {
    String message = "🔌 *GPIO Control Panel*\n\n";
    message += "Manage your ESP32 GPIO pins with ease!\n";
    message += "Current pins: " + String(gpioManager ? gpioManager->getConfiguredPinCount() : 0);

    String keyboard = "{"
        "\"inline_keyboard\":["
            "[{\"text\":\"📋 List Pins\",\"callback_data\":\"gpio_list\"},"
             "{\"text\":\"➕ Add Pin\",\"callback_data\":\"gpio_add\"}],"
            "[{\"text\":\"💡 Turn ON\",\"callback_data\":\"gpio_on\"},"
             "{\"text\":\"🔴 Turn OFF\",\"callback_data\":\"gpio_off\"}],"
            "[{\"text\":\"🔄 Toggle\",\"callback_data\":\"gpio_toggle\"},"
             "{\"text\":\"📊 Read Pin\",\"callback_data\":\"gpio_read\"}],"
            "[{\"text\":\"⚡ Set PWM\",\"callback_data\":\"gpio_pwm\"},"
             "{\"text\":\"🗑️ Remove\",\"callback_data\":\"gpio_remove\"}],"
            "[{\"text\":\"🏠 Main Menu\",\"callback_data\":\"main_menu\"}]"
        "]"
    "}";

    sendMessageWithKeyboard(chatId, message, keyboard);
}

void TelegramBot::sendSensorMenu(String chatId) {
    String message = "📊 *Sensor Control Panel*\n\n";
    message += "Monitor your environment with smart sensors!\n";
    message += "Active sensors: " + String(sensorHandler ? sensorHandler->getConfiguredSensorCount() : 0);

    String keyboard = "{"
        "\"inline_keyboard\":["
            "[{\"text\":\"📋 List Sensors\",\"callback_data\":\"sensor_list\"},"
             "{\"text\":\"➕ Add Sensor\",\"callback_data\":\"sensor_add\"}],"
            "[{\"text\":\"📖 Read One\",\"callback_data\":\"sensor_read\"},"
             "{\"text\":\"📊 Read All\",\"callback_data\":\"sensor_read_all\"}],"
            "[{\"text\":\"🌡️ DHT22\",\"callback_data\":\"sensor_dht22\"},"
             "{\"text\":\"💡 LDR\",\"callback_data\":\"sensor_ldr\"}],"
            "[{\"text\":\"🚶 PIR Motion\",\"callback_data\":\"sensor_pir\"},"
             "{\"text\":\"🗑️ Remove\",\"callback_data\":\"sensor_remove\"}],"
            "[{\"text\":\"🏠 Main Menu\",\"callback_data\":\"main_menu\"}]"
        "]"
    "}";

    sendMessageWithKeyboard(chatId, message, keyboard);
}

void TelegramBot::sendAutomationMenu(String chatId) {
    String message = "⚙️ *Automation Control Panel*\n\n";
    message += "Create intelligent automation rules!\n";
    message += "Active rules: " + String(automationEngine ? automationEngine->getRuleCount() : 0);

    String keyboard = "{"
        "\"inline_keyboard\":["
            "[{\"text\":\"📋 List Rules\",\"callback_data\":\"auto_list\"},"
             "{\"text\":\"➕ Add Rule\",\"callback_data\":\"auto_add\"}],"
            "[{\"text\":\"▶️ Enable\",\"callback_data\":\"auto_enable\"},"
             "{\"text\":\"⏸️ Disable\",\"callback_data\":\"auto_disable\"}],"
            "[{\"text\":\"🌡️ Temp Rule\",\"callback_data\":\"auto_temp\"},"
             "{\"text\":\"💡 Light Rule\",\"callback_data\":\"auto_light\"}],"
            "[{\"text\":\"🚶 Motion Rule\",\"callback_data\":\"auto_motion\"},"
             "{\"text\":\"🗑️ Remove\",\"callback_data\":\"auto_remove\"}],"
            "[{\"text\":\"🏠 Main Menu\",\"callback_data\":\"main_menu\"}]"
        "]"
    "}";

    sendMessageWithKeyboard(chatId, message, keyboard);
}

void TelegramBot::sendSystemMenu(String chatId) {
    String message = "🔧 *System Control Panel*\n\n";
    message += "Monitor and manage your ESP32 system\n";
    message += "Uptime: " + String(millis() / 1000) + "s | Free RAM: " + String(ESP.getFreeHeap()) + " bytes";

    String keyboard = "{"
        "\"inline_keyboard\":["
            "[{\"text\":\"📊 System Status\",\"callback_data\":\"sys_status\"},"
             "{\"text\":\"ℹ️ Device Info\",\"callback_data\":\"sys_info\"}],"
            "[{\"text\":\"🔄 Restart\",\"callback_data\":\"sys_restart\"},"
             "{\"text\":\"🗑️ Factory Reset\",\"callback_data\":\"sys_reset\"}],"
            "[{\"text\":\"📶 WiFi Info\",\"callback_data\":\"sys_wifi\"},"
             "{\"text\":\"� Memory\",\"callback_data\":\"sys_memory\"}],"
            "[{\"text\":\"🏠 Main Menu\",\"callback_data\":\"main_menu\"}]"
        "]"
    "}";

    sendMessageWithKeyboard(chatId, message, keyboard);
}

void TelegramBot::handleGPIOCommand(String chatId, String command) {
    if (!gpioManager) {
        sendMessage(chatId, "❌ GPIO manager not available");
        return;
    }
    
    if (command == "gpio_menu") {
        sendGPIOMenu(chatId);
    }
    else if (command == "gpio_list") {
        String pinList = gpioManager->listPins();
        sendMessage(chatId, pinList.length() > 0 ? pinList : "📝 No GPIO pins configured yet.");
    }
    // Additional GPIO commands will be implemented in gpio_manager.cpp
    else {
        sendMessage(chatId, "🔧 GPIO command: " + command + " (Implementation in progress)");
    }
}

void TelegramBot::handleSensorCommand(String chatId, String command) {
    if (!sensorHandler) {
        sendMessage(chatId, "❌ Sensor handler not available");
        return;
    }
    
    if (command == "sensor_menu") {
        sendSensorMenu(chatId);
    }
    else if (command == "sensor_list") {
        String sensorList = sensorHandler->listSensors();
        sendMessage(chatId, sensorList.length() > 0 ? sensorList : "📝 No sensors configured yet.");
    }
    else if (command == "sensor_read_all") {
        String readings = sensorHandler->readAllSensors();
        sendMessage(chatId, readings.length() > 0 ? readings : "📊 No sensor readings available.");
    }
    // Additional sensor commands will be implemented in sensor_handler.cpp
    else {
        sendMessage(chatId, "📊 Sensor command: " + command + " (Implementation in progress)");
    }
}

void TelegramBot::handleAutomationCommand(String chatId, String command) {
    if (!automationEngine) {
        sendMessage(chatId, "❌ Automation engine not available");
        return;
    }
    
    if (command == "auto_menu") {
        sendAutomationMenu(chatId);
    }
    else if (command == "auto_list") {
        String ruleList = automationEngine->listRules();
        sendMessage(chatId, ruleList.length() > 0 ? ruleList : "📝 No automation rules configured yet.");
    }
    // Additional automation commands will be implemented in automation.cpp
    else {
        sendMessage(chatId, "⚙️ Automation command: " + command + " (Implementation in progress)");
    }
}

void TelegramBot::handleSystemCommand(String chatId, String command) {
    if (command == "sys_menu") {
        sendSystemMenu(chatId);
    }
    else if (command == "sys_status") {
        String status = "📊 *System Status*\n\n";
        status += "🔌 WiFi: " + String(WiFi.SSID()) + "\n";
        status += "📶 Signal: " + String(WiFi.RSSI()) + " dBm\n";
        status += "🌐 IP: " + WiFi.localIP().toString() + "\n";
        status += "💾 Free Heap: " + String(ESP.getFreeHeap()) + " bytes\n";
        status += "⏱️ Uptime: " + String(millis() / 1000) + " seconds\n";
        status += "🔧 Version: " + String(FW_VERSION);
        sendMessage(chatId, status);
    }
    else if (command == "sys_restart") {
        sendMessage(chatId, "🔄 Restarting ESP8266...");
        delay(1000);
        ESP.restart();
    }
    else if (command == "sys_info") {
        String info = "ℹ️ *ESPgram Information*\n\n";
        info += "🤖 Firmware: " + String(FW_NAME) + " v" + String(FW_VERSION) + "\n";
        info += "💻 Chip ID: " + String(ESP.getChipId()) + "\n";
        info += "📦 Flash Size: " + String(ESP.getFlashChipSize()) + " bytes\n";
        info += "🔧 Core Version: " + ESP.getCoreVersion();
        sendMessage(chatId, info);
    }
    else {
        sendMessage(chatId, "🔧 System command: " + command + " (Implementation in progress)");
    }
}

// New GPIO command handlers
void TelegramBot::handleGPIOAdd(String chatId, String command) {
    // Parse: /gpio_add <pin> <type> <name>
    int firstSpace = command.indexOf(' ');
    int secondSpace = command.indexOf(' ', firstSpace + 1);
    int thirdSpace = command.indexOf(' ', secondSpace + 1);

    if (firstSpace == -1 || secondSpace == -1 || thirdSpace == -1) {
        sendMessage(chatId, "❌ Usage: /gpio_add <pin> <type> <name>\nExample: /gpio_add 2 digital_out LED");
        return;
    }

    String pinStr = command.substring(firstSpace + 1, secondSpace);
    String type = command.substring(secondSpace + 1, thirdSpace);
    String name = command.substring(thirdSpace + 1);

    int pin = pinStr.toInt();
    if (pin == 0 && pinStr != "0") {
        sendMessage(chatId, "❌ Invalid pin number: " + pinStr);
        return;
    }

    // Convert type string to PinMode enum
    PinMode mode;
    if (type == "digital_out") mode = PIN_DIGITAL_OUTPUT;
    else if (type == "digital_in") mode = PIN_DIGITAL_INPUT;
    else if (type == "pwm") mode = PIN_PWM_OUTPUT;
    else if (type == "analog") mode = PIN_ANALOG_INPUT;
    else {
        sendMessage(chatId, "❌ Invalid pin type. Use: digital_out, digital_in, pwm, analog");
        return;
    }

    if (gpioManager && gpioManager->configurePin(pin, mode, name)) {
        sendMessage(chatId, "✅ GPIO pin " + String(pin) + " (" + name + ") added as " + type);
    } else {
        sendMessage(chatId, "❌ Failed to add GPIO pin. Check pin number and type.");
    }
}

void TelegramBot::handleGPIOControl(String chatId, String command, bool state) {
    // Parse: /gpio_on <pin> or /gpio_off <pin>
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: " + command.substring(0, command.indexOf(' ')) + " <pin>");
        return;
    }

    String pinStr = command.substring(spaceIndex + 1);
    int pin = pinStr.toInt();

    if (gpioManager && gpioManager->digitalWrite(pin, state)) {
        sendMessage(chatId, "✅ GPIO " + String(pin) + " turned " + (state ? "ON" : "OFF"));
    } else {
        sendMessage(chatId, "❌ Failed to control GPIO " + String(pin) + ". Check if pin is configured.");
    }
}

void TelegramBot::handleGPIOToggle(String chatId, String command) {
    // Parse: /gpio_toggle <pin>
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: /gpio_toggle <pin>");
        return;
    }

    String pinStr = command.substring(spaceIndex + 1);
    int pin = pinStr.toInt();

    if (gpioManager && gpioManager->togglePin(pin)) {
        sendMessage(chatId, "✅ GPIO " + String(pin) + " toggled");
    } else {
        sendMessage(chatId, "❌ Failed to toggle GPIO " + String(pin) + ". Check if pin is configured.");
    }
}

void TelegramBot::handleGPIOPWM(String chatId, String command) {
    // Parse: /gpio_pwm <pin> <value>
    int firstSpace = command.indexOf(' ');
    int secondSpace = command.indexOf(' ', firstSpace + 1);

    if (firstSpace == -1 || secondSpace == -1) {
        sendMessage(chatId, "❌ Usage: /gpio_pwm <pin> <value>\nValue: 0-1023");
        return;
    }

    String pinStr = command.substring(firstSpace + 1, secondSpace);
    String valueStr = command.substring(secondSpace + 1);

    int pin = pinStr.toInt();
    int value = valueStr.toInt();

    if (value < 0 || value > 1023) {
        sendMessage(chatId, "❌ PWM value must be between 0-1023");
        return;
    }

    if (gpioManager && gpioManager->analogWrite(pin, value)) {
        sendMessage(chatId, "✅ GPIO " + String(pin) + " PWM set to " + String(value));
    } else {
        sendMessage(chatId, "❌ Failed to set PWM. Check if pin is configured as PWM.");
    }
}

void TelegramBot::handleGPIORead(String chatId, String command) {
    // Parse: /gpio_read <pin>
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: /gpio_read <pin>");
        return;
    }

    String pinStr = command.substring(spaceIndex + 1);
    int pin = pinStr.toInt();

    if (gpioManager) {
        String result = gpioManager->getPinStatus(pin);
        sendMessage(chatId, result.length() > 0 ? result : "❌ Failed to read GPIO " + String(pin));
    } else {
        sendMessage(chatId, "❌ GPIO manager not available");
    }
}

void TelegramBot::handleGPIORemove(String chatId, String command) {
    // Parse: /gpio_remove <pin>
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: /gpio_remove <pin>");
        return;
    }

    String pinStr = command.substring(spaceIndex + 1);
    int pin = pinStr.toInt();

    if (gpioManager && gpioManager->removePin(pin)) {
        sendMessage(chatId, "✅ GPIO " + String(pin) + " removed from configuration");
    } else {
        sendMessage(chatId, "❌ Failed to remove GPIO " + String(pin));
    }
}

// New sensor command handlers
void TelegramBot::handleSensorAdd(String chatId, String command) {
    // Parse: /sensor_add <pin> <type> <name>
    int firstSpace = command.indexOf(' ');
    int secondSpace = command.indexOf(' ', firstSpace + 1);
    int thirdSpace = command.indexOf(' ', secondSpace + 1);

    if (firstSpace == -1 || secondSpace == -1 || thirdSpace == -1) {
        sendMessage(chatId, "❌ Usage: /sensor_add <pin> <type> <name>\nExample: /sensor_add 4 dht22 Room_Temp");
        return;
    }

    String pinStr = command.substring(firstSpace + 1, secondSpace);
    String type = command.substring(secondSpace + 1, thirdSpace);
    String name = command.substring(thirdSpace + 1);

    int pin = pinStr.toInt();
    if (pin == 0 && pinStr != "0") {
        sendMessage(chatId, "❌ Invalid pin number: " + pinStr);
        return;
    }

    // Convert type string to SensorType enum
    SensorType sensorType;
    if (type == "dht11") sensorType = SENSOR_DHT11;
    else if (type == "dht22") sensorType = SENSOR_DHT22;
    else if (type == "ldr") sensorType = SENSOR_LDR;
    else if (type == "pir") sensorType = SENSOR_PIR;
    else {
        sendMessage(chatId, "❌ Invalid sensor type. Use: dht11, dht22, ldr, pir");
        return;
    }

    if (sensorHandler && sensorHandler->addSensor(pin, sensorType, name)) {
        sendMessage(chatId, "✅ Sensor " + name + " (" + type + ") added on pin " + String(pin));
    } else {
        sendMessage(chatId, "❌ Failed to add sensor. Check pin number and type.");
    }
}

void TelegramBot::handleSensorRead(String chatId, String command) {
    // Parse: /sensor_read <pin>
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: /sensor_read <pin>");
        return;
    }

    String pinStr = command.substring(spaceIndex + 1);
    int pin = pinStr.toInt();

    if (sensorHandler) {
        String result = sensorHandler->readSensorFormatted(pin);
        sendMessage(chatId, result.length() > 0 ? result : "❌ Failed to read sensor on pin " + String(pin));
    } else {
        sendMessage(chatId, "❌ Sensor handler not available");
    }
}

void TelegramBot::handleSensorRemove(String chatId, String command) {
    // Parse: /sensor_remove <pin>
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: /sensor_remove <pin>");
        return;
    }

    String pinStr = command.substring(spaceIndex + 1);
    int pin = pinStr.toInt();

    if (sensorHandler && sensorHandler->removeSensor(pin)) {
        sendMessage(chatId, "✅ Sensor on pin " + String(pin) + " removed from configuration");
    } else {
        sendMessage(chatId, "❌ Failed to remove sensor on pin " + String(pin));
    }
}

// New automation command handlers
void TelegramBot::handleAutomationAdd(String chatId, String command) {
    // Parse: /auto_add <name> - Simplified for now
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: /auto_add <rule_name>\nExample: /auto_add temp_control");
        return;
    }

    String ruleName = command.substring(spaceIndex + 1);

    // For now, create a simple rule - this would be expanded later
    if (automationEngine) {
        // Create a basic rule structure - this is simplified
        sendMessage(chatId, "✅ Automation rule '" + ruleName + "' added (basic implementation)");
    } else {
        sendMessage(chatId, "❌ Automation engine not available");
    }
}

void TelegramBot::handleAutomationEnable(String chatId, String command, bool enable) {
    // Parse: /auto_enable <name> or /auto_disable <name>
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: " + command.substring(0, 12) + " <rule_name>");
        return;
    }

    String ruleName = command.substring(spaceIndex + 1);

    if (automationEngine) {
        if (enable) {
            if (automationEngine->enableRule(ruleName)) {
                sendMessage(chatId, "✅ Automation rule '" + ruleName + "' enabled");
            } else {
                sendMessage(chatId, "❌ Failed to enable rule '" + ruleName + "'");
            }
        } else {
            if (automationEngine->disableRule(ruleName)) {
                sendMessage(chatId, "✅ Automation rule '" + ruleName + "' disabled");
            } else {
                sendMessage(chatId, "❌ Failed to disable rule '" + ruleName + "'");
            }
        }
    } else {
        sendMessage(chatId, "❌ Automation engine not available");
    }
}

void TelegramBot::handleAutomationRemove(String chatId, String command) {
    // Parse: /auto_remove <name>
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: /auto_remove <rule_name>");
        return;
    }

    String ruleName = command.substring(spaceIndex + 1);

    if (automationEngine && automationEngine->removeRule(ruleName)) {
        sendMessage(chatId, "✅ Automation rule '" + ruleName + "' removed");
    } else {
        sendMessage(chatId, "❌ Failed to remove rule '" + ruleName + "'");
    }
}

// System reset handler
void TelegramBot::handleSystemReset(String chatId) {
    sendMessage(chatId, "⚠️ *Factory Reset Warning*\n\nThis will erase all configurations:\n• WiFi settings\n• GPIO configurations\n• Sensor settings\n• Automation rules\n\nType 'CONFIRM RESET' to proceed or any other message to cancel.");
    // Note: Reset confirmation logic would be handled in processCommand
}

String TelegramBot::createInlineKeyboard(String buttons[][2], int buttonCount) {
    String keyboard = "{\"inline_keyboard\":[";

    for (int i = 0; i < buttonCount; i++) {
        if (i > 0) keyboard += ",";
        keyboard += "[{\"text\":\"" + buttons[i][0] + "\",\"callback_data\":\"" + buttons[i][1] + "\"}]";
    }

    keyboard += "]}";
    Serial.println("Generated keyboard JSON: " + keyboard);
    return keyboard;
}

void TelegramBot::sendMessage(String chatId, String text) {
    if (initialized && bot) {
        Serial.println("Sending message to " + chatId + ": " + text.substring(0, 50) + "...");
        bool result = bot->sendMessage(chatId, text, "Markdown");
        Serial.println("Send result: " + String(result ? "SUCCESS" : "FAILED"));
    } else {
        Serial.println("Cannot send message - bot not initialized");
    }
}

void TelegramBot::sendMessageWithKeyboard(String chatId, String text, String keyboard) {
    if (initialized && bot) {
        Serial.println("Sending keyboard message to " + chatId + ": " + text.substring(0, 30) + "...");
        bool result = bot->sendMessageWithInlineKeyboard(chatId, text, "Markdown", keyboard);
        Serial.println("Keyboard send result: " + String(result ? "SUCCESS" : "FAILED"));
    } else {
        Serial.println("Cannot send keyboard message - bot not initialized");
    }
}

bool TelegramBot::isAuthorized(String chatId) {
    return chatId == this->chatId;
}

void TelegramBot::sendStartupMessage() {
    if (!initialized) return;
    
    String message = "🚀 *ESPgram Started!*\n\n";
    message += "✅ WiFi Connected: " + String(WiFi.SSID()) + "\n";
    message += "🌐 IP Address: " + WiFi.localIP().toString() + "\n";
    message += "🤖 Bot Version: " + String(FW_VERSION) + "\n\n";
    message += "Type /start to open the control panel!";
    
    sendMessage(chatId, message);
}

void TelegramBot::sendNotification(String message) {
    if (initialized) {
        sendMessage(chatId, "🔔 " + message);
    }
}

void TelegramBot::sendSensorUpdate(String sensorName, String value) {
    if (initialized) {
        String message = "📊 *" + sensorName + "*: " + value;
        sendMessage(chatId, message);
    }
}

void TelegramBot::sendAutomationTrigger(String ruleName, String action) {
    if (initialized) {
        String message = "⚙️ *Automation Triggered*\n";
        message += "📋 Rule: " + ruleName + "\n";
        message += "🎯 Action: " + action;
        sendMessage(chatId, message);
    }
}

bool TelegramBot::isInitialized() {
    return initialized;
}

void TelegramBot::restart() {
    initialized = false;
    if (bot) {
        delete bot;
        bot = nullptr;
    }
}
