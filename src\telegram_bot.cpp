#include "telegram_bot.h"
#include "gpio_manager.h"
#include "sensor_handler.h"
#include "automation.h"

TelegramBot::TelegramBot() : bot(nullptr), lastBotRan(0), initialized(false),
                             gpioManager(nullptr), sensor<PERSON>andler(nullptr), automationEngine(nullptr) {
    client.setInsecure(); // For simplicity, use insecure connection
}

TelegramBot::~TelegramBot() {
    if (bot) {
        delete bot;
    }
}

bool TelegramBot::begin(const String& token, const String& chat_id) {
    botToken = token;
    chatID = chat_id;
    
    if (botToken.length() == 0 || chatID.length() == 0) {
        DEBUG_PRINTLN("TelegramBot: Invalid token or chat ID");
        return false;
    }
    
    // Initialize bot
    bot = new UniversalTelegramBot(botToken, client);
    bot->longPoll = BOT_LONG_POLL_TIMEOUT;
    
    DEBUG_PRINTLN("TelegramBot: Initializing...");
    
    // Test connection
    String botName = getBotUsername();
    if (botName.length() > 0) {
        DEBUG_PRINTF("TelegramBot: Connected as @%s\n", botName.c_str());
        initialized = true;
        return true;
    } else {
        DEBUG_PRINTLN("TelegramBot: Failed to connect");
        return false;
    }
}

void TelegramBot::setModuleReferences(GPIOManager* gpio, SensorHandler* sensor, AutomationEngine* automation) {
    gpioManager = gpio;
    sensorHandler = sensor;
    automationEngine = automation;
}

void TelegramBot::loop() {
    if (!initialized || !bot) {
        return;
    }
    
    if (millis() - lastBotRan > BOT_CHECK_INTERVAL) {
        int numNewMessages = bot->getUpdates(bot->last_message_received + 1);
        
        while (numNewMessages) {
            DEBUG_PRINTF("TelegramBot: Processing %d new messages\n", numNewMessages);
            handleNewMessages(numNewMessages);
            numNewMessages = bot->getUpdates(bot->last_message_received + 1);
        }
        
        lastBotRan = millis();
    }
}

void TelegramBot::handleNewMessages(int numNewMessages) {
    for (int i = 0; i < numNewMessages; i++) {
        String chatId = String(bot->messages[i].chat_id);
        String text = bot->messages[i].text;
        String fromName = bot->messages[i].from_name;
        
        // Check for callback query (inline keyboard button press)
        if (bot->messages[i].type == "callback_query") {
            handleCallbackQuery(bot->messages[i].text, chatId, bot->messages[i].text);
        } else {
            handleMessage(chatId, text, fromName);
        }
    }
}

void TelegramBot::handleMessage(String chatId, String text, String fromName) {
    DEBUG_PRINTF("TelegramBot: Message from %s (%s): %s\n", fromName.c_str(), chatId.c_str(), text.c_str());

    // Check authorization
    if (!isAuthorizedUser(chatId)) {
        sendMessage(chatId, "❌ Unauthorized access. This bot is configured for a specific user only.");
        return;
    }

    // Send typing indicator
    sendTypingAction(chatId);

    text.trim();
    String originalText = text;
    text.toLowerCase();

    // Extract command and arguments
    int spaceIndex = text.indexOf(' ');
    String command = (spaceIndex > 0) ? text.substring(0, spaceIndex) : text;
    String args = (spaceIndex > 0) ? originalText.substring(spaceIndex + 1) : "";

    // Process commands
    if (command == "/start" || command == "start") {
        processStartCommand(chatId);
    } else if (command == "/help" || command == "help" || command == "?") {
        DEBUG_PRINTLN("Processing help command");
        processHelpCommand(chatId);
    } else if (command == "/info" || command == "/status" || command == "status") {
        processStatusCommand(chatId);
    }
    // Quick Actions
    else if (command == "/on") {
        String result = processOnOffCommand(chatId, args, true);
        sendMessage(chatId, result);
    } else if (command == "/off") {
        String result = processOnOffCommand(chatId, args, false);
        sendMessage(chatId, result);
    } else if (command == "/read") {
        String result = processReadCommand(chatId, args);
        sendMessage(chatId, result);
    } else if (command == "/temp") {
        String result = processTempCommand(chatId);
        sendMessage(chatId, result);
    } else if (command == "/add") {
        String result = processAddCommand(chatId, args);
        sendMessage(chatId, result);
    }
    // Legacy commands (short versions)
    else if (command == "/pins" || command == "/gpio" || command == "gpio") {
        processGPIOCommand(chatId, args);
    } else if (command == "/sensors" || command == "/sensor" || command == "sensor") {
        processSensorCommand(chatId, args);
    } else if (command == "/rules" || command == "/automation" || command == "automation") {
        processAutomationCommand(chatId, args);
    } else if (command == "/sys" || command == "/system" || command == "system") {
        processSystemCommand(chatId, args);
    } else {
        String response = "❓ Unknown command: `" + command + "`\n\n";
        response += "💡 *Quick Commands:*\n";
        response += "• `/on 2` - Turn pin 2 ON\n";
        response += "• `/off 2` - Turn pin 2 OFF\n";
        response += "• `/read 34` - Read pin 34\n";
        response += "• `/temp` - Read temperature\n";
        response += "• `/add led 2` - Add LED\n\n";
        response += "Type `/help` for full command list.";
        sendMessage(chatId, response);
    }
}

void TelegramBot::handleCallbackQuery(String queryId, String chatId, String data) {
    DEBUG_PRINTF("TelegramBot: Callback query: %s\n", data.c_str());
    
    // Answer the callback query to remove loading state
    bot->answerCallbackQuery(queryId);
    
    // Process the callback data
    if (data.startsWith("gpio_")) {
        processGPIOCommand(chatId, data.substring(5));
    }
    else if (data.startsWith("sensor_")) {
        processSensorCommand(chatId, data.substring(7));
    }
    else if (data.startsWith("auto_")) {
        processAutomationCommand(chatId, data.substring(5));
    }
    else if (data.startsWith("sys_")) {
        processSystemCommand(chatId, data.substring(4));
    }
}

void TelegramBot::processStartCommand(String chatId) {
    String welcome = "🚀 *ESPgram v" + String(FW_VERSION) + " Ready!*\n\n";
    welcome += "🎯 *Quick Start Menu:*\n";
    welcome += "• `/pins` - GPIO control\n";
    welcome += "• `/sensors` - Sensor management\n";
    welcome += "• `/rules` - Automation rules\n";
    welcome += "• `/info` - System status\n\n";

    welcome += "⚡ *Quick Actions:*\n";
    welcome += "• `/on <pin>` - Turn pin ON\n";
    welcome += "• `/off <pin>` - Turn pin OFF\n";
    welcome += "• `/read <pin>` - Read pin value\n";
    welcome += "• `/temp` - Read temperature\n\n";

    welcome += "🔧 *Setup Examples:*\n";
    welcome += "• `/add led 2` - Add LED on pin 2\n";
    welcome += "• `/add temp 4` - Add temp sensor on pin 4\n";
    welcome += "• `/add light 34` - Add light sensor\n\n";

    welcome += "💡 *Tip:* Use the menu button below for easy access!";

    sendMessage(chatId, welcome);

    // Set bot commands menu
    setupBotCommands();
}

void TelegramBot::processHelpCommand(String chatId) {
    DEBUG_PRINTLN("TelegramBot: processHelpCommand called");

    // Simple, guaranteed-to-work help message
    String help = "📚 *ESPgram Commands*\n\n";
    help += "*⚡ Quick Actions:*\n";
    help += "• `/on 2` - Turn pin 2 ON\n";
    help += "• `/off 2` - Turn pin 2 OFF\n";
    help += "• `/read 34` - Read pin 34\n";
    help += "• `/temp` - Read temperature\n\n";

    help += "*🔧 Quick Setup:*\n";
    help += "• `/add led 2` - Add LED\n";
    help += "• `/add temp 4` - Add sensor\n\n";

    help += "*📱 Menus:*\n";
    help += "• `/pins` - GPIO control\n";
    help += "• `/sensors` - Sensors\n";
    help += "• `/rules` - Automation\n";
    help += "• `/info` - System status\n\n";

    help += "*💡 Examples:*\n";
    help += "• `/add led 2` then `/on 2`\n";
    help += "• `/add temp 4` then `/temp`\n";
    help += "• Type any command for help";

    bool result = sendMessage(chatId, help);
    DEBUG_PRINTLN("TelegramBot: Help message sent, result: " + String(result ? "SUCCESS" : "FAILED"));
}

void TelegramBot::processStatusCommand(String chatId) {
    String status = "📊 *System Status*\n\n";
    
    // System info
    status += "*🔧 System:*\n";
    status += "• Firmware: " + String(FW_NAME) + " v" + String(FW_VERSION) + "\n";
    status += "• Uptime: " + formatUptime() + "\n";
    status += "• Free Memory: " + formatMemoryInfo() + "\n\n";
    
    // WiFi info
    status += "*📶 WiFi:*\n";
    status += "• Status: ";
    status += WiFi.isConnected() ? "Connected ✅" : "Disconnected ❌";
    status += "\n";
    if (WiFi.isConnected()) {
        status += "• SSID: " + WiFi.SSID() + "\n";
        status += "• IP: " + WiFi.localIP().toString() + "\n";
        status += "• Signal: " + String(WiFi.RSSI()) + " dBm\n";
    }
    status += "\n";
    
    // Module status
    if (gpioManager) {
        status += "*🔌 GPIO:* " + String(gpioManager->getConfiguredPinCount()) + " pins configured\n";
    }
    if (sensorHandler) {
        status += "*🌡️ Sensors:* " + String(sensorHandler->getSensorCount()) + " sensors active\n";
    }
    if (automationEngine) {
        status += "*⚙️ Automations:* " + String(automationEngine->getActiveRuleCount()) + " rules active\n";
    }
    
    sendMessage(chatId, status);
}

void TelegramBot::processGPIOCommand(String chatId, String args) {
    if (!gpioManager) {
        sendMessage(chatId, "❌ GPIO manager not available");
        return;
    }

    if (args == "list" || args.length() == 0) {
        String response = "🔌 *GPIO Pin Status*\n\n";
        response += gpioManager->getStatusReport();
        response += "\n*Commands:*\n";
        response += "• `/gpio set <pin> <mode> [name]`\n";
        response += "• `/gpio write <pin> <value>`\n";
        response += "• `/gpio read <pin>`\n";
        response += "• `/gpio pwm <pin> <value>`\n";
        response += "• `/gpio toggle <pin>`\n";
        response += "• `/gpio remove <pin>`";
        sendMessage(chatId, response);
    }
    else {
        // Parse and execute GPIO command
        String result = gpioManager->executeCommand(args);
        sendMessage(chatId, result);
    }
}

void TelegramBot::processSensorCommand(String chatId, String args) {
    if (!sensorHandler) {
        sendMessage(chatId, "❌ Sensor handler not available");
        return;
    }

    if (args == "list" || args.length() == 0) {
        String response = "🌡️ *Sensor Status*\n\n";
        response += sensorHandler->getStatusReport();
        response += "\n*Commands:*\n";
        response += "• `/sensor add <type> <pin> [name]`\n";
        response += "• `/sensor read <id>`\n";
        response += "• `/sensor readall`\n";
        response += "• `/sensor remove <id>`\n";
        response += "• `/sensor threshold <id> <value>`\n";
        response += "\n*Types:* dht11, dht22, ldr, pir, ir";
        sendMessage(chatId, response);
    }
    else {
        // Parse and execute sensor command
        String result = sensorHandler->executeCommand(args);
        sendMessage(chatId, result);
    }
}

void TelegramBot::processAutomationCommand(String chatId, String args) {
    if (!automationEngine) {
        sendMessage(chatId, "❌ Automation engine not available");
        return;
    }

    if (args == "list" || args.length() == 0) {
        String response = "⚙️ *Automation Rules*\n\n";
        response += automationEngine->getStatusReport();
        response += "\n*Commands:*\n";
        response += "• `/automation add`\n";
        response += "• `/automation enable <id>`\n";
        response += "• `/automation disable <id>`\n";
        response += "• `/automation remove <id>`\n";
        response += "• `/automation trigger <id>`\n";
        response += "\n*Example:* Create rule with guided setup";
        sendMessage(chatId, response);
    }
    else {
        // Parse and execute automation command
        String result = automationEngine->executeCommand(args);
        sendMessage(chatId, result);
    }
}

void TelegramBot::processSystemCommand(String chatId, String args) {
    if (args == "restart") {
        sendMessage(chatId, "🔄 Restarting ESP32...");
        delay(1000);
        ESP.restart();
    }
    else if (args == "reset") {
        sendMessage(chatId, "⚠️ Factory reset will erase all settings. Send 'CONFIRM RESET' to proceed.");
    }
    else if (args == "CONFIRM RESET") {
        sendMessage(chatId, "🏭 Performing factory reset...");
        // Clear all preferences and restart
        Preferences prefs;
        prefs.begin(PREFS_NAMESPACE, false);
        prefs.clear();
        prefs.end();
        delay(1000);
        ESP.restart();
    }
    else if (args == "wifi") {
        String wifiInfo = "📶 *WiFi Information*\n\n";
        wifiInfo += "• Status: ";
        wifiInfo += WiFi.isConnected() ? "Connected" : "Disconnected";
        wifiInfo += "\n";
        if (WiFi.isConnected()) {
            wifiInfo += "• SSID: " + WiFi.SSID() + "\n";
            wifiInfo += "• IP Address: " + WiFi.localIP().toString() + "\n";
            wifiInfo += "• Gateway: " + WiFi.gatewayIP().toString() + "\n";
            wifiInfo += "• DNS: " + WiFi.dnsIP().toString() + "\n";
            wifiInfo += "• Signal Strength: " + String(WiFi.RSSI()) + " dBm\n";
            wifiInfo += "• MAC Address: " + WiFi.macAddress() + "\n";
        }
        sendMessage(chatId, wifiInfo);
    }
    else {
        String sysInfo = "🔧 *System Commands*\n\n";
        sysInfo += "*Available Commands:*\n";
        sysInfo += "• `/system restart` - Restart ESP32\n";
        sysInfo += "• `/system reset` - Factory reset\n";
        sysInfo += "• `/system wifi` - WiFi information\n";
        sysInfo += "• `/status` - Full system status\n\n";
        sysInfo += "Type any command above to execute.";
        sendMessage(chatId, sysInfo);
    }
}

// Removed keyboard generators - using simple commands instead

// Message sending methods
bool TelegramBot::sendMessage(const String& message, bool useMarkdown) {
    return sendMessage(chatID, message, useMarkdown);
}

bool TelegramBot::sendMessage(const String& chatId, const String& message, bool useMarkdown) {
    if (!initialized || !bot) {
        DEBUG_PRINTLN("TelegramBot: sendMessage failed - not initialized or bot null");
        return false;
    }

    DEBUG_PRINTLN("TelegramBot: Sending message: " + message.substring(0, 50) + "...");
    String parseMode = useMarkdown ? "Markdown" : "";
    bool result = bot->sendMessage(chatId, message, parseMode);
    DEBUG_PRINTLN("TelegramBot: Message send result: " + String(result ? "SUCCESS" : "FAILED"));
    return result;
}

bool TelegramBot::sendMessageWithKeyboard(const String& message, const String& keyboard) {
    if (!initialized || !bot) {
        return false;
    }

    return bot->sendMessageWithInlineKeyboard(chatID, message, "Markdown", keyboard);
}

bool TelegramBot::sendPhoto(const String& chatId, const String& photo, const String& caption) {
    if (!initialized || !bot) {
        return false;
    }

    return bot->sendPhoto(chatId, photo, caption);
}

// Utility methods
bool TelegramBot::isAuthorizedUser(String chatId) {
    return chatId == chatID;
}

String TelegramBot::formatUptime() {
    unsigned long uptime = millis() / 1000;
    unsigned long days = uptime / 86400;
    uptime %= 86400;
    unsigned long hours = uptime / 3600;
    uptime %= 3600;
    unsigned long minutes = uptime / 60;
    unsigned long seconds = uptime % 60;

    String result = "";
    if (days > 0) result += String(days) + "d ";
    if (hours > 0) result += String(hours) + "h ";
    if (minutes > 0) result += String(minutes) + "m ";
    result += String(seconds) + "s";

    return result;
}

String TelegramBot::formatMemoryInfo() {
    uint32_t freeHeap = ESP.getFreeHeap();
    uint32_t totalHeap = ESP.getHeapSize();
    uint32_t usedHeap = totalHeap - freeHeap;

    String result = String(freeHeap / 1024) + " KB free";
    result += " (" + String((freeHeap * 100) / totalHeap) + "%)";

    return result;
}

String TelegramBot::escapeMarkdown(String text) {
    text.replace("_", "\\_");
    text.replace("*", "\\*");
    text.replace("[", "\\[");
    text.replace("]", "\\]");
    text.replace("(", "\\(");
    text.replace(")", "\\)");
    text.replace("~", "\\~");
    text.replace("`", "\\`");
    text.replace(">", "\\>");
    text.replace("#", "\\#");
    text.replace("+", "\\+");
    text.replace("-", "\\-");
    text.replace("=", "\\=");
    text.replace("|", "\\|");
    text.replace("{", "\\{");
    text.replace("}", "\\}");
    text.replace(".", "\\.");
    text.replace("!", "\\!");
    return text;
}

void TelegramBot::sendTypingAction(String chatId) {
    if (initialized && bot) {
        bot->sendChatAction(chatId, "typing");
    }
}

void TelegramBot::setupBotCommands() {
    if (!initialized || !bot) return;

    // Set up bot menu commands (like BotFather)
    String commands = "[";
    commands += "{\"command\":\"start\",\"description\":\"🚀 Start the bot\"},";
    commands += "{\"command\":\"help\",\"description\":\"📚 Show all commands\"},";
    commands += "{\"command\":\"info\",\"description\":\"📊 System status\"},";
    commands += "{\"command\":\"on\",\"description\":\"⚡ Turn pin ON\"},";
    commands += "{\"command\":\"off\",\"description\":\"⚡ Turn pin OFF\"},";
    commands += "{\"command\":\"read\",\"description\":\"📖 Read pin value\"},";
    commands += "{\"command\":\"temp\",\"description\":\"🌡️ Read temperature\"},";
    commands += "{\"command\":\"add\",\"description\":\"➕ Add device/sensor\"},";
    commands += "{\"command\":\"pins\",\"description\":\"🔌 GPIO control\"},";
    commands += "{\"command\":\"sensors\",\"description\":\"🌡️ Sensor management\"},";
    commands += "{\"command\":\"rules\",\"description\":\"⚙️ Automation rules\"}";
    commands += "]";

    // Note: This would require a custom implementation to set bot commands
    // For now, we'll just log it
    DEBUG_PRINTLN("TelegramBot: Bot commands menu configured");
}

String TelegramBot::processOnOffCommand(String chatId, String args, bool turnOn) {
    if (!gpioManager) {
        return "❌ GPIO manager not available";
    }

    if (args.length() == 0) {
        return turnOn ?
            "💡 Usage: `/on <pin>` - Example: `/on 2`" :
            "💡 Usage: `/off <pin>` - Example: `/off 2`";
    }

    int pin = args.toInt();
    if (pin == 0 && args != "0") {
        return "❌ Invalid pin number: " + args;
    }

    // Check if pin is configured
    if (!gpioManager->isPinConfigured(pin)) {
        return "❌ Pin " + String(pin) + " not configured. Use `/add led " + String(pin) + "` first.";
    }

    String command = "write " + String(pin) + " " + (turnOn ? "1" : "0");
    String result = gpioManager->executeCommand(command);

    // Add emoji feedback
    if (result.indexOf("✅") >= 0) {
        result = (turnOn ? "🟢 " : "🔴 ") + result;
    }

    return result;
}

String TelegramBot::processReadCommand(String chatId, String args) {
    if (!gpioManager) {
        return "❌ GPIO manager not available";
    }

    if (args.length() == 0) {
        return "💡 Usage: `/read <pin>` - Example: `/read 34`";
    }

    int pin = args.toInt();
    if (pin == 0 && args != "0") {
        return "❌ Invalid pin number: " + args;
    }

    String command = "read " + String(pin);
    String result = gpioManager->executeCommand(command);

    // Add emoji feedback
    if (result.indexOf("✅") >= 0) {
        result = "📖 " + result;
    }

    return result;
}

String TelegramBot::processTempCommand(String chatId) {
    if (!sensorHandler) {
        return "❌ Sensor handler not available";
    }

    // Try to read from any temperature sensor
    String result = sensorHandler->executeCommand("readall");

    if (result.indexOf("No sensors") >= 0) {
        return "🌡️ No temperature sensors configured.\nUse `/add temp 4` to add one.";
    }

    // Filter for temperature readings
    if (result.indexOf("°C") >= 0 || result.indexOf("Temperature") >= 0) {
        return "🌡️ " + result;
    }

    return "🌡️ No temperature readings available.\nUse `/add temp <pin>` to add a temperature sensor.";
}

String TelegramBot::processAddCommand(String chatId, String args) {
    if (args.length() == 0) {
        String help = "➕ *Quick Add Commands:*\n\n";
        help += "🔌 *GPIO Devices:*\n";
        help += "• `/add led <pin>` - Add LED\n";
        help += "• `/add relay <pin>` - Add relay\n";
        help += "• `/add button <pin>` - Add button\n\n";
        help += "🌡️ *Sensors:*\n";
        help += "• `/add temp <pin>` - DHT22 temperature\n";
        help += "• `/add light <pin>` - Light sensor (LDR)\n";
        help += "• `/add motion <pin>` - PIR motion sensor\n\n";
        help += "*Examples:*\n";
        help += "• `/add led 2` - LED on pin 2\n";
        help += "• `/add temp 4` - Temperature sensor on pin 4";
        return help;
    }

    // Parse arguments
    int firstSpace = args.indexOf(' ');
    if (firstSpace < 0) {
        return "❌ Usage: `/add <type> <pin>` - Example: `/add led 2`";
    }

    String type = args.substring(0, firstSpace);
    String pinStr = args.substring(firstSpace + 1);
    int pin = pinStr.toInt();

    if (pin == 0 && pinStr != "0") {
        return "❌ Invalid pin number: " + pinStr;
    }

    type.toLowerCase();

    // Handle different device types
    if (type == "led" || type == "relay") {
        if (!gpioManager) return "❌ GPIO manager not available";
        String deviceName = type;
        deviceName.setCharAt(0, toupper(deviceName.charAt(0)));
        String command = "set " + String(pin) + " output " + deviceName;
        return "🔌 " + gpioManager->executeCommand(command);
    }
    else if (type == "button") {
        if (!gpioManager) return "❌ GPIO manager not available";
        String command = "set " + String(pin) + " input Button";
        return "🔌 " + gpioManager->executeCommand(command);
    }
    else if (type == "temp" || type == "temperature") {
        if (!sensorHandler) return "❌ Sensor handler not available";
        String command = "add dht22 " + String(pin) + " TempSensor";
        return "🌡️ " + sensorHandler->executeCommand(command);
    }
    else if (type == "light" || type == "ldr") {
        if (!sensorHandler) return "❌ Sensor handler not available";
        String command = "add ldr " + String(pin) + " LightSensor";
        return "💡 " + sensorHandler->executeCommand(command);
    }
    else if (type == "motion" || type == "pir") {
        if (!sensorHandler) return "❌ Sensor handler not available";
        String command = "add pir " + String(pin) + " MotionSensor";
        return "🚶 " + sensorHandler->executeCommand(command);
    }
    else {
        return "❌ Unknown device type: " + type + "\n\nSupported types: led, relay, button, temp, light, motion";
    }
}

String TelegramBot::getBotUsername() {
    if (!bot) {
        return "";
    }

    // Try to get bot info - this is a simple implementation
    // In a real scenario, you might want to call getMe API
    return "ESPgram_Bot";
}

// Notification methods
void TelegramBot::sendStartupMessage() {
    String message = "🚀 *ESPgram Started!*\n\n";
    message += "• Firmware: " + String(FW_NAME) + " v" + String(FW_VERSION) + "\n";
    message += "• WiFi: " + WiFi.SSID() + "\n";
    message += "• IP: " + WiFi.localIP().toString() + "\n\n";
    message += "Type /start to begin or /help for commands.";

    sendMessage(message);
}

void TelegramBot::sendErrorNotification(const String& error) {
    String message = "⚠️ *System Error*\n\n";
    message += "Error: " + escapeMarkdown(error) + "\n";
    message += "Time: " + formatUptime();

    sendMessage(message);
}

void TelegramBot::sendSensorAlert(const String& sensorName, const String& value, const String& threshold) {
    String message = "🚨 *Sensor Alert*\n\n";
    message += "Sensor: " + escapeMarkdown(sensorName) + "\n";
    message += "Value: " + escapeMarkdown(value) + "\n";
    message += "Threshold: " + escapeMarkdown(threshold) + "\n";
    message += "Time: " + formatUptime();

    sendMessage(message);
}

void TelegramBot::sendAutomationTriggered(const String& ruleName, const String& action) {
    String message = "⚙️ *Automation Triggered*\n\n";
    message += "Rule: " + escapeMarkdown(ruleName) + "\n";
    message += "Action: " + escapeMarkdown(action) + "\n";
    message += "Time: " + formatUptime();

    sendMessage(message);
}

void TelegramBot::notifyWiFiConnected() {
    String message = "📶 WiFi Connected\n";
    message += "SSID: " + WiFi.SSID() + "\n";
    message += "IP: " + WiFi.localIP().toString();

    sendMessage(message);
}

void TelegramBot::notifyWiFiDisconnected() {
    sendMessage("📶 WiFi Disconnected - Attempting reconnection...");
}

void TelegramBot::notifySystemRestart() {
    sendMessage("🔄 System restarting...");
}

void TelegramBot::notifyLowMemory() {
    String message = "⚠️ Low Memory Warning\n";
    message += "Free: " + formatMemoryInfo();

    sendMessage(message);
}
