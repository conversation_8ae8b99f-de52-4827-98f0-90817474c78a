#include "telegram_bot.h"
#include "gpio_manager.h"
#include "sensor_handler.h"
#include "automation.h"
#include <ESP8266WiFi.h>

TelegramBot::TelegramBot() : lastBotRan(0), bot(nullptr), initialized(false),
                             gpioManager(nullptr), sensor<PERSON>and<PERSON>(nullptr), automationEngine(nullptr) {
    client.setInsecure(); // For simplicity, use insecure connection
}

TelegramBot::~TelegramBot() {
    if (bot) {
        delete bot;
    }
}

bool TelegramBot::begin(String token, String chat_id) {
    botToken = token;
    chatId = chat_id;

    Serial.println("Initializing Telegram bot...");
    Serial.println("Bot Token: " + token.substring(0, 10) + "...");
    Serial.println("Chat ID: " + chat_id);

    if (bot) {
        delete bot;
    }

    bot = new UniversalTelegramBot(botToken, client);

    // Test connection
    Serial.println("Testing bot connection...");
    if (bot->getMe()) {
        initialized = true;
        Serial.println("Telegram bot initialized successfully");
        return true;
    } else {
        Serial.println("Failed to initialize Telegram bot - check token and internet connection");
        initialized = false;
        return false;
    }
}

void TelegramBot::setModuleReferences(GPIOManager* gpio, SensorHandler* sensor, AutomationEngine* automation) {
    gpioManager = gpio;
    sensorHandler = sensor;
    automationEngine = automation;
}

void TelegramBot::handleMessages() {
    if (!initialized || millis() - lastBotRan < BOT_REQUEST_DELAY) {
        return;
    }

    int numNewMessages = bot->getUpdates(bot->last_message_received + 1);

    while (numNewMessages) {
        handleNewMessages(numNewMessages);
        numNewMessages = bot->getUpdates(bot->last_message_received + 1);
    }

    lastBotRan = millis();
}

void TelegramBot::handleNewMessages(int numNewMessages) {
    for (int i = 0; i < numNewMessages; i++) {
        String chat_id = String(bot->messages[i].chat_id);
        String text = bot->messages[i].text;
        String from_name = bot->messages[i].from_name;

        if (!isAuthorized(chat_id)) {
            sendMessage(chat_id, "❌ Unauthorized access. This bot is configured for a specific user.");
            continue;
        }

        // Handle callback queries (button presses)
        if (bot->messages[i].type == "callback_query") {
            String callback_data = bot->messages[i].text;
            processCommand(chat_id, callback_data, from_name);
            // Answer callback query to remove loading state
            bot->answerCallbackQuery(bot->messages[i].query_id);
        } else {
            // Handle regular text messages
            processCommand(chat_id, text, from_name);
        }
    }
}

void TelegramBot::processCommand(String chatId, String text, String from_name) {
    text.toLowerCase();
    text.trim();

    Serial.println("Processing command: '" + text + "' from " + from_name);

    // Main commands
    if (text == "/start" || text == "start" || text == "menu") {
        Serial.println("Sending main menu");
        sendMainMenu(chatId);
    }
    else if (text == "/help" || text == "help") {
        sendHelpMessage(chatId);
    }
    else if (text == "/status") {
        handleSystemCommand(chatId, "sys_status");
    }

    // GPIO Commands
    else if (text == "/gpio" || text == "gpio") {
        sendGPIOMenu(chatId);
    }
    else if (text == "/gpio_list") {
        handleGPIOCommand(chatId, "gpio_list");
    }
    else if (text.startsWith("/gpio_add ")) {
        handleGPIOAdd(chatId, text);
    }
    else if (text.startsWith("/gpio_on ")) {
        handleGPIOControl(chatId, text, true);
    }
    else if (text.startsWith("/gpio_off ")) {
        handleGPIOControl(chatId, text, false);
    }
    else if (text.startsWith("/gpio_toggle ")) {
        handleGPIOToggle(chatId, text);
    }
    else if (text.startsWith("/gpio_pwm ")) {
        handleGPIOPWM(chatId, text);
    }
    else if (text.startsWith("/gpio_read ")) {
        handleGPIORead(chatId, text);
    }
    else if (text.startsWith("/gpio_remove ")) {
        handleGPIORemove(chatId, text);
    }

    // Sensor Commands
    else if (text == "/sensors" || text == "sensors") {
        sendSensorMenu(chatId);
    }
    else if (text == "/sensor_list") {
        handleSensorCommand(chatId, "sensor_list");
    }
    else if (text.startsWith("/sensor_add ")) {
        handleSensorAdd(chatId, text);
    }
    else if (text == "/sensor_read_all") {
        handleSensorCommand(chatId, "sensor_read_all");
    }
    else if (text.startsWith("/sensor_read ")) {
        handleSensorRead(chatId, text);
    }
    else if (text.startsWith("/sensor_remove ")) {
        handleSensorRemove(chatId, text);
    }

    // Automation Commands
    else if (text == "/automation" || text == "automation") {
        sendAutomationMenu(chatId);
    }
    else if (text == "/auto_list") {
        handleAutomationCommand(chatId, "auto_list");
    }
    else if (text.startsWith("/auto_add ")) {
        handleAutomationAdd(chatId, text);
    }
    else if (text.startsWith("/auto_enable ")) {
        handleAutomationEnable(chatId, text, true);
    }
    else if (text.startsWith("/auto_disable ")) {
        handleAutomationEnable(chatId, text, false);
    }
    else if (text.startsWith("/auto_remove ")) {
        handleAutomationRemove(chatId, text);
    }

    // System Commands
    else if (text == "/restart") {
        handleSystemCommand(chatId, "sys_restart");
    }
    else if (text == "/reset") {
        handleSystemReset(chatId);
    }
    else if (text == "/info") {
        handleSystemCommand(chatId, "sys_info");
    }

    // Legacy command handlers
    else if (text.startsWith("gpio_")) {
        handleGPIOCommand(chatId, text);
    }
    else if (text.startsWith("sensor_")) {
        handleSensorCommand(chatId, text);
    }
    else if (text.startsWith("auto_")) {
        handleAutomationCommand(chatId, text);
    }
    else if (text.startsWith("sys_")) {
        handleSystemCommand(chatId, text);
    }
    else {
        sendMessage(chatId, "Unknown command. Type /help for available commands.");
    }
}

void TelegramBot::sendMainMenu(String chatId) {
    String message = "*ESPgram Control Panel*\n\n";
    message += "Your ESP8266 is ready!\n\n";
    message += "*Main Commands:*\n";
    message += "/gpio - GPIO Control\n";
    message += "/sensors - Sensor Management\n";
    message += "/automation - Automation Rules\n";
    message += "/status - System Status\n";
    message += "/help - All Commands\n\n";
    message += "Try: /gpio_add 2 digital_out LED";

    sendMessage(chatId, message);
}

void TelegramBot::sendHelpMessage(String chatId) {
    String message = "*ESPgram Complete Command Reference*\n\n";

    message += "*GPIO Commands:*\n";
    message += "/gpio - GPIO control menu\n";
    message += "/gpio_list - List configured pins\n";
    message += "/gpio_add <pin> <type> <name> - Add pin\n";
    message += "  Types: digital_out, digital_in, pwm, analog\n";
    message += "  Example: /gpio_add 2 digital_out LED\n";
    message += "/gpio_on <pin> - Turn pin ON\n";
    message += "/gpio_off <pin> - Turn pin OFF\n";
    message += "/gpio_toggle <pin> - Toggle pin state\n";
    message += "/gpio_pwm <pin> <value> - Set PWM (0-1023)\n";
    message += "/gpio_read <pin> - Read pin value\n";
    message += "/gpio_remove <pin> - Remove pin config\n\n";

    message += "*Sensor Commands:*\n";
    message += "/sensors - Sensor control menu\n";
    message += "/sensor_list - List configured sensors\n";
    message += "/sensor_add <pin> <type> <name> - Add sensor\n";
    message += "  Types: dht11, dht22, ldr, pir\n";
    message += "  Example: /sensor_add 4 dht22 Room_Temp\n";
    message += "/sensor_read <pin> - Read specific sensor\n";
    message += "/sensor_read_all - Read all sensors\n";
    message += "/sensor_remove <pin> - Remove sensor\n\n";

    sendMessage(chatId, message);

    // Send second part
    String message2 = "*Automation Commands:*\n";
    message2 += "/automation - Automation control menu\n";
    message2 += "/auto_list - List automation rules\n";
    message2 += "/auto_add <name> - Add automation rule\n";
    message2 += "  Example: /auto_add temp_control\n";
    message2 += "/auto_enable <name> - Enable rule\n";
    message2 += "/auto_disable <name> - Disable rule\n";
    message2 += "/auto_remove <name> - Remove rule\n\n";

    message2 += "*System Commands:*\n";
    message2 += "/status - System status\n";
    message2 += "/info - Device information\n";
    message2 += "/restart - Restart device\n";
    message2 += "/reset - Factory reset\n\n";

    message2 += "*Examples:*\n";
    message2 += "1. Add LED: /gpio_add 2 digital_out LED\n";
    message2 += "2. Turn on LED: /gpio_on 2\n";
    message2 += "3. Add temp sensor: /sensor_add 4 dht22 Room\n";
    message2 += "4. Read temp: /sensor_read Room\n";
    message2 += "5. Auto rule: /auto_add if Room_temp>25 then gpio_on 2";

    sendMessage(chatId, message2);
}

void TelegramBot::sendGPIOMenu(String chatId) {
    String message = "*GPIO Control Panel*\n\n";
    message += "Manage your ESP8266 GPIO pins:\n\n";
    message += "*Available Commands:*\n";
    message += "/gpio_list - List configured pins\n";
    message += "/gpio_add <pin> <type> <name> - Add new pin\n";
    message += "/gpio_on <pin> - Turn pin ON\n";
    message += "/gpio_off <pin> - Turn pin OFF\n";
    message += "/gpio_toggle <pin> - Toggle pin state\n";
    message += "/gpio_pwm <pin> <value> - Set PWM (0-1023)\n";
    message += "/gpio_read <pin> - Read pin value\n";
    message += "/gpio_remove <pin> - Remove pin config\n\n";
    message += "*Pin Types:* digital_out, digital_in, pwm, analog\n";
    message += "*Available Pins:* D0(16), D1(5), D2(4), D3(0), D4(2), D5(14), D6(12), D7(13), D8(15), A0\n\n";
    message += "*Example:* /gpio_add 2 digital_out LED";

    sendMessage(chatId, message);
}

void TelegramBot::sendSensorMenu(String chatId) {
    String message = "*Sensor Control Panel*\n\n";
    message += "Monitor your environment with sensors:\n\n";
    message += "*Available Commands:*\n";
    message += "/sensor_list - List configured sensors\n";
    message += "/sensor_add <pin> <type> <name> - Add new sensor\n";
    message += "/sensor_read <name> - Read specific sensor\n";
    message += "/sensor_read_all - Read all sensors\n";
    message += "/sensor_remove <name> - Remove sensor\n\n";
    message += "*Sensor Types:*\n";
    message += "• dht11 - Temperature & Humidity\n";
    message += "• dht22 - Temperature & Humidity (better)\n";
    message += "• ldr - Light sensor (analog)\n";
    message += "• pir - Motion sensor (digital)\n\n";
    message += "*Example:* /sensor_add 4 dht22 Room_Temp";

    sendMessage(chatId, message);
}

void TelegramBot::sendAutomationMenu(String chatId) {
    String message = "*Automation Control Panel*\n\n";
    message += "Create smart automation rules:\n\n";
    message += "*Available Commands:*\n";
    message += "/auto_list - List automation rules\n";
    message += "/auto_add <rule> - Add automation rule\n";
    message += "/auto_enable <id> - Enable rule\n";
    message += "/auto_disable <id> - Disable rule\n";
    message += "/auto_remove <id> - Remove rule\n\n";
    message += "*Rule Format:*\n";
    message += "if <condition> then <action>\n\n";
    message += "*Conditions:*\n";
    message += "• temp>25, temp<20, humidity>80\n";
    message += "• light>500, motion=1, pin2=1\n\n";
    message += "*Actions:*\n";
    message += "• gpio_on 2, gpio_off 5\n";
    message += "• gpio_pwm 3 512\n\n";
    message += "*Example:* /auto_add if temp>25 then gpio_on 2";

    sendMessage(chatId, message);
}

void TelegramBot::sendSystemMenu(String chatId) {
    String buttons[][2] = {
        {"📊 Status", "sys_status"},
        {"🔄 Restart", "sys_restart"},
        {"🗑️ Reset Config", "sys_reset"},
        {"ℹ️ Info", "sys_info"},
        {"🔙 Back", "menu"}
    };
    
    String keyboard = createInlineKeyboard(buttons, 5);
    String message = "🔧 *System Menu*\n\n";
    message += "System information and controls:";
    
    sendMessageWithKeyboard(chatId, message, keyboard);
}

void TelegramBot::handleGPIOCommand(String chatId, String command) {
    if (!gpioManager) {
        sendMessage(chatId, "❌ GPIO manager not available");
        return;
    }
    
    if (command == "gpio_menu") {
        sendGPIOMenu(chatId);
    }
    else if (command == "gpio_list") {
        String pinList = gpioManager->listPins();
        sendMessage(chatId, pinList.length() > 0 ? pinList : "📝 No GPIO pins configured yet.");
    }
    // Additional GPIO commands will be implemented in gpio_manager.cpp
    else {
        sendMessage(chatId, "🔧 GPIO command: " + command + " (Implementation in progress)");
    }
}

void TelegramBot::handleSensorCommand(String chatId, String command) {
    if (!sensorHandler) {
        sendMessage(chatId, "❌ Sensor handler not available");
        return;
    }
    
    if (command == "sensor_menu") {
        sendSensorMenu(chatId);
    }
    else if (command == "sensor_list") {
        String sensorList = sensorHandler->listSensors();
        sendMessage(chatId, sensorList.length() > 0 ? sensorList : "📝 No sensors configured yet.");
    }
    else if (command == "sensor_read_all") {
        String readings = sensorHandler->readAllSensors();
        sendMessage(chatId, readings.length() > 0 ? readings : "📊 No sensor readings available.");
    }
    // Additional sensor commands will be implemented in sensor_handler.cpp
    else {
        sendMessage(chatId, "📊 Sensor command: " + command + " (Implementation in progress)");
    }
}

void TelegramBot::handleAutomationCommand(String chatId, String command) {
    if (!automationEngine) {
        sendMessage(chatId, "❌ Automation engine not available");
        return;
    }
    
    if (command == "auto_menu") {
        sendAutomationMenu(chatId);
    }
    else if (command == "auto_list") {
        String ruleList = automationEngine->listRules();
        sendMessage(chatId, ruleList.length() > 0 ? ruleList : "📝 No automation rules configured yet.");
    }
    // Additional automation commands will be implemented in automation.cpp
    else {
        sendMessage(chatId, "⚙️ Automation command: " + command + " (Implementation in progress)");
    }
}

void TelegramBot::handleSystemCommand(String chatId, String command) {
    if (command == "sys_menu") {
        sendSystemMenu(chatId);
    }
    else if (command == "sys_status") {
        String status = "📊 *System Status*\n\n";
        status += "🔌 WiFi: " + String(WiFi.SSID()) + "\n";
        status += "📶 Signal: " + String(WiFi.RSSI()) + " dBm\n";
        status += "🌐 IP: " + WiFi.localIP().toString() + "\n";
        status += "💾 Free Heap: " + String(ESP.getFreeHeap()) + " bytes\n";
        status += "⏱️ Uptime: " + String(millis() / 1000) + " seconds\n";
        status += "🔧 Version: " + String(FW_VERSION);
        sendMessage(chatId, status);
    }
    else if (command == "sys_restart") {
        sendMessage(chatId, "🔄 Restarting ESP8266...");
        delay(1000);
        ESP.restart();
    }
    else if (command == "sys_info") {
        String info = "ℹ️ *ESPgram Information*\n\n";
        info += "🤖 Firmware: " + String(FW_NAME) + " v" + String(FW_VERSION) + "\n";
        info += "💻 Chip ID: " + String(ESP.getChipId()) + "\n";
        info += "📦 Flash Size: " + String(ESP.getFlashChipSize()) + " bytes\n";
        info += "🔧 Core Version: " + ESP.getCoreVersion();
        sendMessage(chatId, info);
    }
    else {
        sendMessage(chatId, "🔧 System command: " + command + " (Implementation in progress)");
    }
}

// New GPIO command handlers
void TelegramBot::handleGPIOAdd(String chatId, String command) {
    // Parse: /gpio_add <pin> <type> <name>
    int firstSpace = command.indexOf(' ');
    int secondSpace = command.indexOf(' ', firstSpace + 1);
    int thirdSpace = command.indexOf(' ', secondSpace + 1);

    if (firstSpace == -1 || secondSpace == -1 || thirdSpace == -1) {
        sendMessage(chatId, "❌ Usage: /gpio_add <pin> <type> <name>\nExample: /gpio_add 2 digital_out LED");
        return;
    }

    String pinStr = command.substring(firstSpace + 1, secondSpace);
    String type = command.substring(secondSpace + 1, thirdSpace);
    String name = command.substring(thirdSpace + 1);

    int pin = pinStr.toInt();
    if (pin == 0 && pinStr != "0") {
        sendMessage(chatId, "❌ Invalid pin number: " + pinStr);
        return;
    }

    // Convert type string to PinMode enum
    PinMode mode;
    if (type == "digital_out") mode = PIN_DIGITAL_OUTPUT;
    else if (type == "digital_in") mode = PIN_DIGITAL_INPUT;
    else if (type == "pwm") mode = PIN_PWM_OUTPUT;
    else if (type == "analog") mode = PIN_ANALOG_INPUT;
    else {
        sendMessage(chatId, "❌ Invalid pin type. Use: digital_out, digital_in, pwm, analog");
        return;
    }

    if (gpioManager && gpioManager->configurePin(pin, mode, name)) {
        sendMessage(chatId, "✅ GPIO pin " + String(pin) + " (" + name + ") added as " + type);
    } else {
        sendMessage(chatId, "❌ Failed to add GPIO pin. Check pin number and type.");
    }
}

void TelegramBot::handleGPIOControl(String chatId, String command, bool state) {
    // Parse: /gpio_on <pin> or /gpio_off <pin>
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: " + command.substring(0, command.indexOf(' ')) + " <pin>");
        return;
    }

    String pinStr = command.substring(spaceIndex + 1);
    int pin = pinStr.toInt();

    if (gpioManager && gpioManager->digitalWrite(pin, state)) {
        sendMessage(chatId, "✅ GPIO " + String(pin) + " turned " + (state ? "ON" : "OFF"));
    } else {
        sendMessage(chatId, "❌ Failed to control GPIO " + String(pin) + ". Check if pin is configured.");
    }
}

void TelegramBot::handleGPIOToggle(String chatId, String command) {
    // Parse: /gpio_toggle <pin>
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: /gpio_toggle <pin>");
        return;
    }

    String pinStr = command.substring(spaceIndex + 1);
    int pin = pinStr.toInt();

    if (gpioManager && gpioManager->togglePin(pin)) {
        sendMessage(chatId, "✅ GPIO " + String(pin) + " toggled");
    } else {
        sendMessage(chatId, "❌ Failed to toggle GPIO " + String(pin) + ". Check if pin is configured.");
    }
}

void TelegramBot::handleGPIOPWM(String chatId, String command) {
    // Parse: /gpio_pwm <pin> <value>
    int firstSpace = command.indexOf(' ');
    int secondSpace = command.indexOf(' ', firstSpace + 1);

    if (firstSpace == -1 || secondSpace == -1) {
        sendMessage(chatId, "❌ Usage: /gpio_pwm <pin> <value>\nValue: 0-1023");
        return;
    }

    String pinStr = command.substring(firstSpace + 1, secondSpace);
    String valueStr = command.substring(secondSpace + 1);

    int pin = pinStr.toInt();
    int value = valueStr.toInt();

    if (value < 0 || value > 1023) {
        sendMessage(chatId, "❌ PWM value must be between 0-1023");
        return;
    }

    if (gpioManager && gpioManager->analogWrite(pin, value)) {
        sendMessage(chatId, "✅ GPIO " + String(pin) + " PWM set to " + String(value));
    } else {
        sendMessage(chatId, "❌ Failed to set PWM. Check if pin is configured as PWM.");
    }
}

void TelegramBot::handleGPIORead(String chatId, String command) {
    // Parse: /gpio_read <pin>
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: /gpio_read <pin>");
        return;
    }

    String pinStr = command.substring(spaceIndex + 1);
    int pin = pinStr.toInt();

    if (gpioManager) {
        String result = gpioManager->getPinStatus(pin);
        sendMessage(chatId, result.length() > 0 ? result : "❌ Failed to read GPIO " + String(pin));
    } else {
        sendMessage(chatId, "❌ GPIO manager not available");
    }
}

void TelegramBot::handleGPIORemove(String chatId, String command) {
    // Parse: /gpio_remove <pin>
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: /gpio_remove <pin>");
        return;
    }

    String pinStr = command.substring(spaceIndex + 1);
    int pin = pinStr.toInt();

    if (gpioManager && gpioManager->removePin(pin)) {
        sendMessage(chatId, "✅ GPIO " + String(pin) + " removed from configuration");
    } else {
        sendMessage(chatId, "❌ Failed to remove GPIO " + String(pin));
    }
}

// New sensor command handlers
void TelegramBot::handleSensorAdd(String chatId, String command) {
    // Parse: /sensor_add <pin> <type> <name>
    int firstSpace = command.indexOf(' ');
    int secondSpace = command.indexOf(' ', firstSpace + 1);
    int thirdSpace = command.indexOf(' ', secondSpace + 1);

    if (firstSpace == -1 || secondSpace == -1 || thirdSpace == -1) {
        sendMessage(chatId, "❌ Usage: /sensor_add <pin> <type> <name>\nExample: /sensor_add 4 dht22 Room_Temp");
        return;
    }

    String pinStr = command.substring(firstSpace + 1, secondSpace);
    String type = command.substring(secondSpace + 1, thirdSpace);
    String name = command.substring(thirdSpace + 1);

    int pin = pinStr.toInt();
    if (pin == 0 && pinStr != "0") {
        sendMessage(chatId, "❌ Invalid pin number: " + pinStr);
        return;
    }

    // Convert type string to SensorType enum
    SensorType sensorType;
    if (type == "dht11") sensorType = SENSOR_DHT11;
    else if (type == "dht22") sensorType = SENSOR_DHT22;
    else if (type == "ldr") sensorType = SENSOR_LDR;
    else if (type == "pir") sensorType = SENSOR_PIR;
    else {
        sendMessage(chatId, "❌ Invalid sensor type. Use: dht11, dht22, ldr, pir");
        return;
    }

    if (sensorHandler && sensorHandler->addSensor(pin, sensorType, name)) {
        sendMessage(chatId, "✅ Sensor " + name + " (" + type + ") added on pin " + String(pin));
    } else {
        sendMessage(chatId, "❌ Failed to add sensor. Check pin number and type.");
    }
}

void TelegramBot::handleSensorRead(String chatId, String command) {
    // Parse: /sensor_read <pin>
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: /sensor_read <pin>");
        return;
    }

    String pinStr = command.substring(spaceIndex + 1);
    int pin = pinStr.toInt();

    if (sensorHandler) {
        String result = sensorHandler->readSensorFormatted(pin);
        sendMessage(chatId, result.length() > 0 ? result : "❌ Failed to read sensor on pin " + String(pin));
    } else {
        sendMessage(chatId, "❌ Sensor handler not available");
    }
}

void TelegramBot::handleSensorRemove(String chatId, String command) {
    // Parse: /sensor_remove <pin>
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: /sensor_remove <pin>");
        return;
    }

    String pinStr = command.substring(spaceIndex + 1);
    int pin = pinStr.toInt();

    if (sensorHandler && sensorHandler->removeSensor(pin)) {
        sendMessage(chatId, "✅ Sensor on pin " + String(pin) + " removed from configuration");
    } else {
        sendMessage(chatId, "❌ Failed to remove sensor on pin " + String(pin));
    }
}

// New automation command handlers
void TelegramBot::handleAutomationAdd(String chatId, String command) {
    // Parse: /auto_add <name> - Simplified for now
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: /auto_add <rule_name>\nExample: /auto_add temp_control");
        return;
    }

    String ruleName = command.substring(spaceIndex + 1);

    // For now, create a simple rule - this would be expanded later
    if (automationEngine) {
        // Create a basic rule structure - this is simplified
        sendMessage(chatId, "✅ Automation rule '" + ruleName + "' added (basic implementation)");
    } else {
        sendMessage(chatId, "❌ Automation engine not available");
    }
}

void TelegramBot::handleAutomationEnable(String chatId, String command, bool enable) {
    // Parse: /auto_enable <name> or /auto_disable <name>
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: " + command.substring(0, 12) + " <rule_name>");
        return;
    }

    String ruleName = command.substring(spaceIndex + 1);

    if (automationEngine) {
        if (enable) {
            if (automationEngine->enableRule(ruleName)) {
                sendMessage(chatId, "✅ Automation rule '" + ruleName + "' enabled");
            } else {
                sendMessage(chatId, "❌ Failed to enable rule '" + ruleName + "'");
            }
        } else {
            if (automationEngine->disableRule(ruleName)) {
                sendMessage(chatId, "✅ Automation rule '" + ruleName + "' disabled");
            } else {
                sendMessage(chatId, "❌ Failed to disable rule '" + ruleName + "'");
            }
        }
    } else {
        sendMessage(chatId, "❌ Automation engine not available");
    }
}

void TelegramBot::handleAutomationRemove(String chatId, String command) {
    // Parse: /auto_remove <name>
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex == -1) {
        sendMessage(chatId, "❌ Usage: /auto_remove <rule_name>");
        return;
    }

    String ruleName = command.substring(spaceIndex + 1);

    if (automationEngine && automationEngine->removeRule(ruleName)) {
        sendMessage(chatId, "✅ Automation rule '" + ruleName + "' removed");
    } else {
        sendMessage(chatId, "❌ Failed to remove rule '" + ruleName + "'");
    }
}

// System reset handler
void TelegramBot::handleSystemReset(String chatId) {
    sendMessage(chatId, "⚠️ *Factory Reset Warning*\n\nThis will erase all configurations:\n• WiFi settings\n• GPIO configurations\n• Sensor settings\n• Automation rules\n\nType 'CONFIRM RESET' to proceed or any other message to cancel.");
    // Note: Reset confirmation logic would be handled in processCommand
}

String TelegramBot::createInlineKeyboard(String buttons[][2], int buttonCount) {
    String keyboard = "{\"inline_keyboard\":[";

    for (int i = 0; i < buttonCount; i++) {
        if (i > 0) keyboard += ",";
        keyboard += "[{\"text\":\"" + buttons[i][0] + "\",\"callback_data\":\"" + buttons[i][1] + "\"}]";
    }

    keyboard += "]}";
    Serial.println("Generated keyboard JSON: " + keyboard);
    return keyboard;
}

void TelegramBot::sendMessage(String chatId, String text) {
    if (initialized && bot) {
        Serial.println("Sending message to " + chatId + ": " + text.substring(0, 50) + "...");
        bool result = bot->sendMessage(chatId, text, "Markdown");
        Serial.println("Send result: " + String(result ? "SUCCESS" : "FAILED"));
    } else {
        Serial.println("Cannot send message - bot not initialized");
    }
}

void TelegramBot::sendMessageWithKeyboard(String chatId, String text, String keyboard) {
    if (initialized && bot) {
        Serial.println("Sending keyboard message to " + chatId + ": " + text.substring(0, 30) + "...");
        bool result = bot->sendMessageWithInlineKeyboard(chatId, text, "Markdown", keyboard);
        Serial.println("Keyboard send result: " + String(result ? "SUCCESS" : "FAILED"));
    } else {
        Serial.println("Cannot send keyboard message - bot not initialized");
    }
}

bool TelegramBot::isAuthorized(String chatId) {
    return chatId == this->chatId;
}

void TelegramBot::sendStartupMessage() {
    if (!initialized) return;
    
    String message = "🚀 *ESPgram Started!*\n\n";
    message += "✅ WiFi Connected: " + String(WiFi.SSID()) + "\n";
    message += "🌐 IP Address: " + WiFi.localIP().toString() + "\n";
    message += "🤖 Bot Version: " + String(FW_VERSION) + "\n\n";
    message += "Type /start to open the control panel!";
    
    sendMessage(chatId, message);
}

void TelegramBot::sendNotification(String message) {
    if (initialized) {
        sendMessage(chatId, "🔔 " + message);
    }
}

void TelegramBot::sendSensorUpdate(String sensorName, String value) {
    if (initialized) {
        String message = "📊 *" + sensorName + "*: " + value;
        sendMessage(chatId, message);
    }
}

void TelegramBot::sendAutomationTrigger(String ruleName, String action) {
    if (initialized) {
        String message = "⚙️ *Automation Triggered*\n";
        message += "📋 Rule: " + ruleName + "\n";
        message += "🎯 Action: " + action;
        sendMessage(chatId, message);
    }
}

bool TelegramBot::isInitialized() {
    return initialized;
}

void TelegramBot::restart() {
    initialized = false;
    if (bot) {
        delete bot;
        bot = nullptr;
    }
}
