#!/usr/bin/env python3
"""
ESPgram Release Creation Script
Automatically creates release packages after successful build
"""

import os
import shutil
import sys
from datetime import datetime
import subprocess
import json

def get_project_info():
    """Get project information from platformio.ini and config.h"""
    project_info = {
        'name': 'ESPgram',
        'version': '1.0.0',
        'board': 'esp8266',
        'build_date': datetime.now().strftime('%Y%m%d_%H%M%S')
    }
    
    # Try to get version from config.h
    try:
        config_path = os.path.join('include', 'config.h')
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                content = f.read()
                import re
                version_match = re.search(r'#define FW_VERSION "([^"]+)"', content)
                if version_match:
                    project_info['version'] = version_match.group(1)
    except Exception as e:
        print(f"Warning: Could not read version from config.h: {e}")
    
    return project_info

def create_release_folder(project_info):
    """Create release folder structure"""
    release_name = f"{project_info['name']}_v{project_info['version']}_{project_info['build_date']}"
    release_path = os.path.join('release', release_name)
    
    # Create release directory
    os.makedirs(release_path, exist_ok=True)
    
    return release_path, release_name

def copy_firmware_files(release_path, project_info):
    """Copy firmware files to release folder"""
    files_copied = []

    # Copy only the main firmware binary
    firmware_src = os.path.join('.pio', 'build', 'esp8266', 'firmware.bin')
    if os.path.exists(firmware_src):
        firmware_dst = os.path.join(release_path, f"{project_info['name']}_v{project_info['version']}.bin")
        shutil.copy2(firmware_src, firmware_dst)
        files_copied.append(f"{project_info['name']}_v{project_info['version']}.bin")
        print(f"[OK] Copied firmware binary: {firmware_dst}")
    else:
        print(f"[ERROR] Firmware binary not found: {firmware_src}")
        return False, files_copied

    return True, files_copied

def create_release_info(release_path, project_info, files_copied):
    """Create release information file"""
    release_info = {
        'project': project_info['name'],
        'version': project_info['version'],
        'build_date': project_info['build_date'],
        'build_timestamp': datetime.now().isoformat(),
        'files': files_copied,
        'platform': 'ESP8266',
        'board': 'NodeMCU v1.0 (ESP-12E)',
        'framework': 'Arduino',
        'features': [
            'WiFi Setup with Captive Portal',
            'Telegram Bot Control',
            'GPIO Management (Digital/PWM/Analog)',
            'Sensor Support (DHT11/DHT22/LDR/PIR)',
            'Automation Engine',
            'EEPROM Configuration Storage',
            'OTA Ready'
        ],
        'memory_usage': get_memory_usage(),
        'installation': {
            'method': 'PlatformIO Upload',
            'command': 'pio run --target upload',
            'baud_rate': 115200,
            'flash_size': '4MB',
            'setup_instructions': [
                '1. Flash the firmware to ESP8266',
                '2. Connect to ESPgram-Setup WiFi network',
                '3. Navigate to 192.168.4.1 in browser',
                '4. Enter WiFi credentials and Telegram bot details',
                '5. Save configuration and restart',
                '6. Send /start to your Telegram bot'
            ]
        }
    }
    
    # Save as JSON
    info_file = os.path.join(release_path, 'release_info.json')
    with open(info_file, 'w') as f:
        json.dump(release_info, f, indent=2)
    
    # Save as text
    info_txt = os.path.join(release_path, 'release_info.txt')
    with open(info_txt, 'w', encoding='utf-8') as f:
        f.write(f"ESPgram Firmware Release\n")
        f.write(f"========================\n\n")
        f.write(f"Version: {release_info['version']}\n")
        f.write(f"Build Date: {release_info['build_date']}\n")
        f.write(f"Platform: {release_info['platform']}\n")
        f.write(f"Board: {release_info['board']}\n\n")
        f.write(f"Files Included:\n")
        for file in files_copied:
            f.write(f"  - {file}\n")
        f.write(f"\nFeatures:\n")
        for feature in release_info['features']:
            f.write(f"  - {feature}\n")
        f.write(f"\nInstallation:\n")
        for step in release_info['installation']['setup_instructions']:
            f.write(f"  {step}\n")
    
    print(f"[OK] Created release info: {info_file}")
    print(f"[OK] Created release info: {info_txt}")

def get_memory_usage():
    """Get memory usage from build output"""
    try:
        # Run pio run to get memory usage
        result = subprocess.run(['pio', 'run'], capture_output=True, text=True)
        output = result.stdout
        
        memory_info = {}
        
        # Parse memory usage
        import re
        ram_match = re.search(r'RAM:\s+\[.*?\]\s+([\d.]+)%\s+\(used (\d+) bytes from (\d+) bytes\)', output)
        if ram_match:
            memory_info['ram'] = {
                'percentage': float(ram_match.group(1)),
                'used': int(ram_match.group(2)),
                'total': int(ram_match.group(3))
            }
        
        flash_match = re.search(r'Flash:\s+\[.*?\]\s+([\d.]+)%\s+\(used (\d+) bytes from (\d+) bytes\)', output)
        if flash_match:
            memory_info['flash'] = {
                'percentage': float(flash_match.group(1)),
                'used': int(flash_match.group(2)),
                'total': int(flash_match.group(3))
            }
        
        return memory_info
    except Exception as e:
        print(f"Warning: Could not get memory usage: {e}")
        return {}

def create_flash_script(release_path, project_info):
    """Create flash script for easy installation"""
    
    # Windows batch script
    bat_script = os.path.join(release_path, 'flash_firmware.bat')
    with open(bat_script, 'w', encoding='utf-8') as f:
        f.write('@echo off\n')
        f.write('echo ESPgram Firmware Flash Script\n')
        f.write('echo ===============================\n')
        f.write('echo.\n')
        f.write('echo Make sure your ESP8266 is connected and in download mode\n')
        f.write('echo Press any key to continue...\n')
        f.write('pause >nul\n')
        f.write('echo.\n')
        f.write('echo Flashing firmware...\n')
        f.write(f'esptool.py --port COM4 --baud 921600 write_flash 0x0 {project_info["name"]}_v{project_info["version"]}.bin\n')
        f.write('echo.\n')
        f.write('if %errorlevel% equ 0 (\n')
        f.write('    echo ✅ Firmware flashed successfully!\n')
        f.write('    echo Connect to ESPgram-Setup WiFi to configure\n')
        f.write(') else (\n')
        f.write('    echo ❌ Flash failed. Check connection and try again.\n')
        f.write(')\n')
        f.write('pause\n')
    
    # Linux/Mac shell script
    sh_script = os.path.join(release_path, 'flash_firmware.sh')
    with open(sh_script, 'w', encoding='utf-8') as f:
        f.write('#!/bin/bash\n')
        f.write('echo "ESPgram Firmware Flash Script"\n')
        f.write('echo "==============================="\n')
        f.write('echo ""\n')
        f.write('echo "Make sure your ESP8266 is connected and in download mode"\n')
        f.write('read -p "Press Enter to continue..."\n')
        f.write('echo ""\n')
        f.write('echo "Flashing firmware..."\n')
        f.write(f'esptool.py --port /dev/ttyUSB0 --baud 921600 write_flash 0x0 {project_info["name"]}_v{project_info["version"]}.bin\n')
        f.write('echo ""\n')
        f.write('if [ $? -eq 0 ]; then\n')
        f.write('    echo "✅ Firmware flashed successfully!"\n')
        f.write('    echo "Connect to ESPgram-Setup WiFi to configure"\n')
        f.write('else\n')
        f.write('    echo "❌ Flash failed. Check connection and try again."\n')
        f.write('fi\n')
        f.write('read -p "Press Enter to exit..."\n')
    
    # Make shell script executable
    try:
        os.chmod(sh_script, 0o755)
    except:
        pass
    
    print(f"[OK] Created flash scripts: {bat_script}, {sh_script}")

def main():
    """Main function"""
    print("ESPgram Release Creation Script")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not os.path.exists('platformio.ini'):
        print("[ERROR] platformio.ini not found. Run this script from the project root.")
        sys.exit(1)

    # Check if firmware was built
    firmware_path = os.path.join('.pio', 'build', 'esp8266', 'firmware.bin')
    if not os.path.exists(firmware_path):
        print("[ERROR] Firmware not found. Run 'pio run' first.")
        sys.exit(1)
    
    try:
        # Get project information
        project_info = get_project_info()
        print(f"📦 Creating release for {project_info['name']} v{project_info['version']}")
        
        # Create release folder
        release_path, release_name = create_release_folder(project_info)
        print(f"📁 Release folder: {release_path}")
        
        # Copy firmware files
        success, files_copied = copy_firmware_files(release_path, project_info)
        if not success:
            print("[ERROR] Failed to copy firmware files")
            sys.exit(1)
        
        # Create release information
        create_release_info(release_path, project_info, files_copied)
        
        # Create flash scripts
        create_flash_script(release_path, project_info)
        
        print("\n[SUCCESS] Release created successfully!")
        print(f"Location: {release_path}")
        print(f"Release name: {release_name}")
        print("\nFiles included:")
        for file in files_copied:
            print(f"   - {file}")
        print("   - release_info.txt")
        print("   - flash_firmware.bat")
        print("   - flash_firmware.sh")

    except Exception as e:
        print(f"[ERROR] Error creating release: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
