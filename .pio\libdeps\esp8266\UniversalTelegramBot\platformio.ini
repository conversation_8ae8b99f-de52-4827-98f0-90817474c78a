; PlatformIO Project Configuration File

;   64 ArduinoJson
;      by <PERSON><PERSON>
;      Repository: https://github.com/bblanchon/ArduinoJson.git
; 1262 UniversalTelegramBot
;      by <PERSON>
;      Repository: https://github.com/witnessmenow/Universal-Arduino-Telegram-Bot.git

[platformio]
description = Universal Telegram Bot Library
default_envs = d1_mini

[env]
lib_deps =
  ArduinoJson
monitor_speed = 115200

[env:d1_mini]
platform = espressif8266
board = d1_mini
framework = arduino

[env:esp32dev]
platform = espressif32
board = esp32doit-devkit-v1
framework = arduino
