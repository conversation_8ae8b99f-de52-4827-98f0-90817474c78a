# ESPgram - ESP8266 Telegram Bot Firmware

A robust, modular firmware for ESP8266 that provides complete control via Telegram Bot without requiring a web interface (except for initial setup).

## 🚀 Features

### 🔌 First-Time Setup
- **Captive Portal**: Automatic AP mode on first boot
- **Web Setup**: Clean, responsive setup page served from firmware
- **Credential Storage**: Secure EEPROM storage for WiFi and Telegram credentials
- **Dual Mode**: Automatic fallback to setup mode if WiF<PERSON> fails

### 💬 Telegram Bot Control
- **Complete Control**: All functionality accessible via Telegram
- **Interactive Menus**: Intuitive inline keyboard navigation
- **Real-time Notifications**: Instant status updates and alerts
- **Secure Access**: Chat ID-based authorization

### ⚙️ GPIO Management
- **Digital Output**: Control LEDs, relays, motors
- **Digital Input**: Read buttons, switches, sensors
- **PWM Output**: Control brightness, speed (0-1023 range)
- **Analog Input**: Read analog sensors (A0 pin)

### 📊 Sensor Support
- **DHT11/DHT22**: Temperature and humidity monitoring
- **LDR**: Light level detection
- **PIR**: Motion detection
- **Digital Sensors**: Generic digital input support

### 🤖 Automation Engine
- **Smart Rules**: If-then automation logic
- **Multiple Conditions**: Sensor thresholds, pin states, time intervals
- **Various Actions**: Pin control, PWM, notifications
- **Cooldown System**: Prevent spam triggering

### 🔧 System Features
- **Memory Optimized**: Efficient memory usage for ESP8266
- **OTA Ready**: Single .bin file compilation
- **Version Control**: Automated version management
- **Error Handling**: Robust error detection and recovery
- **Watchdog Support**: System stability monitoring

## 📁 Project Structure

```
ESPgram/
├── src/
│   ├── main.cpp              # Main application
│   ├── wifi_setup.cpp        # WiFi and setup management
│   ├── telegram_bot.cpp      # Telegram bot implementation
│   ├── gpio_manager.cpp      # GPIO control and management
│   ├── sensor_handler.cpp    # Sensor reading and monitoring
│   └── automation.cpp        # Automation rules engine
├── include/
│   ├── config.h              # Configuration and constants
│   ├── html_page.h           # Embedded setup page
│   ├── wifi_setup.h          # WiFi setup header
│   ├── telegram_bot.h        # Telegram bot header
│   ├── gpio_manager.h        # GPIO manager header
│   ├── sensor_handler.h      # Sensor handler header
│   └── automation.h          # Automation engine header
├── version/
│   ├── version.txt           # Version information
│   └── update_version.py     # Version update script
├── platformio.ini            # PlatformIO configuration
└── README.md                 # This file
```

## 🛠️ Installation

### Prerequisites
- [PlatformIO](https://platformio.org/) installed
- ESP8266 development board (NodeMCU recommended)
- Telegram Bot Token (from @BotFather)
- Your Telegram Chat ID (from @userinfobot)

### Build and Flash

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd ESPgram
   ```

2. **Build the firmware**:
   ```bash
   pio run
   ```

3. **Flash to ESP8266**:
   ```bash
   pio run --target upload
   ```

4. **Monitor serial output**:
   ```bash
   pio device monitor
   ```

## 🔧 Setup Process

### 1. First Boot
- ESP8266 starts in AP mode
- Connect to WiFi network: `ESPgram-Setup` (no password)
- Browser should automatically redirect to setup page
- If not, navigate to `***********`

### 2. Configuration
- Enter your WiFi credentials
- Enter Telegram Bot Token (get from @BotFather)
- Enter your Telegram Chat ID (get from @userinfobot)
- Click "Save Configuration"

### 3. First Connection
- ESP8266 restarts and connects to your WiFi
- Sends startup message to your Telegram
- Type `/start` in Telegram to open control panel

## 📱 Telegram Commands

### Main Menu
- `/start` or `menu` - Open main control panel
- `/help` - Show help information

### Quick Commands
- **GPIO Control**: Configure and control pins
- **Sensor Monitoring**: Add and read sensors
- **Automation**: Create smart rules
- **System**: Status, restart, reset

## 🔌 GPIO Pin Reference (ESP8266/NodeMCU)

| GPIO | NodeMCU | Notes |
|------|---------|-------|
| 0    | D3      | Boot mode pin, use with caution |
| 1    | TX      | Serial TX, avoid if using Serial |
| 2    | D4      | Built-in LED (inverted) |
| 3    | RX      | Serial RX, avoid if using Serial |
| 4    | D2      | Good for general use |
| 5    | D1      | Good for general use |
| 12   | D6      | Good for general use |
| 13   | D7      | Good for general use |
| 14   | D5      | Good for general use |
| 15   | D8      | Boot mode pin, use with caution |
| 16   | D0      | No interrupt support |
| A0   | A0      | Analog input only (0-1024) |

## 📊 Sensor Wiring

### DHT11/DHT22 (Temperature & Humidity)
```
DHT Pin 1 (VCC) → 3.3V
DHT Pin 2 (Data) → GPIO Pin (e.g., D2)
DHT Pin 3 (NC) → Not connected
DHT Pin 4 (GND) → GND
```

### LDR (Light Sensor)
```
LDR Pin 1 → A0
LDR Pin 2 → GND
10kΩ Resistor: A0 → 3.3V (pull-up)
```

### PIR (Motion Sensor)
```
PIR VCC → 3.3V or 5V
PIR OUT → GPIO Pin (e.g., D1)
PIR GND → GND
```

## 🤖 Automation Examples

### Example 1: Motion-Activated Light
- **Condition**: PIR sensor detects motion (Pin HIGH)
- **Action**: Turn on LED (Pin ON)
- **Cooldown**: 30 seconds

### Example 2: Temperature Alert
- **Condition**: DHT22 temperature > 30°C
- **Action**: Send notification
- **Repeat**: Yes (with cooldown)

### Example 3: Light-Dependent LED
- **Condition**: LDR value < 500 (dark)
- **Action**: Turn on LED with PWM 512 (50% brightness)

## 🔧 Version Management

Update firmware version using the Python script:

```bash
# Show current version
python version/update_version.py current

# Increment patch version (1.0.0 → 1.0.1)
python version/update_version.py patch

# Increment minor version (1.0.1 → 1.1.0)
python version/update_version.py minor

# Increment major version (1.1.0 → 2.0.0)
python version/update_version.py major

# Set specific version
python version/update_version.py 1.2.3
```

## 🐛 Troubleshooting

### WiFi Connection Issues
- Check SSID and password in setup
- Ensure 2.4GHz WiFi (ESP8266 doesn't support 5GHz)
- Check signal strength

### Telegram Bot Issues
- Verify bot token format (should contain `:`)
- Ensure chat ID is numeric
- Check internet connection
- Try restarting the bot

### Memory Issues
- Monitor free heap in system status
- Reduce number of sensors/automations if needed
- Check for memory leaks in serial monitor

### GPIO Issues
- Verify pin numbers (use GPIO numbers, not NodeMCU labels)
- Check wiring connections
- Avoid pins used for boot/flash

## 📈 Performance Tips

1. **Memory Optimization**:
   - Limit concurrent sensors and automations
   - Use appropriate data types
   - Monitor heap usage

2. **Network Stability**:
   - Use strong WiFi signal
   - Implement reconnection logic
   - Monitor connection status

3. **Power Management**:
   - Use appropriate power supply (5V 2A recommended)
   - Consider deep sleep for battery applications

## 🔒 Security Notes

- Bot token and chat ID are stored in EEPROM
- Only authorized chat ID can control the device
- Setup mode times out after 5 minutes
- No external web interface after setup

## 📄 License

This project is open source. Feel free to modify and distribute according to your needs.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

## 📞 Support

For support and questions:
1. Check the troubleshooting section
2. Review serial monitor output
3. Open an issue on the repository
