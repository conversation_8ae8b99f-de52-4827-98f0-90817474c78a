{"project": "ESPgram", "version": "1.0.0", "build_date": "20250602_150712", "build_timestamp": "2025-06-02T15:07:12.617888", "files": ["ESPgram_v1.0.0.bin", "ESPgram_v1.0.0.elf"], "platform": "ESP8266", "board": "NodeMCU v1.0 (ESP-12E)", "framework": "<PERSON><PERSON><PERSON><PERSON>", "features": ["WiFi Setup with Captive Portal", "Telegram Bot Control", "GPIO Management (Digital/PWM/Analog)", "Sensor Support (DHT11/DHT22/LDR/PIR)", "Automation Engine", "EEPROM Configuration Storage", "OTA Ready"], "memory_usage": {"ram": {"percentage": 44.2, "used": 36208, "total": 81920}, "flash": {"percentage": 43.3, "used": 452299, "total": 1044464}}, "installation": {"method": "PlatformIO Upload", "command": "pio run --target upload", "baud_rate": 115200, "flash_size": "4MB", "setup_instructions": ["1. Flash the firmware to ESP8266", "2. Connect to ESPgram-Setup WiFi network", "3. Navigate to 192.168.4.1 in browser", "4. <PERSON>ter W<PERSON> credentials and Telegram bot details", "5. Save configuration and restart", "6. Send /start to your Telegram bot"]}}