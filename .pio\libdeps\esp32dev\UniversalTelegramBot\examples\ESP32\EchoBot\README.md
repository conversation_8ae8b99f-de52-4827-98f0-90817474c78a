#ESP32 - Echo Bot

This is a basic example of how to use UniversalTelegramBot on ESP32 based boards.

The application will echo an message it received back to the user.

NOTE: You will need to enter your SSID, password and bot Token for the example to work.

Application originally written by [<PERSON><PERSON><PERSON><PERSON>](giancar<PERSON>.<EMAIL>) for [ESP8266-TelegramBot library](https://github.com/Gianbacchio/ESP8266-TelegramBot)

Adapted by [<PERSON>](https://github.com/witnessmenow)

## License

You may copy, distribute and modify the software provided that modifications are described and licensed for free under [LGPL-3](http://www.gnu.org/licenses/lgpl-3.0.html). Derivatives works (including modifications or anything statically linked to the library) can only be redistributed under [LGPL-3](http://www.gnu.org/licenses/lgpl-3.0.html), but applications that use the library don't have to be.
