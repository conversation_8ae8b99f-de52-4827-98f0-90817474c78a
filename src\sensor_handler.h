#ifndef SENSOR_HANDLER_H
#define SENSOR_HANDLER_H

#include <Arduino.h>
#include <Preferences.h>
#include <DHT.h>
#include "config.h"

// Forward declaration
class TelegramBot;

struct Sensor {
    uint8_t id;
    SensorType type;
    uint8_t pin;
    String name;
    float value;
    float threshold;
    bool enabled;
    bool alertEnabled;
    unsigned long lastRead;
    DHT* dhtSensor; // For DHT sensors
};

class SensorHandler {
private:
    Preferences preferences;
    Sensor sensors[MAX_SENSORS];
    int sensorCount;
    bool initialized;
    TelegramBot* telegramBot;

    // Reading intervals
    unsigned long lastSensorRead;
    const unsigned long SENSOR_READ_INTERVAL = 5000; // 5 seconds

    // Private methods
    int findFreeSensorSlot();
    int findSensorById(uint8_t id);
    int findSensorByPin(uint8_t pin);
    bool isValidSensorPin(uint8_t pin, SensorType type);
    void initializeSensor(int index);
    void readSensor(int index);
    void checkThresholds();
    void saveConfiguration();
    void loadConfiguration();

    // Command parsing
    String parseAddCommand(const String& args);
    String parseReadCommand(const String& args);
    String parseRemoveCommand(const String& args);
    String parseThresholdCommand(const String& args);
    String parseListCommand();
    String parseReadAllCommand();

    // Sensor type helpers
    SensorType parseSensorType(const String& typeStr);
    String sensorTypeToString(SensorType type);

    // DHT sensor management
    bool createDHTSensor(int index);
    void destroyDHTSensor(int index);

public:
    SensorHandler();
    ~SensorHandler();

    // Initialization
    bool begin();
    void loop();
    void setTelegramBot(TelegramBot* bot) { telegramBot = bot; }

    // Sensor management
    bool addSensor(SensorType type, uint8_t pin, const String& name = "");
    bool removeSensor(uint8_t id);
    bool enableSensor(uint8_t id, bool enable = true);
    bool setSensorThreshold(uint8_t id, float threshold);
    bool enableSensorAlert(uint8_t id, bool enable = true);

    // Reading methods
    bool readSensorValue(uint8_t id, float& value);
    String readAllSensors();

    // Information methods
    String getStatusReport();
    String getSensorInfo(uint8_t id);
    int getSensorCount() const { return sensorCount; }
    bool isSensorAvailable(uint8_t pin, SensorType type);

    // Command interface
    String executeCommand(const String& command);

    // Utility methods
    void resetAllSensors();
    String getSensorName(uint8_t id);
    SensorType getSensorType(uint8_t id);
    float getSensorValue(uint8_t id);
    float getSensorThreshold(uint8_t id);
    bool isSensorEnabled(uint8_t id);
};

#endif // SENSOR_HANDLER_H
