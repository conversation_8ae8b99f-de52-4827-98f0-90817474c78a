#ifndef SENSOR_HANDLER_H
#define SENSOR_HANDLER_H

#include <Arduino.h>
#include <DHT.h>
#include <EEPROM.h>
#include "config.h"

enum SensorType {
    SENSOR_NONE = 0,
    SENSOR_DHT11 = 1,
    SENSOR_DHT22 = 2,
    SENSOR_LDR = 3,
    SENSOR_PIR = 4,
    SENSOR_DIGITAL = 5
};

struct SensorConfig {
    uint8_t pin;
    SensorType type;
    String name;
    bool enabled;
    float lastValue;
    float threshold;
    unsigned long lastRead;
    
    SensorConfig() : pin(0), type(SENSOR_NONE), name(""), enabled(false), 
                     lastValue(0), threshold(0), lastRead(0) {}
};

class SensorHandler {
private:
    SensorConfig sensors[MAX_SENSORS];
    int sensorCount;
    DHT* dhtSensors[MAX_SENSORS];
    unsigned long lastUpdate;
    
    // Private methods
    bool isValidSensorPin(uint8_t pin, SensorType type);
    int findSensorIndex(uint8_t pin);
    void saveConfiguration();
    void loadConfiguration();
    String sensorTypeToString(SensorType type);
    SensorType stringToSensorType(String typeStr);
    float readSensorValue(int index);
    void initializeDHTSensor(int index);
    void cleanupDHTSensor(int index);
    
public:
    SensorHandler();
    ~SensorHandler();
    
    // Initialization
    void begin();
    
    // Sensor configuration
    bool addSensor(uint8_t pin, SensorType type, String name = "", float threshold = 0);
    bool removeSensor(uint8_t pin);
    bool enableSensor(uint8_t pin);
    bool disableSensor(uint8_t pin);
    bool setSensorThreshold(uint8_t pin, float threshold);
    
    // Sensor reading
    float readSensor(uint8_t pin);
    String readSensorFormatted(uint8_t pin);
    void updateAllSensors();
    String readAllSensors();
    
    // Sensor information
    String listSensors();
    String getSensorStatus(uint8_t pin);
    String getSensorName(uint8_t pin);
    SensorType getSensorType(uint8_t pin);
    float getSensorValue(uint8_t pin);
    float getSensorThreshold(uint8_t pin);
    bool isSensorEnabled(uint8_t pin);
    
    // Threshold checking
    bool checkThresholds();
    String getThresholdAlerts();
    
    // Configuration management
    void clearConfiguration();
    int getConfiguredSensorCount();
    String getAvailableSensorTypes();
    
    // Update loop
    void loop();
};

#endif // SENSOR_HANDLER_H
