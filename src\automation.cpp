#include "automation.h"
#include "gpio_manager.h"
#include "sensor_handler.h"
#include "telegram_bot.h"

AutomationEngine::AutomationEngine() : ruleCount(0), initialized(false), 
                                       gpio<PERSON><PERSON>ger(nullptr), sensor<PERSON><PERSON><PERSON>(nullptr), telegram<PERSON>ot(nullptr),
                                       lastRuleCheck(0) {
    // Initialize rule array
    for (int i = 0; i < MAX_AUTOMATIONS; i++) {
        rules[i].id = 255; // Invalid ID
        rules[i].name = "";
        rules[i].enabled = false;
        rules[i].triggerType = TRIGGER_NONE;
        rules[i].triggerPin = 255;
        rules[i].triggerSensorId = 255;
        rules[i].triggerValue = 0.0;
        rules[i].triggerInterval = 0;
        rules[i].lastTriggered = 0;
        rules[i].actionType = ACTION_NONE;
        rules[i].actionPin = 255;
        rules[i].actionValue = 0;
        rules[i].actionMessage = "";
        rules[i].hasCondition = false;
        rules[i].conditionPin = 255;
        rules[i].conditionValue = 0;
        rules[i].createdTime = 0;
    }
}

AutomationEngine::~AutomationEngine() {
    // Nothing to clean up
}

bool AutomationEngine::begin() {
    DEBUG_PRINTLN("AutomationEngine: Initializing...");
    
    preferences.begin(PREFS_NAMESPACE, false);
    loadConfiguration();
    
    initialized = true;
    DEBUG_PRINTF("AutomationEngine: Initialized with %d rules\n", ruleCount);
    return true;
}

void AutomationEngine::setModuleReferences(GPIOManager* gpio, SensorHandler* sensor, TelegramBot* bot) {
    gpioManager = gpio;
    sensorHandler = sensor;
    telegramBot = bot;
}

void AutomationEngine::loop() {
    if (!initialized) return;
    
    unsigned long currentTime = millis();
    
    // Check rules periodically
    if (currentTime - lastRuleCheck > RULE_CHECK_INTERVAL) {
        lastRuleCheck = currentTime;
        
        for (int i = 0; i < ruleCount; i++) {
            if (rules[i].id != 255 && rules[i].enabled) {
                if (evaluateTrigger(i) && evaluateCondition(i)) {
                    executeAction(i);
                    rules[i].lastTriggered = currentTime;
                }
            }
        }
    }
}

bool AutomationEngine::addRule(const String& name, TriggerType triggerType, ActionType actionType) {
    // Find free slot
    int index = findFreeRuleSlot();
    if (index == -1) {
        DEBUG_PRINTLN("AutomationEngine: No free rule slots");
        return false;
    }
    
    // Configure rule
    rules[index].id = index + 1; // IDs start from 1
    rules[index].name = name.length() > 0 ? name : ("Rule" + String(rules[index].id));
    rules[index].enabled = true;
    rules[index].triggerType = triggerType;
    rules[index].triggerPin = 255;
    rules[index].triggerSensorId = 255;
    rules[index].triggerValue = 0.0;
    rules[index].triggerInterval = 0;
    rules[index].lastTriggered = 0;
    rules[index].actionType = actionType;
    rules[index].actionPin = 255;
    rules[index].actionValue = 0;
    rules[index].actionMessage = "";
    rules[index].hasCondition = false;
    rules[index].conditionPin = 255;
    rules[index].conditionValue = 0;
    rules[index].createdTime = millis();
    
    if (index >= ruleCount) {
        ruleCount = index + 1;
    }
    
    saveConfiguration();
    
    DEBUG_PRINTF("AutomationEngine: Added rule %s (ID: %d)\n", 
                 rules[index].name.c_str(), rules[index].id);
    return true;
}

bool AutomationEngine::removeRule(uint8_t id) {
    int index = findRuleById(id);
    if (index == -1) {
        return false;
    }
    
    // Clear configuration
    rules[index].id = 255;
    rules[index].name = "";
    rules[index].enabled = false;
    rules[index].triggerType = TRIGGER_NONE;
    rules[index].triggerPin = 255;
    rules[index].triggerSensorId = 255;
    rules[index].triggerValue = 0.0;
    rules[index].triggerInterval = 0;
    rules[index].lastTriggered = 0;
    rules[index].actionType = ACTION_NONE;
    rules[index].actionPin = 255;
    rules[index].actionValue = 0;
    rules[index].actionMessage = "";
    rules[index].hasCondition = false;
    rules[index].conditionPin = 255;
    rules[index].conditionValue = 0;
    rules[index].createdTime = 0;
    
    // Update rule count
    int maxId = 0;
    for (int i = 0; i < MAX_AUTOMATIONS; i++) {
        if (rules[i].id != 255 && rules[i].id > maxId) {
            maxId = rules[i].id;
        }
    }
    ruleCount = maxId;
    
    saveConfiguration();
    
    DEBUG_PRINTF("AutomationEngine: Removed rule ID %d\n", id);
    return true;
}

bool AutomationEngine::enableRule(uint8_t id, bool enable) {
    int index = findRuleById(id);
    if (index == -1) {
        return false;
    }
    
    rules[index].enabled = enable;
    saveConfiguration();
    
    DEBUG_PRINTF("AutomationEngine: Rule ID %d %s\n", id, enable ? "enabled" : "disabled");
    return true;
}

bool AutomationEngine::triggerRule(uint8_t id) {
    int index = findRuleById(id);
    if (index == -1 || !rules[index].enabled) {
        return false;
    }
    
    if (evaluateCondition(index)) {
        executeAction(index);
        rules[index].lastTriggered = millis();
        return true;
    }
    
    return false;
}

// Configuration methods
bool AutomationEngine::setRuleTrigger(uint8_t id, TriggerType type, uint8_t pin, float value) {
    int index = findRuleById(id);
    if (index == -1) {
        return false;
    }
    
    rules[index].triggerType = type;
    rules[index].triggerPin = pin;
    rules[index].triggerValue = value;
    saveConfiguration();
    
    return true;
}

bool AutomationEngine::setRuleAction(uint8_t id, ActionType type, uint8_t pin, int value, const String& message) {
    int index = findRuleById(id);
    if (index == -1) {
        return false;
    }
    
    rules[index].actionType = type;
    rules[index].actionPin = pin;
    rules[index].actionValue = value;
    rules[index].actionMessage = message;
    saveConfiguration();
    
    return true;
}

bool AutomationEngine::setRuleCondition(uint8_t id, uint8_t pin, int value) {
    int index = findRuleById(id);
    if (index == -1) {
        return false;
    }
    
    rules[index].hasCondition = true;
    rules[index].conditionPin = pin;
    rules[index].conditionValue = value;
    saveConfiguration();
    
    return true;
}

bool AutomationEngine::setRuleInterval(uint8_t id, unsigned long interval) {
    int index = findRuleById(id);
    if (index == -1) {
        return false;
    }
    
    rules[index].triggerInterval = interval;
    saveConfiguration();
    
    return true;
}

// Private helper methods
int AutomationEngine::findFreeRuleSlot() {
    for (int i = 0; i < MAX_AUTOMATIONS; i++) {
        if (rules[i].id == 255) {
            return i;
        }
    }
    return -1;
}

int AutomationEngine::findRuleById(uint8_t id) {
    for (int i = 0; i < MAX_AUTOMATIONS; i++) {
        if (rules[i].id == id) {
            return i;
        }
    }
    return -1;
}

bool AutomationEngine::evaluateTrigger(int ruleIndex) {
    if (ruleIndex < 0 || ruleIndex >= MAX_AUTOMATIONS || rules[ruleIndex].id == 255) {
        return false;
    }
    
    AutomationRule& rule = rules[ruleIndex];
    unsigned long currentTime = millis();
    
    switch (rule.triggerType) {
        case TRIGGER_PIN_HIGH:
            if (gpioManager && rule.triggerPin != 255) {
                return gpioManager->getPinValue(rule.triggerPin) == 1;
            }
            break;
            
        case TRIGGER_PIN_LOW:
            if (gpioManager && rule.triggerPin != 255) {
                return gpioManager->getPinValue(rule.triggerPin) == 0;
            }
            break;
            
        case TRIGGER_SENSOR_THRESHOLD:
            if (sensorHandler && rule.triggerSensorId != 255) {
                float sensorValue = sensorHandler->getSensorValue(rule.triggerSensorId);
                return sensorValue > rule.triggerValue;
            }
            break;
            
        case TRIGGER_TIME_INTERVAL:
            if (rule.triggerInterval > 0) {
                return (currentTime - rule.lastTriggered) > rule.triggerInterval;
            }
            break;
            
        default:
            return false;
    }
    
    return false;
}

bool AutomationEngine::evaluateCondition(int ruleIndex) {
    if (ruleIndex < 0 || ruleIndex >= MAX_AUTOMATIONS || rules[ruleIndex].id == 255) {
        return false;
    }
    
    AutomationRule& rule = rules[ruleIndex];
    
    // If no condition is set, always return true
    if (!rule.hasCondition || rule.conditionPin == 255) {
        return true;
    }
    
    // Check condition
    if (gpioManager) {
        int pinValue = gpioManager->getPinValue(rule.conditionPin);
        return pinValue == rule.conditionValue;
    }
    
    return true;
}

void AutomationEngine::executeAction(int ruleIndex) {
    if (ruleIndex < 0 || ruleIndex >= MAX_AUTOMATIONS || rules[ruleIndex].id == 255) {
        return;
    }

    AutomationRule& rule = rules[ruleIndex];

    switch (rule.actionType) {
        case ACTION_PIN_HIGH:
            if (gpioManager && rule.actionPin != 255) {
                gpioManager->digitalWrite(rule.actionPin, 1);
            }
            break;

        case ACTION_PIN_LOW:
            if (gpioManager && rule.actionPin != 255) {
                gpioManager->digitalWrite(rule.actionPin, 0);
            }
            break;

        case ACTION_PIN_TOGGLE:
            if (gpioManager && rule.actionPin != 255) {
                gpioManager->togglePin(rule.actionPin);
            }
            break;

        case ACTION_PWM_SET:
            if (gpioManager && rule.actionPin != 255) {
                gpioManager->pwmWrite(rule.actionPin, rule.actionValue);
            }
            break;

        case ACTION_SEND_MESSAGE:
            if (telegramBot && rule.actionMessage.length() > 0) {
                String message = "🤖 *Automation Triggered*\n\n";
                message += "Rule: " + rule.name + "\n";
                message += "Message: " + rule.actionMessage;
                telegramBot->sendMessage(message);
            }
            break;

        default:
            break;
    }

    // Notify via Telegram if bot is available
    if (telegramBot) {
        String notification = "⚙️ Automation: " + rule.name + " triggered";
        telegramBot->sendMessage(notification);
    }

    DEBUG_PRINTF("AutomationEngine: Executed action for rule %s\n", rule.name.c_str());
}

// Status and information methods
String AutomationEngine::getStatusReport() {
    String report = "";

    if (ruleCount == 0) {
        report = "No automation rules configured.\n\n";
        report += "Use `/automation add` to create rules.\n";
        report += "Example: Create rule with guided setup";
        return report;
    }

    for (int i = 0; i < ruleCount; i++) {
        if (rules[i].id != 255) {
            report += "*" + rules[i].name + "* (ID: " + String(rules[i].id) + ")\n";
            report += "• Status: " + String(rules[i].enabled ? "Enabled" : "Disabled") + "\n";
            report += "• Trigger: " + triggerTypeToString(rules[i].triggerType);

            if (rules[i].triggerPin != 255) {
                report += " (Pin " + String(rules[i].triggerPin) + ")";
            }
            if (rules[i].triggerSensorId != 255) {
                report += " (Sensor " + String(rules[i].triggerSensorId) + ")";
            }
            if (rules[i].triggerValue != 0.0) {
                report += " > " + String(rules[i].triggerValue, 2);
            }

            report += "\n• Action: " + actionTypeToString(rules[i].actionType);

            if (rules[i].actionPin != 255) {
                report += " (Pin " + String(rules[i].actionPin) + ")";
            }
            if (rules[i].actionValue != 0) {
                report += " = " + String(rules[i].actionValue);
            }
            if (rules[i].actionMessage.length() > 0) {
                report += " (" + rules[i].actionMessage + ")";
            }

            if (rules[i].hasCondition) {
                report += "\n• Condition: Pin " + String(rules[i].conditionPin) +
                         " = " + String(rules[i].conditionValue);
            }

            if (rules[i].lastTriggered > 0) {
                report += "\n• Last: " + String((millis() - rules[i].lastTriggered) / 1000) + "s ago";
            }

            report += "\n\n";
        }
    }

    return report;
}

String AutomationEngine::getRuleInfo(uint8_t id) {
    int index = findRuleById(id);
    if (index == -1) {
        return "Rule ID " + String(id) + " not found";
    }

    String info = rules[index].name + " (ID: " + String(id) + ")\n";
    info += "Status: " + String(rules[index].enabled ? "Enabled" : "Disabled") + "\n";
    info += "Trigger: " + triggerTypeToString(rules[index].triggerType) + "\n";
    info += "Action: " + actionTypeToString(rules[index].actionType) + "\n";

    if (rules[index].lastTriggered > 0) {
        info += "Last Triggered: " + String((millis() - rules[index].lastTriggered) / 1000) + "s ago\n";
    }

    return info;
}

int AutomationEngine::getActiveRuleCount() const {
    int activeCount = 0;
    for (int i = 0; i < ruleCount; i++) {
        if (rules[i].id != 255 && rules[i].enabled) {
            activeCount++;
        }
    }
    return activeCount;
}

// Command interface
String AutomationEngine::executeCommand(const String& command) {
    String cmd = command;
    cmd.toLowerCase();
    cmd.trim();

    if (cmd == "add") {
        return parseAddCommand("");
    }
    else if (cmd.startsWith("enable ")) {
        return parseEnableCommand(cmd.substring(7));
    }
    else if (cmd.startsWith("disable ")) {
        return parseDisableCommand(cmd.substring(8));
    }
    else if (cmd.startsWith("remove ")) {
        return parseRemoveCommand(cmd.substring(7));
    }
    else if (cmd.startsWith("trigger ")) {
        return parseTriggerCommand(cmd.substring(8));
    }
    else if (cmd == "list") {
        return parseListCommand();
    }
    else {
        return "❓ Unknown automation command. Available: add, enable, disable, remove, trigger, list";
    }
}

String AutomationEngine::parseAddCommand(const String& args) {
    // Simple rule creation - in a real implementation, this could be more interactive
    String ruleName = "Rule" + String(ruleCount + 1);

    if (addRule(ruleName, TRIGGER_NONE, ACTION_NONE)) {
        return "✅ Rule created with ID " + String(ruleCount) + "\n" +
               "Configure it with:\n" +
               "• Set trigger: Contact developer for advanced setup\n" +
               "• Set action: Contact developer for advanced setup\n" +
               "This is a basic implementation.";
    } else {
        return "❌ Failed to create rule (no free slots)";
    }
}

String AutomationEngine::parseEnableCommand(const String& args) {
    int id = args.toInt();

    if (enableRule(id, true)) {
        return "✅ Rule ID " + String(id) + " enabled";
    } else {
        return "❌ Rule ID " + String(id) + " not found";
    }
}

String AutomationEngine::parseDisableCommand(const String& args) {
    int id = args.toInt();

    if (enableRule(id, false)) {
        return "⏸️ Rule ID " + String(id) + " disabled";
    } else {
        return "❌ Rule ID " + String(id) + " not found";
    }
}

String AutomationEngine::parseRemoveCommand(const String& args) {
    int id = args.toInt();

    if (removeRule(id)) {
        return "🗑️ Rule ID " + String(id) + " removed";
    } else {
        return "❌ Rule ID " + String(id) + " not found";
    }
}

String AutomationEngine::parseTriggerCommand(const String& args) {
    int id = args.toInt();

    if (triggerRule(id)) {
        return "⚡ Rule ID " + String(id) + " triggered manually";
    } else {
        return "❌ Rule ID " + String(id) + " not found or disabled";
    }
}

String AutomationEngine::parseListCommand() {
    return getStatusReport();
}

// Utility methods
void AutomationEngine::resetAllRules() {
    for (int i = 0; i < ruleCount; i++) {
        if (rules[i].id != 255) {
            removeRule(rules[i].id);
        }
    }
    ruleCount = 0;
    saveConfiguration();
}

bool AutomationEngine::isRuleEnabled(uint8_t id) {
    int index = findRuleById(id);
    return (index != -1) ? rules[index].enabled : false;
}

String AutomationEngine::getRuleName(uint8_t id) {
    int index = findRuleById(id);
    return (index != -1) ? rules[index].name : "";
}

// Configuration management
void AutomationEngine::saveConfiguration() {
    if (!initialized) return;

    // Save rule configurations
    for (int i = 0; i < ruleCount; i++) {
        if (rules[i].id != 255) {
            String key = "rule_" + String(i);
            String config = String(rules[i].id) + "," +
                           rules[i].name + "," +
                           String(rules[i].enabled ? 1 : 0) + "," +
                           String(rules[i].triggerType) + "," +
                           String(rules[i].triggerPin) + "," +
                           String(rules[i].triggerSensorId) + "," +
                           String(rules[i].triggerValue, 2) + "," +
                           String(rules[i].triggerInterval) + "," +
                           String(rules[i].actionType) + "," +
                           String(rules[i].actionPin) + "," +
                           String(rules[i].actionValue) + "," +
                           rules[i].actionMessage + "," +
                           String(rules[i].hasCondition ? 1 : 0) + "," +
                           String(rules[i].conditionPin) + "," +
                           String(rules[i].conditionValue);
            preferences.putString(key.c_str(), config);
        }
    }

    preferences.putInt("rule_count", ruleCount);
}

void AutomationEngine::loadConfiguration() {
    ruleCount = preferences.getInt("rule_count", 0);

    for (int i = 0; i < ruleCount && i < MAX_AUTOMATIONS; i++) {
        String key = "rule_" + String(i);
        String config = preferences.getString(key.c_str(), "");

        if (config.length() > 0) {
            // Parse configuration string (simplified parsing)
            int commaCount = 0;
            int lastIndex = 0;
            String parts[15];

            for (int j = 0; j < config.length() && commaCount < 14; j++) {
                if (config.charAt(j) == ',') {
                    parts[commaCount] = config.substring(lastIndex, j);
                    lastIndex = j + 1;
                    commaCount++;
                }
            }
            parts[commaCount] = config.substring(lastIndex);

            if (commaCount >= 14) {
                rules[i].id = parts[0].toInt();
                rules[i].name = parts[1];
                rules[i].enabled = parts[2].toInt() == 1;
                rules[i].triggerType = (TriggerType)parts[3].toInt();
                rules[i].triggerPin = parts[4].toInt();
                rules[i].triggerSensorId = parts[5].toInt();
                rules[i].triggerValue = parts[6].toFloat();
                rules[i].triggerInterval = parts[7].toInt();
                rules[i].actionType = (ActionType)parts[8].toInt();
                rules[i].actionPin = parts[9].toInt();
                rules[i].actionValue = parts[10].toInt();
                rules[i].actionMessage = parts[11];
                rules[i].hasCondition = parts[12].toInt() == 1;
                rules[i].conditionPin = parts[13].toInt();
                rules[i].conditionValue = parts[14].toInt();
                rules[i].lastTriggered = 0;
                rules[i].createdTime = millis();
            }
        }
    }
}

// Helper methods
String AutomationEngine::triggerTypeToString(TriggerType type) {
    switch (type) {
        case TRIGGER_PIN_HIGH: return "Pin High";
        case TRIGGER_PIN_LOW: return "Pin Low";
        case TRIGGER_SENSOR_THRESHOLD: return "Sensor Threshold";
        case TRIGGER_TIME_INTERVAL: return "Time Interval";
        default: return "None";
    }
}

String AutomationEngine::actionTypeToString(ActionType type) {
    switch (type) {
        case ACTION_PIN_HIGH: return "Pin High";
        case ACTION_PIN_LOW: return "Pin Low";
        case ACTION_PIN_TOGGLE: return "Pin Toggle";
        case ACTION_PWM_SET: return "PWM Set";
        case ACTION_SEND_MESSAGE: return "Send Message";
        default: return "None";
    }
}

TriggerType AutomationEngine::parseTriggerType(const String& typeStr) {
    String type = typeStr;
    type.toLowerCase();

    if (type == "pin_high" || type == "high") return TRIGGER_PIN_HIGH;
    if (type == "pin_low" || type == "low") return TRIGGER_PIN_LOW;
    if (type == "sensor" || type == "threshold") return TRIGGER_SENSOR_THRESHOLD;
    if (type == "time" || type == "interval") return TRIGGER_TIME_INTERVAL;

    return TRIGGER_NONE;
}

ActionType AutomationEngine::parseActionType(const String& typeStr) {
    String type = typeStr;
    type.toLowerCase();

    if (type == "pin_high" || type == "high") return ACTION_PIN_HIGH;
    if (type == "pin_low" || type == "low") return ACTION_PIN_LOW;
    if (type == "toggle") return ACTION_PIN_TOGGLE;
    if (type == "pwm") return ACTION_PWM_SET;
    if (type == "message" || type == "msg") return ACTION_SEND_MESSAGE;

    return ACTION_NONE;
}
