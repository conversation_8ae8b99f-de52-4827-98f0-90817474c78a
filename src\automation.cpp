#include "automation.h"
#include "gpio_manager.h"
#include "sensor_handler.h"
#include "telegram_bot.h"

AutomationEngine::AutomationEngine() : ruleCount(0), lastCheck(0),
                                       gpioManager(nullptr), sensor<PERSON><PERSON><PERSON>(nullptr), telegram<PERSON>ot(nullptr) {
    // Initialize rules array
    for (int i = 0; i < MAX_AUTOMATIONS; i++) {
        rules[i] = AutomationRule();
    }
}

void AutomationEngine::begin() {
    loadConfiguration();
    Serial.println("Automation Engine initialized with " + String(ruleCount) + " rules");
}

void AutomationEngine::setModuleReferences(GPIOManager* gpio, SensorHandler* sensor, TelegramBot* telegram) {
    gpioManager = gpio;
    sensorHandler = sensor;
    telegramBot = telegram;
}

int AutomationEngine::findRuleIndex(String name) {
    for (int i = 0; i < ruleCount; i++) {
        if (rules[i].name.equals(name)) {
            return i;
        }
    }
    return -1;
}

bool AutomationEngine::addRule(String name, ConditionType condType, uint8_t condPin, float condValue,
                               ActionType actType, uint8_t actPin, int actValue, String actMessage) {
    // Check if rule already exists
    int index = findRuleIndex(name);
    if (index == -1) {
        // Add new rule
        if (ruleCount >= MAX_AUTOMATIONS) {
            Serial.println("Maximum automation rule count reached");
            return false;
        }
        index = ruleCount++;
    }
    
    // Configure rule
    rules[index].name = name;
    rules[index].enabled = true;
    rules[index].conditionType = condType;
    rules[index].conditionPin = condPin;
    rules[index].conditionValue = condValue;
    rules[index].actionType = actType;
    rules[index].actionPin = actPin;
    rules[index].actionValue = actValue;
    rules[index].actionMessage = actMessage;
    rules[index].lastTriggered = 0;
    rules[index].repeatAction = false;
    rules[index].cooldownPeriod = 5000; // Default 5 second cooldown
    
    saveConfiguration();
    Serial.println("Automation rule added: " + name);
    return true;
}

bool AutomationEngine::removeRule(String name) {
    int index = findRuleIndex(name);
    if (index == -1) {
        return false;
    }
    
    // Shift remaining rules
    for (int i = index; i < ruleCount - 1; i++) {
        rules[i] = rules[i + 1];
    }
    ruleCount--;
    
    saveConfiguration();
    Serial.println("Automation rule removed: " + name);
    return true;
}

bool AutomationEngine::enableRule(String name) {
    int index = findRuleIndex(name);
    if (index == -1) {
        return false;
    }
    
    rules[index].enabled = true;
    saveConfiguration();
    return true;
}

bool AutomationEngine::disableRule(String name) {
    int index = findRuleIndex(name);
    if (index == -1) {
        return false;
    }
    
    rules[index].enabled = false;
    saveConfiguration();
    return true;
}

bool AutomationEngine::setRuleCooldown(String name, unsigned long cooldown) {
    int index = findRuleIndex(name);
    if (index == -1) {
        return false;
    }
    
    rules[index].cooldownPeriod = cooldown;
    saveConfiguration();
    return true;
}

bool AutomationEngine::setRuleRepeat(String name, bool repeat) {
    int index = findRuleIndex(name);
    if (index == -1) {
        return false;
    }
    
    rules[index].repeatAction = repeat;
    saveConfiguration();
    return true;
}

String AutomationEngine::listRules() {
    if (ruleCount == 0) {
        return "📝 No automation rules configured.";
    }
    
    String list = "⚙️ *Automation Rules:*\n\n";
    
    for (int i = 0; i < ruleCount; i++) {
        String status = rules[i].enabled ? "✅" : "❌";
        list += status + " **" + rules[i].name + "**\n";
        list += "   🔍 Condition: " + conditionTypeToString(rules[i].conditionType);
        
        if (rules[i].conditionType != CONDITION_TIME_INTERVAL) {
            list += " (Pin " + String(rules[i].conditionPin) + ")";
        }
        
        if (rules[i].conditionValue != 0) {
            list += " = " + String(rules[i].conditionValue, 1);
        }
        list += "\n";
        
        list += "   🎯 Action: " + actionTypeToString(rules[i].actionType);
        if (rules[i].actionType != ACTION_SEND_MESSAGE && rules[i].actionType != ACTION_SEND_NOTIFICATION) {
            list += " (Pin " + String(rules[i].actionPin) + ")";
        }
        list += "\n";
        
        if (rules[i].cooldownPeriod > 0) {
            list += "   ⏱️ Cooldown: " + String(rules[i].cooldownPeriod / 1000) + "s\n";
        }
        list += "\n";
    }
    
    return list;
}

String AutomationEngine::getRuleStatus(String name) {
    int index = findRuleIndex(name);
    if (index == -1) {
        return "Rule not found";
    }
    
    String status = "⚙️ **" + name + "**\n";
    status += "⚡ Status: " + String(rules[index].enabled ? "Enabled" : "Disabled") + "\n";
    status += "🔍 Condition: " + conditionTypeToString(rules[index].conditionType) + "\n";
    status += "🎯 Action: " + actionTypeToString(rules[index].actionType) + "\n";
    status += "🔄 Repeat: " + String(rules[index].repeatAction ? "Yes" : "No") + "\n";
    status += "⏱️ Cooldown: " + String(rules[index].cooldownPeriod / 1000) + "s\n";
    
    if (rules[index].lastTriggered > 0) {
        status += "🕒 Last Triggered: " + String((millis() - rules[index].lastTriggered) / 1000) + "s ago";
    } else {
        status += "🕒 Never triggered";
    }
    
    return status;
}

bool AutomationEngine::evaluateCondition(int ruleIndex) {
    if (!gpioManager || !sensorHandler) {
        return false;
    }
    
    AutomationRule& rule = rules[ruleIndex];
    
    switch (rule.conditionType) {
        case CONDITION_SENSOR_GREATER: {
            float sensorValue = sensorHandler->readSensor(rule.conditionPin);
            return !isnan(sensorValue) && sensorValue > rule.conditionValue;
        }
        case CONDITION_SENSOR_LESS: {
            float sensorValue = sensorHandler->readSensor(rule.conditionPin);
            return !isnan(sensorValue) && sensorValue < rule.conditionValue;
        }
        case CONDITION_SENSOR_EQUAL: {
            float sensorValue = sensorHandler->readSensor(rule.conditionPin);
            return !isnan(sensorValue) && abs(sensorValue - rule.conditionValue) < 0.1;
        }
        case CONDITION_PIN_HIGH:
            return gpioManager->digitalRead(rule.conditionPin);
        case CONDITION_PIN_LOW:
            return !gpioManager->digitalRead(rule.conditionPin);
        case CONDITION_TIME_INTERVAL:
            return (millis() - rule.lastTriggered) >= rule.conditionInterval;
        default:
            return false;
    }
}

void AutomationEngine::executeAction(int ruleIndex) {
    if (!gpioManager || !telegramBot) {
        return;
    }
    
    AutomationRule& rule = rules[ruleIndex];
    
    switch (rule.actionType) {
        case ACTION_PIN_ON:
            gpioManager->digitalWrite(rule.actionPin, true);
            break;
        case ACTION_PIN_OFF:
            gpioManager->digitalWrite(rule.actionPin, false);
            break;
        case ACTION_PIN_TOGGLE:
            gpioManager->togglePin(rule.actionPin);
            break;
        case ACTION_PIN_PWM:
            gpioManager->analogWrite(rule.actionPin, rule.actionValue);
            break;
        case ACTION_SEND_MESSAGE:
            telegramBot->sendNotification(rule.actionMessage);
            break;
        case ACTION_SEND_NOTIFICATION:
            telegramBot->sendAutomationTrigger(rule.name, rule.actionMessage);
            break;
        default:
            break;
    }
    
    rule.lastTriggered = millis();
    
    // Send automation trigger notification
    String actionDesc = actionTypeToString(rule.actionType);
    if (rule.actionType != ACTION_SEND_MESSAGE && rule.actionType != ACTION_SEND_NOTIFICATION) {
        actionDesc += " Pin " + String(rule.actionPin);
    }
    telegramBot->sendAutomationTrigger(rule.name, actionDesc);
}

bool AutomationEngine::isInCooldown(int ruleIndex) {
    AutomationRule& rule = rules[ruleIndex];
    return (millis() - rule.lastTriggered) < rule.cooldownPeriod;
}

void AutomationEngine::checkRules() {
    for (int i = 0; i < ruleCount; i++) {
        if (!rules[i].enabled) {
            continue;
        }
        
        // Check cooldown
        if (!rules[i].repeatAction && isInCooldown(i)) {
            continue;
        }
        
        // Evaluate condition
        if (evaluateCondition(i)) {
            executeAction(i);
        }
    }
    
    lastCheck = millis();
}

void AutomationEngine::loop() {
    if (millis() - lastCheck >= AUTOMATION_CHECK_INTERVAL) {
        checkRules();
    }
}

bool AutomationEngine::triggerRule(String name) {
    int index = findRuleIndex(name);
    if (index == -1 || !rules[index].enabled) {
        return false;
    }
    
    executeAction(index);
    return true;
}

String AutomationEngine::conditionTypeToString(ConditionType type) {
    switch (type) {
        case CONDITION_SENSOR_GREATER: return "Sensor >";
        case CONDITION_SENSOR_LESS: return "Sensor <";
        case CONDITION_SENSOR_EQUAL: return "Sensor =";
        case CONDITION_PIN_HIGH: return "Pin HIGH";
        case CONDITION_PIN_LOW: return "Pin LOW";
        case CONDITION_TIME_INTERVAL: return "Time Interval";
        default: return "Unknown";
    }
}

String AutomationEngine::actionTypeToString(ActionType type) {
    switch (type) {
        case ACTION_PIN_ON: return "Pin ON";
        case ACTION_PIN_OFF: return "Pin OFF";
        case ACTION_PIN_TOGGLE: return "Pin TOGGLE";
        case ACTION_PIN_PWM: return "Pin PWM";
        case ACTION_SEND_MESSAGE: return "Send Message";
        case ACTION_SEND_NOTIFICATION: return "Send Notification";
        default: return "Unknown";
    }
}

ConditionType AutomationEngine::stringToConditionType(String typeStr) {
    typeStr.toLowerCase();
    if (typeStr.indexOf("greater") >= 0 || typeStr.indexOf(">") >= 0) return CONDITION_SENSOR_GREATER;
    if (typeStr.indexOf("less") >= 0 || typeStr.indexOf("<") >= 0) return CONDITION_SENSOR_LESS;
    if (typeStr.indexOf("equal") >= 0 || typeStr.indexOf("=") >= 0) return CONDITION_SENSOR_EQUAL;
    if (typeStr.indexOf("high") >= 0) return CONDITION_PIN_HIGH;
    if (typeStr.indexOf("low") >= 0) return CONDITION_PIN_LOW;
    if (typeStr.indexOf("time") >= 0 || typeStr.indexOf("interval") >= 0) return CONDITION_TIME_INTERVAL;
    return CONDITION_NONE;
}

ActionType AutomationEngine::stringToActionType(String typeStr) {
    typeStr.toLowerCase();
    if (typeStr.indexOf("on") >= 0 || typeStr.indexOf("high") >= 0) return ACTION_PIN_ON;
    if (typeStr.indexOf("off") >= 0 || typeStr.indexOf("low") >= 0) return ACTION_PIN_OFF;
    if (typeStr.indexOf("toggle") >= 0) return ACTION_PIN_TOGGLE;
    if (typeStr.indexOf("pwm") >= 0) return ACTION_PIN_PWM;
    if (typeStr.indexOf("message") >= 0) return ACTION_SEND_MESSAGE;
    if (typeStr.indexOf("notification") >= 0) return ACTION_SEND_NOTIFICATION;
    return ACTION_NONE;
}

void AutomationEngine::saveConfiguration() {
    // Save rule count
    EEPROM.put(AUTOMATION_CONFIG_ADDR, ruleCount);
    
    // Save automation rules
    for (int i = 0; i < ruleCount && i < MAX_AUTOMATIONS; i++) {
        int addr = AUTOMATION_CONFIG_ADDR + 4 + (i * sizeof(AutomationRule));
        EEPROM.put(addr, rules[i]);
    }
    
    EEPROM.commit();
}

void AutomationEngine::loadConfiguration() {
    // Load rule count
    EEPROM.get(AUTOMATION_CONFIG_ADDR, ruleCount);
    
    // Validate rule count
    if (ruleCount < 0 || ruleCount > MAX_AUTOMATIONS) {
        ruleCount = 0;
        return;
    }
    
    // Load automation rules
    for (int i = 0; i < ruleCount; i++) {
        int addr = AUTOMATION_CONFIG_ADDR + 4 + (i * sizeof(AutomationRule));
        EEPROM.get(addr, rules[i]);
    }
}

void AutomationEngine::clearConfiguration() {
    ruleCount = 0;
    saveConfiguration();
}

bool AutomationEngine::isRuleEnabled(String name) {
    int index = findRuleIndex(name);
    return index != -1 ? rules[index].enabled : false;
}

int AutomationEngine::getRuleCount() {
    return ruleCount;
}

String AutomationEngine::getAvailableConditions() {
    String conditions = "🔍 *Available Conditions:*\n\n";
    conditions += "📊 **Sensor >** - Sensor value greater than threshold\n";
    conditions += "📊 **Sensor <** - Sensor value less than threshold\n";
    conditions += "📊 **Sensor =** - Sensor value equals threshold\n";
    conditions += "🔌 **Pin HIGH** - Digital pin reads HIGH\n";
    conditions += "🔌 **Pin LOW** - Digital pin reads LOW\n";
    conditions += "⏰ **Time Interval** - Trigger every X milliseconds\n";
    
    return conditions;
}

String AutomationEngine::getAvailableActions() {
    String actions = "🎯 *Available Actions:*\n\n";
    actions += "🔌 **Pin ON** - Set digital pin HIGH\n";
    actions += "🔌 **Pin OFF** - Set digital pin LOW\n";
    actions += "🔄 **Pin TOGGLE** - Toggle digital pin state\n";
    actions += "📊 **Pin PWM** - Set PWM value (0-1023)\n";
    actions += "💬 **Send Message** - Send custom message\n";
    actions += "🔔 **Send Notification** - Send automation notification\n";
    
    return actions;
}
