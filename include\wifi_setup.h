#ifndef WIFI_SETUP_H
#define WIFI_SETUP_H

#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include <EEPROM.h>
#include "config.h"

class WiFiSetup {
private:
    ESP8266WebServer server;
    bool setupMode;
    unsigned long setupStartTime;
    
    // Configuration structure
    struct Config {
        char ssid[MAX_SSID_LENGTH];
        char password[MAX_PASS_LENGTH];
        char botToken[MAX_TOKEN_LENGTH];
        char chatId[MAX_CHAT_ID_LENGTH];
        bool configured;
    };
    
    Config config;
    
    // Private methods
    void startAccessPoint();
    void setupWebServer();
    void handleRoot();
    void handleSave();
    void handleNotFound();
    bool saveConfig();
    bool loadConfig();
    void clearConfig();
    String urlDecode(String str);
    
public:
    WiFiSetup();
    
    // Public methods
    void begin();
    void handleClient();
    bool isConfigured();
    bool connectToWiFi();
    void startSetupMode();
    void stopSetupMode();
    bool isSetupMode();
    
    // Getters for configuration
    String getSSID();
    String getPassword();
    String getBotToken();
    String getChatId();
    
    // Configuration management
    void resetConfiguration();
    bool isSetupTimeout();
};

#endif // WIFI_SETUP_H
