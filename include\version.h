#ifndef VERSION_H
#define VERSION_H

// ESPgram Version Information
// Auto-generated by pre_build.py - DO NOT EDIT MANUALLY

#define FIRMWARE_VERSION_MAJOR 1
#define FIRMWARE_VERSION_MINOR 0
#define FIRMWARE_VERSION_PATCH 0
#define FIRMWARE_VERSION_BUILD 1

#define FIRMWARE_VERSION_STRING "1.0.0"
#define FIRMWARE_BUILD_DATE "2025-06-02"
#define FIRMWARE_BUILD_TIME "17:42:13"
#define FIRMWARE_BUILD_TIMESTAMP "2025-06-02 17:42:13"

// Developer Information
#define FIRMWARE_DEVELOPER "SK Raihan"
#define FIRMWARE_ORGANIZATION "SKR Electronics Lab"
#define FIRMWARE_EMAIL "<EMAIL>"
#define FIRMWARE_WEBSITE "skrelectronicslab.com"

// Project Information
#define PROJECT_NAME "ESPgram"
#define PROJECT_DESCRIPTION "Revolutionary IoT Control System"
#define PROJECT_LICENSE "Custom License - SK Raihan"

// Build Information
#define BUILD_ENVIRONMENT "PlatformIO"
#define BUILD_PLATFORM "ESP32"
#define BUILD_FRAMEWORK "Arduino"

#endif // VERSION_H
