@echo off
echo ESPgram Firmware Flash Script
echo ===============================
echo.
echo Make sure your ESP8266 is connected and in download mode
echo Press any key to continue...
pause >nul
echo.
echo Flashing firmware...
esptool.py --port COM4 --baud 921600 write_flash 0x0 ESPgram_v1.0.0.bin
echo.
if %errorlevel% equ 0 (
    echo ✅ Firmware flashed successfully!
    echo Connect to ESPgram-Setup WiFi to configure
) else (
    echo ❌ Flash failed. Check connection and try again.
)
pause
