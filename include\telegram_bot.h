#ifndef TELEGRAM_BOT_H
#define TELEGRAM_BOT_H

#include <Arduino.h>
#include <WiFiClientSecure.h>
#include <UniversalTelegramBot.h>
#include "config.h"

// Forward declarations
class GPIOManager;
class SensorHandler;
class AutomationEngine;

class TelegramBot {
private:
    WiFiClientSecure client;
    UniversalTelegramBot* bot;
    String botToken;
    String chatId;
    unsigned long lastBotRan;
    bool initialized;
    
    // References to other modules
    GPIOManager* gpioManager;
    SensorHandler* sensorHandler;
    AutomationEngine* automationEngine;
    
    // Command handling
    void handleNewMessages(int numNewMessages);
    void handleCallbackQuery(String chatId, String callbackData, String queryId);
    void processCommand(String chatId, String text, String from_name);
    void sendMainMenu(String chatId);
    void sendHelpMessage(String chatId);
    void sendGPIOMenu(String chatId);
    void sendSensorMenu(String chatId);
    void sendAutomationMenu(String chatId);
    void sendSystemMenu(String chatId);

    // Command processors
    void handleGPIOCommand(String chatId, String command);
    void handleSensorCommand(String chatId, String command);
    void handleAutomationCommand(String chatId, String command);
    void handleSystemCommand(String chatId, String command);

    // New detailed command handlers
    void handleGPIOAdd(String chatId, String command);
    void handleGPIOControl(String chatId, String command, bool state);
    void handleGPIOToggle(String chatId, String command);
    void handleGPIOPWM(String chatId, String command);
    void handleGPIORead(String chatId, String command);
    void handleGPIORemove(String chatId, String command);

    void handleSensorAdd(String chatId, String command);
    void handleSensorRead(String chatId, String command);
    void handleSensorRemove(String chatId, String command);

    void handleAutomationAdd(String chatId, String command);
    void handleAutomationEnable(String chatId, String command, bool enable);
    void handleAutomationRemove(String chatId, String command);

    void handleSystemReset(String chatId);
    
    // Utility functions
    void sendMessage(String chatId, String text);
    void sendMessageWithKeyboard(String chatId, String text, String keyboard);
    String createInlineKeyboard(String buttons[][2], int buttonCount);
    bool isAuthorized(String chatId);
    
public:
    TelegramBot();
    ~TelegramBot();
    
    // Initialization
    bool begin(String token, String chat_id);
    void setModuleReferences(GPIOManager* gpio, SensorHandler* sensor, AutomationEngine* automation);
    
    // Main loop
    void handleMessages();
    
    // Public messaging
    void sendStartupMessage();
    void sendNotification(String message);
    void sendSensorUpdate(String sensorName, String value);
    void sendAutomationTrigger(String ruleName, String action);
    
    // Status
    bool isInitialized();
    void restart();
};

#endif // TELEGRAM_BOT_H
