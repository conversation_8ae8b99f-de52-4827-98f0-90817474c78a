#ifndef CONFIG_H
#define CONFIG_H

// Firmware version
#define FW_VERSION "1.0.0"
#define FW_NAME "ESPgram"

// WiFi AP Configuration
#define AP_SSID "ESPgram-Setup"
#define AP_PASSWORD ""  // Open AP for setup
#define AP_IP IPAddress(192, 168, 4, 1)
#define AP_GATEWAY IPAddress(192, 168, 4, 1)
#define AP_SUBNET IPAddress(255, 255, 255, 0)

// Web server settings
#define WEB_SERVER_PORT 80
#define SETUP_TIMEOUT 300000  // 5 minutes timeout for setup mode

// EEPROM Configuration
#define EEPROM_SIZE 1024
#define WIFI_SSID_ADDR 0
#define WIFI_PASS_ADDR 64
#define BOT_TOKEN_ADDR 128
#define CHAT_ID_ADDR 192
#define CONFIG_FLAG_ADDR 256
#define GPIO_CONFIG_ADDR 260        // 4 + (16 * 32) = 516 bytes max
#define SENSOR_CONFIG_ADDR 780      // 4 + (8 * 40) = 324 bytes max
#define AUTOMATION_CONFIG_ADDR 900  // 4 + (10 * 80) = 804 bytes max

// String lengths
#define MAX_SSID_LENGTH 32
#define MAX_PASS_LENGTH 64
#define MAX_TOKEN_LENGTH 64
#define MAX_CHAT_ID_LENGTH 16

// GPIO Configuration
#define MAX_GPIO_PINS 16
#define MAX_SENSORS 8
#define MAX_AUTOMATIONS 10

// Telegram settings
#define BOT_REQUEST_DELAY 1000
#define BOT_LONG_POLL_TIMEOUT 0

// Sensor update intervals (milliseconds)
#define SENSOR_UPDATE_INTERVAL 30000  // 30 seconds
#define AUTOMATION_CHECK_INTERVAL 5000  // 5 seconds

// Pin definitions for common sensors
#define DHT_DEFAULT_PIN 2
#define LDR_DEFAULT_PIN A0
#define PIR_DEFAULT_PIN 4

// Debug settings
#define DEBUG_SERIAL true
#define SERIAL_BAUD 115200

// Memory optimization
#define TELEGRAM_BUFFER_SIZE 1024
#define JSON_BUFFER_SIZE 512

// WiFi connection settings
#define WIFI_CONNECT_TIMEOUT 20000  // 20 seconds
#define WIFI_RETRY_DELAY 5000       // 5 seconds between retries

#endif // CONFIG_H
