#ifndef CONFIG_H
#define CONFIG_H

// Firmware version (auto-updated by version script)
#define FW_VERSION "1.0.0"
#define FW_NAME "ESPgram"

// WiFi Configuration
#define AP_SSID "ESPgram-Setup"
#define AP_PASSWORD ""  // Open AP for setup
#define AP_IP IPAddress(192, 168, 4, 1)
#define AP_GATEWAY IPAddress(192, 168, 4, 1)
#define AP_SUBNET IPAddress(255, 255, 255, 0)

// Web Server
#define WEB_SERVER_PORT 80
#define CAPTIVE_PORTAL_TIMEOUT 300000  // 5 minutes

// Telegram <PERSON><PERSON>
#define BOT_TOKEN_MAX_LENGTH 50
#define CHAT_ID_MAX_LENGTH 20
#define BOT_CHECK_INTERVAL 1000  // 1 second
#define BOT_LONG_POLL_TIMEOUT 60

// EEPROM/Preferences
#define PREFS_NAMESPACE "espgram"
#define WIFI_SSID_KEY "wifi_ssid"
#define WIFI_PA<PERSON>_KEY "wifi_pass"
#define BOT_TOKEN_KEY "bot_token"
#define CHAT_ID_KEY "chat_id"
#define SETUP_DONE_KEY "setup_done"

// GPIO Configuration
#define MAX_GPIO_PINS 40
#define MAX_SENSORS 10
#define MAX_AUTOMATIONS 20

// Pin Types
enum PinType {
    PIN_DISABLED = 0,
    PIN_DIGITAL_OUTPUT = 1,
    PIN_DIGITAL_INPUT = 2,
    PIN_PWM_OUTPUT = 3,
    PIN_ANALOG_INPUT = 4
};

// Sensor Types
enum SensorType {
    SENSOR_NONE = 0,
    SENSOR_DHT11 = 1,
    SENSOR_DHT22 = 2,
    SENSOR_LDR = 3,
    SENSOR_PIR = 4,
    SENSOR_IR = 5
};

// Automation Trigger Types
enum TriggerType {
    TRIGGER_NONE = 0,
    TRIGGER_PIN_HIGH = 1,
    TRIGGER_PIN_LOW = 2,
    TRIGGER_SENSOR_THRESHOLD = 3,
    TRIGGER_TIME_INTERVAL = 4
};

// Automation Action Types
enum ActionType {
    ACTION_NONE = 0,
    ACTION_PIN_HIGH = 1,
    ACTION_PIN_LOW = 2,
    ACTION_PIN_TOGGLE = 3,
    ACTION_PWM_SET = 4,
    ACTION_SEND_MESSAGE = 5
};

// Memory optimization
#define JSON_BUFFER_SIZE 1024
#define MESSAGE_BUFFER_SIZE 512
#define COMMAND_BUFFER_SIZE 128

// Debug settings
#ifdef CORE_DEBUG_LEVEL
    #define DEBUG_PRINT(x) Serial.print(x)
    #define DEBUG_PRINTLN(x) Serial.println(x)
    #define DEBUG_PRINTF(x, ...) Serial.printf(x, __VA_ARGS__)
#else
    #define DEBUG_PRINT(x)
    #define DEBUG_PRINTLN(x)
    #define DEBUG_PRINTF(x, ...)
#endif

// Pin validation macros
#define IS_VALID_GPIO(pin) ((pin >= 0) && (pin <= 39) && (pin != 6) && (pin != 7) && (pin != 8) && (pin != 9) && (pin != 10) && (pin != 11))
#define IS_OUTPUT_CAPABLE(pin) ((pin <= 33) && IS_VALID_GPIO(pin))
#define IS_PWM_CAPABLE(pin) IS_OUTPUT_CAPABLE(pin)
#define IS_ADC_CAPABLE(pin) (((pin >= 32) && (pin <= 39)) || ((pin >= 25) && (pin <= 27)) || (pin == 0) || (pin == 2) || (pin == 4) || (pin == 12) || (pin == 13) || (pin == 14) || (pin == 15))

#endif // CONFIG_H
