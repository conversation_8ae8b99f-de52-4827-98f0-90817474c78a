[env:esp8266]
platform = espressif8266
board = nodemcuv2
framework = arduino

; Build flags for optimization
build_flags = 
    -DPIO_FRAMEWORK_ARDUINO_LWIP2_LOW_MEMORY
    -DVTABLES_IN_FLASH
    -fno-exceptions
    -lstdc++
    -Os

; Libraries
lib_deps = 
    ESP8266WiFi
    ESP8266WebServer
    ESP8266HTTPClient
    ArduinoJson@^6.21.3
    UniversalTelegramBot@^1.3.0
    DHT sensor library@^1.4.4
    Adafruit Unified Sensor@^1.1.9

; Monitor settings
monitor_speed = 115200
monitor_filters = esp8266_exception_decoder

; Upload settings
upload_speed = 921600

; OTA settings for future use
upload_protocol = esptool
upload_port = COM4

; Build settings for single .bin file
build_type = release
