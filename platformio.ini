[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino

; Build flags
build_flags = 
    -DCORE_DEBUG_LEVEL=3
    -DBOARD_HAS_PSRAM
    -mfix-esp32-psram-cache-issue

; Monitor settings
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; Libraries
lib_deps = 
    bblanchon/Arduino<PERSON><PERSON>@^6.21.3
    witnessmenow/UniversalTelegramBot@^1.3.0
    me-no-dev/ESPAsyncWebServer@^1.2.3
    me-no-dev/AsyncTCP@^1.1.1
    adafruit/DHT sensor library@^1.4.4
    adafruit/Adafruit Unified Sensor@^1.1.9

; Upload settings
upload_speed = 921600

; Filesystem
board_build.filesystem = littlefs

; Extra scripts
extra_scripts = 
    pre:version/update_version.py
    post:scripts/copy_firmware.py

; Build settings
build_type = release
