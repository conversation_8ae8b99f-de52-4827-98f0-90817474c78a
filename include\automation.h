#ifndef AUTOMATION_H
#define AUTOMATION_H

#include <Arduino.h>
#include <EEPROM.h>
#include "config.h"

// Forward declarations
class GPIOManager;
class SensorHandler;
class TelegramBot;

enum ConditionType {
    CONDITION_NONE = 0,
    CONDITION_SENSOR_GREATER = 1,
    CONDITION_SENSOR_LESS = 2,
    CONDITION_SENSOR_EQUAL = 3,
    CONDITION_PIN_HIGH = 4,
    CONDITION_PIN_LOW = 5,
    CONDITION_TIME_INTERVAL = 6
};

enum ActionType {
    ACTION_NONE = 0,
    ACTION_PIN_ON = 1,
    ACTION_PIN_OFF = 2,
    ACTION_PIN_TOGGLE = 3,
    ACTION_PIN_PWM = 4,
    ACTION_SEND_MESSAGE = 5,
    ACTION_SEND_NOTIFICATION = 6
};

struct AutomationRule {
    String name;
    bool enabled;
    
    // Condition
    ConditionType conditionType;
    uint8_t conditionPin;
    float conditionValue;
    unsigned long conditionInterval;
    unsigned long lastTriggered;
    
    // Action
    ActionType actionType;
    uint8_t actionPin;
    int actionValue;
    String actionMessage;
    
    // Settings
    bool repeatAction;
    unsigned long cooldownPeriod;
    
    AutomationRule() : name(""), enabled(false), conditionType(CONDITION_NONE),
                       conditionPin(0), conditionValue(0), conditionInterval(0),
                       lastTriggered(0), actionType(ACTION_NONE), actionPin(0),
                       actionValue(0), actionMessage(""), repeatAction(false),
                       cooldownPeriod(0) {}
};

class AutomationEngine {
private:
    AutomationRule rules[MAX_AUTOMATIONS];
    int ruleCount;
    unsigned long lastCheck;
    
    // References to other modules
    GPIOManager* gpioManager;
    SensorHandler* sensorHandler;
    TelegramBot* telegramBot;
    
    // Private methods
    int findRuleIndex(String name);
    bool evaluateCondition(int ruleIndex);
    void executeAction(int ruleIndex);
    void saveConfiguration();
    void loadConfiguration();
    String conditionTypeToString(ConditionType type);
    String actionTypeToString(ActionType type);
    ConditionType stringToConditionType(String typeStr);
    ActionType stringToActionType(String typeStr);
    bool isInCooldown(int ruleIndex);
    
public:
    AutomationEngine();
    
    // Initialization
    void begin();
    void setModuleReferences(GPIOManager* gpio, SensorHandler* sensor, TelegramBot* telegram);
    
    // Rule management
    bool addRule(String name, ConditionType condType, uint8_t condPin, float condValue,
                 ActionType actType, uint8_t actPin, int actValue, String actMessage = "");
    bool removeRule(String name);
    bool enableRule(String name);
    bool disableRule(String name);
    bool setRuleCooldown(String name, unsigned long cooldown);
    bool setRuleRepeat(String name, bool repeat);
    
    // Rule information
    String listRules();
    String getRuleStatus(String name);
    bool isRuleEnabled(String name);
    int getRuleCount();
    
    // Execution
    void checkRules();
    void loop();
    
    // Configuration management
    void clearConfiguration();
    String getAvailableConditions();
    String getAvailableActions();
    
    // Manual trigger
    bool triggerRule(String name);
};

#endif // AUTOMATION_H
