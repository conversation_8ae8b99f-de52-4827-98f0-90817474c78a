#!/usr/bin/env python3
"""
ESPgram Post-Build Script
=========================
Automatically copies firmware and updates release information after compilation.

Developer: SK Raihan
Organization: SKR Electronics Lab
Email: <EMAIL>
"""

import os
import sys
import json
import shutil
import datetime
from pathlib import Path

# Configuration
VERSION_FILE = "version.json"
RELEASE_DIR = "release"
BUILD_DIR = ".pio/build/esp32dev"
FIRMWARE_SOURCE = f"{BUILD_DIR}/firmware.bin"
VERSION_INFO_FILE = f"{RELEASE_DIR}/version_info.txt"
BUILD_INFO_FILE = f"{RELEASE_DIR}/build_info.txt"

def load_version():
    """Load current version from version.json"""
    if os.path.exists(VERSION_FILE):
        with open(VERSION_FILE, 'r') as f:
            return json.load(f)
    else:
        return {
            "major": 1,
            "minor": 0,
            "patch": 0,
            "build": 0,
            "version_string": "1.0.0"
        }

def get_firmware_size():
    """Get firmware file size"""
    if os.path.exists(FIRMWARE_SOURCE):
        size = os.path.getsize(FIRMWARE_SOURCE)
        return size, f"{size:,} bytes ({size/1024:.1f} KB)"
    return 0, "Unknown"

def create_release_directory():
    """Create release directory if it doesn't exist"""
    os.makedirs(RELEASE_DIR, exist_ok=True)

def copy_firmware(version_data):
    """Copy firmware to release directory with proper naming"""
    if not os.path.exists(FIRMWARE_SOURCE):
        print(f"❌ Firmware not found: {FIRMWARE_SOURCE}")
        return False
    
    # Create firmware filename with version
    version_str = version_data['version_string']
    firmware_name = f"ESPgram_v{version_str}.bin"
    latest_name = "ESPgram_latest.bin"
    
    firmware_dest = f"{RELEASE_DIR}/{firmware_name}"
    latest_dest = f"{RELEASE_DIR}/{latest_name}"
    
    try:
        # Copy versioned firmware
        shutil.copy2(FIRMWARE_SOURCE, firmware_dest)
        print(f"✅ Copied firmware to: {firmware_dest}")
        
        # Copy as latest
        shutil.copy2(FIRMWARE_SOURCE, latest_dest)
        print(f"✅ Copied firmware to: {latest_dest}")
        
        return True, firmware_name
    except Exception as e:
        print(f"❌ Failed to copy firmware: {e}")
        return False, None

def create_version_info(version_data, firmware_name):
    """Create version information file"""
    size_bytes, size_str = get_firmware_size()
    build_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    version_info = f"""ESPgram Firmware Version Information
=====================================

Version: {version_data['version_string']}
Build: {version_data['build']}
Release Date: {build_time}

Firmware Details:
- File: {firmware_name}
- Size: {size_str}
- Platform: ESP32
- Framework: Arduino

Developer Information:
- Name: SK Raihan
- Organization: SKR Electronics Lab
- Email: <EMAIL>
- Website: skrelectronicslab.com
- Instagram: @skr_electronics_lab
- YouTube: SKR Electronics Lab
- Twitter: @skrelectronics

Project Information:
- Name: ESPgram
- Description: Revolutionary IoT Control System
- License: Custom License - SK Raihan
- Repository: Private Development

Features:
- Telegram-based ESP32 control
- Conversational setup wizards
- Smart automation templates
- Professional project templates
- Advanced IoT capabilities
- Real-time monitoring
- EEPROM configuration storage
- WiFi captive portal setup

Installation:
1. Use ESP32 Flash Download Tool
2. Flash at address 0x10000
3. Set flash mode to DIO
4. Set flash frequency to 40MHz
5. Reset ESP32 after flashing

Support:
- Email: <EMAIL>
- Follow @skr_electronics_lab for updates

© 2024 SK Raihan - SKR Electronics Lab
All rights reserved.
"""
    
    with open(VERSION_INFO_FILE, 'w') as f:
        f.write(version_info)
    
    print(f"✅ Version info created: {VERSION_INFO_FILE}")

def create_build_info(version_data):
    """Create build information file"""
    size_bytes, size_str = get_firmware_size()
    build_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    build_info = f"""ESPgram Build Information
========================

Build Details:
- Version: {version_data['version_string']}
- Build Number: {version_data['build']}
- Build Time: {build_time}
- Firmware Size: {size_str}

Build Environment:
- Platform: PlatformIO
- Board: ESP32 Dev Module
- Framework: Arduino
- Compiler: GCC

Memory Usage:
- Flash: Check PlatformIO output
- RAM: Check PlatformIO output

Developer: SK Raihan
Organization: SKR Electronics Lab
Build System: Automated via Python scripts

Last Build: {build_time}
"""
    
    with open(BUILD_INFO_FILE, 'w') as f:
        f.write(build_info)
    
    print(f"✅ Build info created: {BUILD_INFO_FILE}")

def update_release_manifest():
    """Update release manifest with all available versions"""
    manifest_file = f"{RELEASE_DIR}/release_manifest.json"
    
    # Scan release directory for firmware files
    firmware_files = []
    if os.path.exists(RELEASE_DIR):
        for file in os.listdir(RELEASE_DIR):
            if file.endswith('.bin') and file.startswith('ESPgram_v'):
                file_path = f"{RELEASE_DIR}/{file}"
                size = os.path.getsize(file_path)
                mtime = os.path.getmtime(file_path)
                
                firmware_files.append({
                    "filename": file,
                    "size": size,
                    "size_formatted": f"{size:,} bytes ({size/1024:.1f} KB)",
                    "modified": datetime.datetime.fromtimestamp(mtime).isoformat(),
                    "version": file.replace('ESPgram_v', '').replace('.bin', '')
                })
    
    # Sort by modification time (newest first)
    firmware_files.sort(key=lambda x: x['modified'], reverse=True)
    
    manifest = {
        "project": "ESPgram",
        "developer": "SK Raihan",
        "organization": "SKR Electronics Lab",
        "last_updated": datetime.datetime.now().isoformat(),
        "total_releases": len(firmware_files),
        "releases": firmware_files
    }
    
    with open(manifest_file, 'w') as f:
        json.dump(manifest, f, indent=2)
    
    print(f"✅ Release manifest updated: {manifest_file}")

def main():
    """Main post-build process"""
    print("=" * 50)
    print("ESPgram Post-Build Script")
    print("=" * 50)
    print(f"Developer: SK Raihan")
    print(f"Organization: SKR Electronics Lab")
    print(f"Timestamp: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Load version data
    version_data = load_version()
    print(f"Processing version: {version_data['version_string']} (Build {version_data['build']})")
    
    # Create release directory
    create_release_directory()
    print(f"✅ Release directory ready: {RELEASE_DIR}")
    
    # Copy firmware
    success, firmware_name = copy_firmware(version_data)
    if not success:
        print("❌ Post-build process failed!")
        sys.exit(1)
    
    # Get firmware size
    size_bytes, size_str = get_firmware_size()
    print(f"📊 Firmware size: {size_str}")
    
    # Create version and build info
    create_version_info(version_data, firmware_name)
    create_build_info(version_data)
    
    # Update release manifest
    update_release_manifest()
    
    print()
    print("🎉 Post-build process completed successfully!")
    print(f"🚀 Firmware ready: {RELEASE_DIR}/{firmware_name}")
    print("=" * 50)

if __name__ == "__main__":
    main()
