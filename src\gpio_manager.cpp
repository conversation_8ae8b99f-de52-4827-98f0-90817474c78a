#include "gpio_manager.h"
#include <Arduino.h>

GPIOManager::GPIOManager() : pinCount(0) {
    // Initialize pins array
    for (int i = 0; i < MAX_GPIO_PINS; i++) {
        pins[i] = GPIOPin();
    }
}

void GPIOManager::begin() {
    loadConfiguration();
    
    // Initialize configured pins
    for (int i = 0; i < pinCount; i++) {
        if (pins[i].enabled) {
            switch (pins[i].mode) {
                case PIN_DIGITAL_OUTPUT:
                    pinMode(pins[i].pin, OUTPUT);
                    ::digitalWrite(pins[i].pin, pins[i].value);
                    break;
                case PIN_DIGITAL_INPUT:
                    pinMode(pins[i].pin, INPUT_PULLUP);
                    break;
                case PIN_PWM_OUTPUT:
                    pinMode(pins[i].pin, OUTPUT);
                    analogWrite(pins[i].pin, pins[i].value);
                    break;
                case PIN_ANALOG_INPUT:
                    // A0 is the only analog input on ESP8266
                    break;
                default:
                    break;
            }
        }
    }
    
    Serial.println("GPIO Manager initialized with " + String(pinCount) + " pins");
}

bool GPIOManager::isValidPin(uint8_t pin) {
    // Valid GPIO pins for ESP8266 (NodeMCU)
    // Avoid pins used for flash (6-11) and other system functions
    uint8_t validPins[] = {0, 1, 2, 3, 4, 5, 12, 13, 14, 15, 16};
    int validPinCount = sizeof(validPins) / sizeof(validPins[0]);
    
    for (int i = 0; i < validPinCount; i++) {
        if (validPins[i] == pin) {
            return true;
        }
    }
    return false;
}

bool GPIOManager::isPinConfigured(uint8_t pin) {
    return findPinIndex(pin) != -1;
}

int GPIOManager::findPinIndex(uint8_t pin) {
    for (int i = 0; i < pinCount; i++) {
        if (pins[i].pin == pin) {
            return i;
        }
    }
    return -1;
}

bool GPIOManager::configurePin(uint8_t pin, PinMode mode, String name) {
    if (!isValidPin(pin)) {
        Serial.println("Invalid pin: " + String(pin));
        return false;
    }
    
    // Check if pin is already configured
    int index = findPinIndex(pin);
    if (index == -1) {
        // Add new pin
        if (pinCount >= MAX_GPIO_PINS) {
            Serial.println("Maximum pin count reached");
            return false;
        }
        index = pinCount++;
    }
    
    // Configure pin
    pins[index].pin = pin;
    pins[index].mode = mode;
    pins[index].name = name.length() > 0 ? name : "GPIO" + String(pin);
    pins[index].value = 0;
    pins[index].enabled = true;
    
    // Set hardware pin mode
    switch (mode) {
        case PIN_DIGITAL_OUTPUT:
            pinMode(pin, OUTPUT);
            ::digitalWrite(pin, LOW);
            break;
        case PIN_DIGITAL_INPUT:
            pinMode(pin, INPUT_PULLUP);
            break;
        case PIN_PWM_OUTPUT:
            pinMode(pin, OUTPUT);
            analogWrite(pin, 0);
            break;
        case PIN_ANALOG_INPUT:
            if (pin != A0) {
                Serial.println("Analog input only available on A0");
                return false;
            }
            break;
        default:
            pins[index].enabled = false;
            return false;
    }
    
    saveConfiguration();
    Serial.println("Pin " + String(pin) + " configured as " + pinModeToString(mode));
    return true;
}

bool GPIOManager::removePin(uint8_t pin) {
    int index = findPinIndex(pin);
    if (index == -1) {
        return false;
    }
    
    // Shift remaining pins
    for (int i = index; i < pinCount - 1; i++) {
        pins[i] = pins[i + 1];
    }
    pinCount--;
    
    // Reset hardware pin
    pinMode(pin, INPUT);
    
    saveConfiguration();
    Serial.println("Pin " + String(pin) + " removed");
    return true;
}

bool GPIOManager::enablePin(uint8_t pin) {
    int index = findPinIndex(pin);
    if (index == -1) {
        return false;
    }
    
    pins[index].enabled = true;
    saveConfiguration();
    return true;
}

bool GPIOManager::disablePin(uint8_t pin) {
    int index = findPinIndex(pin);
    if (index == -1) {
        return false;
    }
    
    pins[index].enabled = false;
    pinMode(pin, INPUT); // Set to safe state
    saveConfiguration();
    return true;
}

bool GPIOManager::digitalWrite(uint8_t pin, bool state) {
    int index = findPinIndex(pin);
    if (index == -1 || !pins[index].enabled) {
        return false;
    }
    
    if (pins[index].mode != PIN_DIGITAL_OUTPUT) {
        return false;
    }
    
    ::digitalWrite(pin, state ? HIGH : LOW);
    pins[index].value = state ? 1 : 0;
    saveConfiguration();
    return true;
}

bool GPIOManager::digitalRead(uint8_t pin) {
    int index = findPinIndex(pin);
    if (index == -1 || !pins[index].enabled) {
        return false;
    }
    
    if (pins[index].mode != PIN_DIGITAL_INPUT) {
        return false;
    }
    
    bool state = ::digitalRead(pin) == HIGH;
    pins[index].value = state ? 1 : 0;
    return state;
}

bool GPIOManager::analogWrite(uint8_t pin, int value) {
    int index = findPinIndex(pin);
    if (index == -1 || !pins[index].enabled) {
        return false;
    }
    
    if (pins[index].mode != PIN_PWM_OUTPUT) {
        return false;
    }
    
    // Constrain value to valid PWM range
    value = constrain(value, 0, 1023);
    ::analogWrite(pin, value);
    pins[index].value = value;
    saveConfiguration();
    return true;
}

int GPIOManager::analogRead(uint8_t pin) {
    int index = findPinIndex(pin);
    if (index == -1 || !pins[index].enabled) {
        return -1;
    }
    
    if (pins[index].mode != PIN_ANALOG_INPUT || pin != A0) {
        return -1;
    }
    
    int value = ::analogRead(pin);
    pins[index].value = value;
    return value;
}

bool GPIOManager::togglePin(uint8_t pin) {
    int index = findPinIndex(pin);
    if (index == -1 || !pins[index].enabled) {
        return false;
    }
    
    if (pins[index].mode != PIN_DIGITAL_OUTPUT) {
        return false;
    }
    
    bool newState = pins[index].value == 0;
    return digitalWrite(pin, newState);
}

String GPIOManager::listPins() {
    if (pinCount == 0) {
        return "📝 No GPIO pins configured.";
    }
    
    String list = "🔌 *Configured GPIO Pins:*\n\n";
    
    for (int i = 0; i < pinCount; i++) {
        String status = pins[i].enabled ? "✅" : "❌";
        list += status + " **Pin " + String(pins[i].pin) + "** (" + pins[i].name + ")\n";
        list += "   📋 Mode: " + pinModeToString(pins[i].mode) + "\n";
        list += "   📊 Value: " + String(pins[i].value) + "\n\n";
    }
    
    return list;
}

String GPIOManager::getPinStatus(uint8_t pin) {
    int index = findPinIndex(pin);
    if (index == -1) {
        return "Pin not configured";
    }
    
    String status = "🔌 **Pin " + String(pin) + "** (" + pins[index].name + ")\n";
    status += "📋 Mode: " + pinModeToString(pins[index].mode) + "\n";
    status += "📊 Value: " + String(pins[index].value) + "\n";
    status += "⚡ Status: " + String(pins[index].enabled ? "Enabled" : "Disabled");
    
    return status;
}

String GPIOManager::getPinName(uint8_t pin) {
    int index = findPinIndex(pin);
    return index != -1 ? pins[index].name : "";
}

PinMode GPIOManager::getPinMode(uint8_t pin) {
    int index = findPinIndex(pin);
    return index != -1 ? pins[index].mode : PIN_DISABLED;
}

int GPIOManager::getPinValue(uint8_t pin) {
    int index = findPinIndex(pin);
    return index != -1 ? pins[index].value : -1;
}

bool GPIOManager::isPinEnabled(uint8_t pin) {
    int index = findPinIndex(pin);
    return index != -1 ? pins[index].enabled : false;
}

void GPIOManager::updateAllPins() {
    for (int i = 0; i < pinCount; i++) {
        if (!pins[i].enabled) continue;
        
        if (pins[i].mode == PIN_DIGITAL_INPUT) {
            digitalRead(pins[i].pin);
        } else if (pins[i].mode == PIN_ANALOG_INPUT) {
            analogRead(pins[i].pin);
        }
    }
}

String GPIOManager::readAllInputs() {
    String readings = "📖 *Input Readings:*\n\n";
    bool hasInputs = false;
    
    for (int i = 0; i < pinCount; i++) {
        if (!pins[i].enabled) continue;
        
        if (pins[i].mode == PIN_DIGITAL_INPUT || pins[i].mode == PIN_ANALOG_INPUT) {
            hasInputs = true;
            readings += "📍 " + pins[i].name + " (Pin " + String(pins[i].pin) + "): ";
            
            if (pins[i].mode == PIN_DIGITAL_INPUT) {
                bool state = digitalRead(pins[i].pin);
                readings += state ? "HIGH" : "LOW";
            } else {
                int value = analogRead(pins[i].pin);
                readings += String(value);
            }
            readings += "\n";
        }
    }
    
    return hasInputs ? readings : "📝 No input pins configured.";
}

void GPIOManager::resetAllPins() {
    for (int i = 0; i < pinCount; i++) {
        pinMode(pins[i].pin, INPUT);
    }
    pinCount = 0;
    clearConfiguration();
    Serial.println("All GPIO pins reset");
}

String GPIOManager::pinModeToString(PinMode mode) {
    switch (mode) {
        case PIN_DIGITAL_OUTPUT: return "Digital Output";
        case PIN_DIGITAL_INPUT: return "Digital Input";
        case PIN_PWM_OUTPUT: return "PWM Output";
        case PIN_ANALOG_INPUT: return "Analog Input";
        default: return "Disabled";
    }
}

PinMode GPIOManager::stringToPinMode(String modeStr) {
    modeStr.toLowerCase();
    if (modeStr == "output" || modeStr == "digital_output") return PIN_DIGITAL_OUTPUT;
    if (modeStr == "input" || modeStr == "digital_input") return PIN_DIGITAL_INPUT;
    if (modeStr == "pwm" || modeStr == "pwm_output") return PIN_PWM_OUTPUT;
    if (modeStr == "analog" || modeStr == "analog_input") return PIN_ANALOG_INPUT;
    return PIN_DISABLED;
}

void GPIOManager::saveConfiguration() {
    // Save pin count
    EEPROM.put(GPIO_CONFIG_ADDR, pinCount);

    // Save pin configurations (simplified structure)
    for (int i = 0; i < pinCount && i < MAX_GPIO_PINS; i++) {
        int addr = GPIO_CONFIG_ADDR + 4 + (i * 32); // Fixed size per pin
        EEPROM.put(addr, pins[i].pin);
        EEPROM.put(addr + 1, (uint8_t)pins[i].mode);
        EEPROM.put(addr + 2, pins[i].enabled);
        EEPROM.put(addr + 4, pins[i].value);
        // Store name as fixed 20 char array
        char nameBuffer[20] = {0};
        pins[i].name.toCharArray(nameBuffer, 20);
        for (int j = 0; j < 20; j++) {
            EEPROM.put(addr + 8 + j, nameBuffer[j]);
        }
    }

    EEPROM.commit();
}

void GPIOManager::loadConfiguration() {
    // Load pin count
    EEPROM.get(GPIO_CONFIG_ADDR, pinCount);

    // Validate pin count
    if (pinCount < 0 || pinCount > MAX_GPIO_PINS) {
        pinCount = 0;
        return;
    }

    // Load pin configurations (simplified structure)
    for (int i = 0; i < pinCount; i++) {
        int addr = GPIO_CONFIG_ADDR + 4 + (i * 32); // Fixed size per pin
        EEPROM.get(addr, pins[i].pin);
        uint8_t mode;
        EEPROM.get(addr + 1, mode);
        pins[i].mode = (PinMode)mode;
        EEPROM.get(addr + 2, pins[i].enabled);
        EEPROM.get(addr + 4, pins[i].value);
        // Load name from fixed 20 char array
        char nameBuffer[20] = {0};
        for (int j = 0; j < 20; j++) {
            EEPROM.get(addr + 8 + j, nameBuffer[j]);
        }
        pins[i].name = String(nameBuffer);
    }
}

void GPIOManager::clearConfiguration() {
    pinCount = 0;
    saveConfiguration();
}

int GPIOManager::getConfiguredPinCount() {
    return pinCount;
}

String GPIOManager::getAvailablePins() {
    String available = "📌 *Available GPIO Pins:*\n\n";
    uint8_t validPins[] = {0, 1, 2, 3, 4, 5, 12, 13, 14, 15, 16};
    int validPinCount = sizeof(validPins) / sizeof(validPins[0]);
    
    for (int i = 0; i < validPinCount; i++) {
        uint8_t pin = validPins[i];
        String status = isPinConfigured(pin) ? "🔴 Used" : "🟢 Free";
        available += "📍 GPIO" + String(pin) + " - " + status + "\n";
    }
    
    available += "\n💡 *Note:* A0 is for analog input only";
    return available;
}
