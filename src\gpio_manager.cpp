#include "gpio_manager.h"

GPIOManager::GPIOManager() : configuredPinCount(0), initialized(false), nextPWMChannel(0) {
    // Initialize pin array
    for (int i = 0; i < MAX_GPIO_PINS; i++) {
        pins[i].pin = 255; // Invalid pin number
        pins[i].type = PIN_DISABLED;
        pins[i].name = "";
        pins[i].value = 0;
        pins[i].pwmChannel = -1;
        pins[i].enabled = false;
        pins[i].lastUpdate = 0;
    }
    
    // Initialize PWM channel tracking
    for (int i = 0; i < 16; i++) {
        pwmChannels[i] = false;
    }
}

GPIOManager::~GPIOManager() {
    // Clean up PWM channels
    for (int i = 0; i < configuredPinCount; i++) {
        if (pins[i].type == PIN_PWM_OUTPUT && pins[i].pwmChannel >= 0) {
            ledcDetachPin(pins[i].pin);
        }
    }
}

bool GPIOManager::begin() {
    DEBUG_PRINTLN("GPIOManager: Initializing...");
    
    preferences.begin(PREFS_NAMESPACE, false);
    loadConfiguration();
    
    // Initialize all configured pins
    for (int i = 0; i < configuredPinCount; i++) {
        if (pins[i].pin != 255) {
            initializePin(i);
        }
    }
    
    initialized = true;
    DEBUG_PRINTF("GPIOManager: Initialized with %d configured pins\n", configuredPinCount);
    return true;
}

void GPIOManager::loop() {
    // Periodic tasks if needed
    // For now, just update input pin values
    for (int i = 0; i < configuredPinCount; i++) {
        if (pins[i].enabled && (pins[i].type == PIN_DIGITAL_INPUT || pins[i].type == PIN_ANALOG_INPUT)) {
            int newValue;
            if (pins[i].type == PIN_DIGITAL_INPUT) {
                digitalRead(pins[i].pin, newValue);
            } else {
                analogRead(pins[i].pin, newValue);
            }
            
            if (newValue != pins[i].value) {
                pins[i].value = newValue;
                pins[i].lastUpdate = millis();
            }
        }
    }
}

bool GPIOManager::configurePin(uint8_t pin, PinType type, const String& name) {
    if (!isValidPin(pin)) {
        DEBUG_PRINTF("GPIOManager: Invalid pin number: %d\n", pin);
        return false;
    }
    
    // Check if pin is already configured
    int index = findPinIndex(pin);
    if (index == -1) {
        // Find free slot
        for (int i = 0; i < MAX_GPIO_PINS; i++) {
            if (pins[i].pin == 255) {
                index = i;
                configuredPinCount++;
                break;
            }
        }
        
        if (index == -1) {
            DEBUG_PRINTLN("GPIOManager: No free slots for new pin");
            return false;
        }
    }
    
    // Configure pin
    pins[index].pin = pin;
    pins[index].type = type;
    pins[index].name = name.length() > 0 ? name : "GPIO" + String(pin);
    pins[index].value = 0;
    pins[index].enabled = true;
    pins[index].lastUpdate = millis();
    
    // Handle PWM channel allocation
    if (type == PIN_PWM_OUTPUT) {
        if (pins[index].pwmChannel == -1) {
            pins[index].pwmChannel = getFreePWMChannel();
            if (pins[index].pwmChannel == -1) {
                DEBUG_PRINTLN("GPIOManager: No free PWM channels");
                return false;
            }
        }
    } else if (pins[index].pwmChannel >= 0) {
        // Release PWM channel if changing from PWM
        releasePWMChannel(pins[index].pwmChannel);
        pins[index].pwmChannel = -1;
    }
    
    initializePin(index);
    saveConfiguration();
    
    DEBUG_PRINTF("GPIOManager: Configured pin %d as %s\n", pin, name.c_str());
    return true;
}

bool GPIOManager::removePin(uint8_t pin) {
    int index = findPinIndex(pin);
    if (index == -1) {
        return false;
    }
    
    // Release PWM channel if used
    if (pins[index].pwmChannel >= 0) {
        ledcDetachPin(pin);
        releasePWMChannel(pins[index].pwmChannel);
    }
    
    // Reset pin to input (safe state)
    pinMode(pin, INPUT);
    
    // Clear configuration
    pins[index].pin = 255;
    pins[index].type = PIN_DISABLED;
    pins[index].name = "";
    pins[index].value = 0;
    pins[index].pwmChannel = -1;
    pins[index].enabled = false;
    pins[index].lastUpdate = 0;
    
    configuredPinCount--;
    saveConfiguration();
    
    DEBUG_PRINTF("GPIOManager: Removed pin %d\n", pin);
    return true;
}

bool GPIOManager::enablePin(uint8_t pin, bool enable) {
    int index = findPinIndex(pin);
    if (index == -1) {
        return false;
    }
    
    pins[index].enabled = enable;
    saveConfiguration();
    
    DEBUG_PRINTF("GPIOManager: Pin %d %s\n", pin, enable ? "enabled" : "disabled");
    return true;
}

bool GPIOManager::digitalRead(uint8_t pin, int& value) {
    int index = findPinIndex(pin);
    if (index == -1 || !pins[index].enabled) {
        return false;
    }
    
    if (pins[index].type != PIN_DIGITAL_INPUT) {
        return false;
    }
    
    value = ::digitalRead(pin);
    pins[index].value = value;
    pins[index].lastUpdate = millis();
    
    return true;
}

bool GPIOManager::digitalWrite(uint8_t pin, int value) {
    int index = findPinIndex(pin);
    if (index == -1 || !pins[index].enabled) {
        return false;
    }
    
    if (pins[index].type != PIN_DIGITAL_OUTPUT) {
        return false;
    }
    
    ::digitalWrite(pin, value ? HIGH : LOW);
    pins[index].value = value ? 1 : 0;
    pins[index].lastUpdate = millis();
    
    DEBUG_PRINTF("GPIOManager: Pin %d set to %d\n", pin, pins[index].value);
    return true;
}

bool GPIOManager::analogRead(uint8_t pin, int& value) {
    int index = findPinIndex(pin);
    if (index == -1 || !pins[index].enabled) {
        return false;
    }
    
    if (pins[index].type != PIN_ANALOG_INPUT) {
        return false;
    }
    
    value = ::analogRead(pin);
    pins[index].value = value;
    pins[index].lastUpdate = millis();
    
    return true;
}

bool GPIOManager::pwmWrite(uint8_t pin, int value) {
    int index = findPinIndex(pin);
    if (index == -1 || !pins[index].enabled) {
        return false;
    }
    
    if (pins[index].type != PIN_PWM_OUTPUT || pins[index].pwmChannel == -1) {
        return false;
    }
    
    // Constrain value to 0-255
    value = constrain(value, 0, 255);
    
    ledcWrite(pins[index].pwmChannel, value);
    pins[index].value = value;
    pins[index].lastUpdate = millis();
    
    DEBUG_PRINTF("GPIOManager: Pin %d PWM set to %d\n", pin, value);
    return true;
}

bool GPIOManager::togglePin(uint8_t pin) {
    int index = findPinIndex(pin);
    if (index == -1 || !pins[index].enabled) {
        return false;
    }
    
    if (pins[index].type != PIN_DIGITAL_OUTPUT) {
        return false;
    }
    
    int newValue = pins[index].value ? 0 : 1;
    return digitalWrite(pin, newValue);
}

// Private helper methods
bool GPIOManager::isValidPin(uint8_t pin) {
    return IS_VALID_GPIO(pin);
}

bool GPIOManager::isPinConfigured(uint8_t pin) {
    return findPinIndex(pin) != -1;
}

int GPIOManager::findPinIndex(uint8_t pin) {
    for (int i = 0; i < MAX_GPIO_PINS; i++) {
        if (pins[i].pin == pin) {
            return i;
        }
    }
    return -1;
}

int GPIOManager::getFreePWMChannel() {
    for (int i = 0; i < 16; i++) {
        if (!pwmChannels[i]) {
            pwmChannels[i] = true;
            return i;
        }
    }
    return -1;
}

void GPIOManager::releasePWMChannel(int channel) {
    if (channel >= 0 && channel < 16) {
        pwmChannels[channel] = false;
    }
}

void GPIOManager::initializePin(int index) {
    if (index < 0 || index >= MAX_GPIO_PINS || pins[index].pin == 255) {
        return;
    }

    uint8_t pin = pins[index].pin;

    switch (pins[index].type) {
        case PIN_DIGITAL_OUTPUT:
            pinMode(pin, OUTPUT);
            digitalWrite(pin, LOW);
            pins[index].value = 0;
            break;

        case PIN_DIGITAL_INPUT:
            pinMode(pin, INPUT_PULLUP);
            pins[index].value = ::digitalRead(pin);
            break;

        case PIN_PWM_OUTPUT:
            if (pins[index].pwmChannel >= 0) {
                ledcSetup(pins[index].pwmChannel, 5000, 8); // 5kHz, 8-bit resolution
                ledcAttachPin(pin, pins[index].pwmChannel);
                ledcWrite(pins[index].pwmChannel, 0);
                pins[index].value = 0;
            }
            break;

        case PIN_ANALOG_INPUT:
            pinMode(pin, INPUT);
            pins[index].value = ::analogRead(pin);
            break;

        default:
            break;
    }

    pins[index].lastUpdate = millis();
}

void GPIOManager::saveConfiguration() {
    if (!initialized) return;

    // Save pin configurations
    for (int i = 0; i < configuredPinCount; i++) {
        if (pins[i].pin != 255) {
            String key = "pin_" + String(i);
            String config = String(pins[i].pin) + "," +
                           String(pins[i].type) + "," +
                           pins[i].name + "," +
                           String(pins[i].enabled ? 1 : 0);
            preferences.putString(key.c_str(), config);
        }
    }

    preferences.putInt("pin_count", configuredPinCount);
}

void GPIOManager::loadConfiguration() {
    configuredPinCount = preferences.getInt("pin_count", 0);

    for (int i = 0; i < configuredPinCount && i < MAX_GPIO_PINS; i++) {
        String key = "pin_" + String(i);
        String config = preferences.getString(key.c_str(), "");

        if (config.length() > 0) {
            // Parse configuration string
            int commaIndex1 = config.indexOf(',');
            int commaIndex2 = config.indexOf(',', commaIndex1 + 1);
            int commaIndex3 = config.indexOf(',', commaIndex2 + 1);

            if (commaIndex1 > 0 && commaIndex2 > 0 && commaIndex3 > 0) {
                pins[i].pin = config.substring(0, commaIndex1).toInt();
                pins[i].type = (PinType)config.substring(commaIndex1 + 1, commaIndex2).toInt();
                pins[i].name = config.substring(commaIndex2 + 1, commaIndex3);
                pins[i].enabled = config.substring(commaIndex3 + 1).toInt() == 1;
                pins[i].value = 0;
                pins[i].pwmChannel = -1;
                pins[i].lastUpdate = 0;

                // Allocate PWM channel if needed
                if (pins[i].type == PIN_PWM_OUTPUT) {
                    pins[i].pwmChannel = getFreePWMChannel();
                }
            }
        }
    }
}

String GPIOManager::getStatusReport() {
    String report = "";

    if (configuredPinCount == 0) {
        report = "No GPIO pins configured.\n\n";
        report += "Use `/gpio set <pin> <mode>` to configure pins.\n";
        report += "Available modes: output, input, pwm, analog";
        return report;
    }

    for (int i = 0; i < configuredPinCount; i++) {
        if (pins[i].pin != 255) {
            report += "*Pin " + String(pins[i].pin) + "* (" + pins[i].name + ")\n";

            String typeStr = "";
            switch (pins[i].type) {
                case PIN_DIGITAL_OUTPUT: typeStr = "Digital Output"; break;
                case PIN_DIGITAL_INPUT: typeStr = "Digital Input"; break;
                case PIN_PWM_OUTPUT: typeStr = "PWM Output"; break;
                case PIN_ANALOG_INPUT: typeStr = "Analog Input"; break;
                default: typeStr = "Unknown"; break;
            }

            report += "• Type: " + typeStr + "\n";
            report += "• Value: " + String(pins[i].value);

            if (pins[i].type == PIN_PWM_OUTPUT) {
                report += " (" + String((pins[i].value * 100) / 255) + "%)";
            } else if (pins[i].type == PIN_ANALOG_INPUT) {
                report += " (" + String((pins[i].value * 3.3) / 4095, 2) + "V)";
            }

            report += "\n• Status: " + String(pins[i].enabled ? "Enabled" : "Disabled") + "\n\n";
        }
    }

    return report;
}

String GPIOManager::executeCommand(const String& command) {
    String cmd = command;
    cmd.toLowerCase();
    cmd.trim();

    if (cmd.startsWith("set ")) {
        return parseSetCommand(cmd.substring(4));
    }
    else if (cmd.startsWith("read ")) {
        return parseReadCommand(cmd.substring(5));
    }
    else if (cmd.startsWith("write ")) {
        return parseWriteCommand(cmd.substring(6));
    }
    else if (cmd.startsWith("pwm ")) {
        return parsePWMCommand(cmd.substring(4));
    }
    else if (cmd.startsWith("toggle ")) {
        return parseToggleCommand(cmd.substring(7));
    }
    else if (cmd.startsWith("remove ")) {
        return parseRemoveCommand(cmd.substring(7));
    }
    else if (cmd == "list" || cmd == "readall") {
        return parseListCommand();
    }
    else {
        return "❓ Unknown GPIO command. Available: set, read, write, pwm, toggle, remove, list";
    }
}

String GPIOManager::parseSetCommand(const String& args) {
    // Format: set <pin> <mode> [name]
    int spaceIndex1 = args.indexOf(' ');
    if (spaceIndex1 == -1) {
        return "❌ Usage: set <pin> <mode> [name]\nModes: output, input, pwm, analog";
    }

    int pin = args.substring(0, spaceIndex1).toInt();
    String modeStr = args.substring(spaceIndex1 + 1);

    int spaceIndex2 = modeStr.indexOf(' ');
    String name = "";
    if (spaceIndex2 != -1) {
        name = modeStr.substring(spaceIndex2 + 1);
        modeStr = modeStr.substring(0, spaceIndex2);
    }

    modeStr.toLowerCase();
    PinType type = PIN_DISABLED;

    if (modeStr == "output" || modeStr == "out") {
        type = PIN_DIGITAL_OUTPUT;
    } else if (modeStr == "input" || modeStr == "in") {
        type = PIN_DIGITAL_INPUT;
    } else if (modeStr == "pwm") {
        type = PIN_PWM_OUTPUT;
    } else if (modeStr == "analog" || modeStr == "adc") {
        type = PIN_ANALOG_INPUT;
    } else {
        return "❌ Invalid mode. Use: output, input, pwm, analog";
    }

    if (configurePin(pin, type, name)) {
        return "✅ Pin " + String(pin) + " configured as " + modeStr +
               (name.length() > 0 ? " (" + name + ")" : "");
    } else {
        return "❌ Failed to configure pin " + String(pin);
    }
}

String GPIOManager::parseReadCommand(const String& args) {
    int pin = args.toInt();
    int index = findPinIndex(pin);

    if (index == -1) {
        return "❌ Pin " + String(pin) + " not configured";
    }

    if (!pins[index].enabled) {
        return "❌ Pin " + String(pin) + " is disabled";
    }

    int value;
    bool success = false;

    if (pins[index].type == PIN_DIGITAL_INPUT) {
        success = digitalRead(pin, value);
    } else if (pins[index].type == PIN_ANALOG_INPUT) {
        success = analogRead(pin, value);
    } else {
        return "❌ Pin " + String(pin) + " is not an input pin";
    }

    if (success) {
        String result = "📖 Pin " + String(pin) + " (" + pins[index].name + "): " + String(value);
        if (pins[index].type == PIN_ANALOG_INPUT) {
            result += " (" + String((value * 3.3) / 4095, 2) + "V)";
        }
        return result;
    } else {
        return "❌ Failed to read pin " + String(pin);
    }
}

String GPIOManager::parseWriteCommand(const String& args) {
    int spaceIndex = args.indexOf(' ');
    if (spaceIndex == -1) {
        return "❌ Usage: write <pin> <value>";
    }

    int pin = args.substring(0, spaceIndex).toInt();
    int value = args.substring(spaceIndex + 1).toInt();

    int index = findPinIndex(pin);
    if (index == -1) {
        return "❌ Pin " + String(pin) + " not configured";
    }

    if (!pins[index].enabled) {
        return "❌ Pin " + String(pin) + " is disabled";
    }

    if (pins[index].type == PIN_DIGITAL_OUTPUT) {
        if (digitalWrite(pin, value)) {
            return "✅ Pin " + String(pin) + " set to " + String(value ? "HIGH" : "LOW");
        }
    } else {
        return "❌ Pin " + String(pin) + " is not a digital output";
    }

    return "❌ Failed to write to pin " + String(pin);
}

String GPIOManager::parsePWMCommand(const String& args) {
    int spaceIndex = args.indexOf(' ');
    if (spaceIndex == -1) {
        return "❌ Usage: pwm <pin> <value> (0-255)";
    }

    int pin = args.substring(0, spaceIndex).toInt();
    int value = args.substring(spaceIndex + 1).toInt();

    int index = findPinIndex(pin);
    if (index == -1) {
        return "❌ Pin " + String(pin) + " not configured";
    }

    if (!pins[index].enabled) {
        return "❌ Pin " + String(pin) + " is disabled";
    }

    if (pins[index].type == PIN_PWM_OUTPUT) {
        if (pwmWrite(pin, value)) {
            return "✅ Pin " + String(pin) + " PWM set to " + String(value) +
                   " (" + String((value * 100) / 255) + "%)";
        }
    } else {
        return "❌ Pin " + String(pin) + " is not a PWM output";
    }

    return "❌ Failed to set PWM on pin " + String(pin);
}

String GPIOManager::parseToggleCommand(const String& args) {
    int pin = args.toInt();

    if (togglePin(pin)) {
        int index = findPinIndex(pin);
        return "🔄 Pin " + String(pin) + " toggled to " +
               String(pins[index].value ? "HIGH" : "LOW");
    } else {
        return "❌ Failed to toggle pin " + String(pin);
    }
}

String GPIOManager::parseRemoveCommand(const String& args) {
    int pin = args.toInt();

    if (removePin(pin)) {
        return "🗑️ Pin " + String(pin) + " removed from configuration";
    } else {
        return "❌ Pin " + String(pin) + " not found or failed to remove";
    }
}

String GPIOManager::parseListCommand() {
    return getStatusReport();
}

// Additional utility methods
String GPIOManager::getPinInfo(uint8_t pin) {
    int index = findPinIndex(pin);
    if (index == -1) {
        return "Pin " + String(pin) + " not configured";
    }

    String info = "Pin " + String(pin) + " (" + pins[index].name + ")\n";

    String typeStr = "";
    switch (pins[index].type) {
        case PIN_DIGITAL_OUTPUT: typeStr = "Digital Output"; break;
        case PIN_DIGITAL_INPUT: typeStr = "Digital Input"; break;
        case PIN_PWM_OUTPUT: typeStr = "PWM Output"; break;
        case PIN_ANALOG_INPUT: typeStr = "Analog Input"; break;
        default: typeStr = "Unknown"; break;
    }

    info += "Type: " + typeStr + "\n";
    info += "Value: " + String(pins[index].value) + "\n";
    info += "Status: " + String(pins[index].enabled ? "Enabled" : "Disabled") + "\n";

    if (pins[index].lastUpdate > 0) {
        info += "Last Update: " + String((millis() - pins[index].lastUpdate) / 1000) + "s ago";
    }

    return info;
}

bool GPIOManager::isPinAvailable(uint8_t pin) {
    return isValidPin(pin) && !isPinConfigured(pin);
}

String GPIOManager::readAllPins() {
    String result = "📊 *All Pin Values*\n\n";

    bool hasInputs = false;
    for (int i = 0; i < configuredPinCount; i++) {
        if (pins[i].pin != 255 && pins[i].enabled) {
            if (pins[i].type == PIN_DIGITAL_INPUT || pins[i].type == PIN_ANALOG_INPUT) {
                hasInputs = true;

                int value;
                bool success = false;

                if (pins[i].type == PIN_DIGITAL_INPUT) {
                    success = digitalRead(pins[i].pin, value);
                } else {
                    success = analogRead(pins[i].pin, value);
                }

                if (success) {
                    result += "Pin " + String(pins[i].pin) + " (" + pins[i].name + "): " + String(value);
                    if (pins[i].type == PIN_ANALOG_INPUT) {
                        result += " (" + String((value * 3.3) / 4095, 2) + "V)";
                    }
                    result += "\n";
                }
            }
        }
    }

    if (!hasInputs) {
        result += "No input pins configured.";
    }

    return result;
}

String GPIOManager::listConfiguredPins() {
    return getStatusReport();
}

void GPIOManager::resetAllPins() {
    for (int i = 0; i < configuredPinCount; i++) {
        if (pins[i].pin != 255) {
            removePin(pins[i].pin);
        }
    }
    configuredPinCount = 0;
    saveConfiguration();
}

bool GPIOManager::setPinName(uint8_t pin, const String& name) {
    int index = findPinIndex(pin);
    if (index == -1) {
        return false;
    }

    pins[index].name = name;
    saveConfiguration();
    return true;
}

String GPIOManager::getPinName(uint8_t pin) {
    int index = findPinIndex(pin);
    if (index == -1) {
        return "";
    }

    return pins[index].name;
}

bool GPIOManager::setPinMode(uint8_t pin, PinType type) {
    int index = findPinIndex(pin);
    if (index == -1) {
        return false;
    }

    // Release PWM channel if changing from PWM
    if (pins[index].type == PIN_PWM_OUTPUT && pins[index].pwmChannel >= 0) {
        ledcDetachPin(pin);
        releasePWMChannel(pins[index].pwmChannel);
        pins[index].pwmChannel = -1;
    }

    pins[index].type = type;

    // Allocate PWM channel if changing to PWM
    if (type == PIN_PWM_OUTPUT) {
        pins[index].pwmChannel = getFreePWMChannel();
        if (pins[index].pwmChannel == -1) {
            return false;
        }
    }

    initializePin(index);
    saveConfiguration();
    return true;
}

PinType GPIOManager::getPinType(uint8_t pin) {
    int index = findPinIndex(pin);
    if (index == -1) {
        return PIN_DISABLED;
    }

    return pins[index].type;
}

int GPIOManager::getPinValue(uint8_t pin) {
    int index = findPinIndex(pin);
    if (index == -1) {
        return -1;
    }

    return pins[index].value;
}

unsigned long GPIOManager::getPinLastUpdate(uint8_t pin) {
    int index = findPinIndex(pin);
    if (index == -1) {
        return 0;
    }

    return pins[index].lastUpdate;
}
