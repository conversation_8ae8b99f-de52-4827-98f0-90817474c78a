#ifndef TELEGRAM_BOT_H
#define TELEGRAM_BOT_H

#include <WiFiClientSecure.h>
#include <UniversalTelegramBot.h>
#include <ArduinoJson.h>
#include "config.h"

// Forward declarations
class GPIOManager;
class SensorHandler;
class AutomationEngine;

struct BotCommand {
    String command;
    String description;
    String category;
};

class TelegramBot {
private:
    WiFiClientSecure client;
    UniversalTelegramBot* bot;
    String botToken;
    String chatID;
    unsigned long lastBotRan;
    bool initialized;
    
    // References to other modules
    GPIOManager* gpioManager;
    SensorHandler* sensorHandler;
    AutomationEngine* automationEngine;
    
    // Command handling
    void handleNewMessages(int numNewMessages);
    void handleMessage(String chatId, String text, String fromName);
    void handleCallbackQuery(String queryId, String chatId, String data);
    
    // Command processors
    void processStartCommand(String chatId);
    void processHelpCommand(String chatId);
    void processStatusCommand(String chatId);
    void processGPIOCommand(String chatId, String args);
    void processSensorCommand(String chatId, String args);
    void processAutomationCommand(String chatId, String args);
    void processSystemCommand(String chatId, String args);
    
    // Removed keyboard generators - using simple commands instead
    
    // Utility methods
    bool isAuthorizedUser(String chatId);
    String formatUptime();
    String formatMemoryInfo();
    String escapeMarkdown(String text);
    void sendTypingAction(String chatId);
    void setupBotCommands();

    // Quick action methods
    String processQuickCommand(String chatId, String command, String args);
    String processAddCommand(String chatId, String args);
    String processOnOffCommand(String chatId, String args, bool turnOn);
    String processReadCommand(String chatId, String args);
    String processTempCommand(String chatId);

public:
    TelegramBot();
    ~TelegramBot();
    
    // Initialization
    bool begin(const String& token, const String& chat_id);
    void setModuleReferences(GPIOManager* gpio, SensorHandler* sensor, AutomationEngine* automation);
    
    // Main loop
    void loop();
    
    // Message sending
    bool sendMessage(const String& message, bool useMarkdown = true);
    bool sendMessage(const String& chatId, const String& message, bool useMarkdown = true);
    bool sendMessageWithKeyboard(const String& message, const String& keyboard);
    bool sendPhoto(const String& chatId, const String& photo, const String& caption = "");
    
    // Status methods
    bool isInitialized() const { return initialized; }
    String getBotUsername();
    
    // Notification methods
    void sendStartupMessage();
    void sendErrorNotification(const String& error);
    void sendSensorAlert(const String& sensorName, const String& value, const String& threshold);
    void sendAutomationTriggered(const String& ruleName, const String& action);
    
    // System notifications
    void notifyWiFiConnected();
    void notifyWiFiDisconnected();
    void notifySystemRestart();
    void notifyLowMemory();
};

#endif // TELEGRAM_BOT_H
