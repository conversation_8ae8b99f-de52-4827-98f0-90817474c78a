ESPgram Firmware Release
========================

Version: 1.0.0
Build Date: 20250602_150507
Platform: ESP8266
Board: NodeMCU v1.0 (ESP-12E)

Files Included:
  - ESPgram_v1.0.0.bin
  - ESPgram_v1.0.0.elf

Features:
  - WiFi Setup with Captive Portal
  - Telegram Bot Control
  - GPIO Management (Digital/PWM/Analog)
  - Sensor Support (DHT11/DHT22/LDR/PIR)
  - Automation Engine
  - EEPROM Configuration Storage
  - OTA Ready

Installation:
  1. Flash the firmware to ESP8266
  2. Connect to ESPgram-Setup WiFi network
  3. Navigate to 192.168.4.1 in browser
  4. Enter WiFi credentials and Telegram bot details
  5. Save configuration and restart
  6. Send /start to your Telegram bot
