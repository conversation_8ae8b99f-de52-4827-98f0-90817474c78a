# ESPgram - ESP32 Telegram Bot Control Firmware

A comprehensive, production-ready firmware for ESP32 that provides complete Telegram Bot-based control of GPIO pins, sensors, and automation rules. Designed for easy deployment with a single `.bin` file and intuitive setup process.

## 🚀 Features

### ✅ **Phase 1 - Core Implementation (Current)**
- **🔌 Captive Portal Setup**: Automatic WiFi configuration with embedded web interface
- **💬 Telegram Bot Integration**: Full bot control with inline keyboards
- **🔧 GPIO Management**: Digital I/O, PWM, and analog pin control
- **💾 Persistent Storage**: EEPROM-based configuration storage
- **🔄 OTA Ready**: Single `.bin` compilation for easy flashing
- **📱 Mobile-Friendly**: Responsive setup interface
- **🛡️ Memory Optimized**: Efficient memory usage for ESP32

### 🔄 **Phase 2 - Sensors & Automation (Next)**
- **🌡️ Sensor Support**: DHT11/22, LDR, PIR, IR sensors
- **⚙️ Automation Engine**: Rule-based automation system
- **🚨 Smart Alerts**: Threshold-based notifications
- **📊 Data Logging**: Sensor data history

## 📋 Requirements

- **Hardware**: ESP32 development board
- **Software**: PlatformIO IDE
- **Telegram**: Bot token from @BotFather
- **Network**: WiFi connection

## 🛠️ Installation

### 1. Clone Repository
```bash
git clone <repository-url>
cd ESPgram
```

### 2. Build Firmware
```bash
# Install PlatformIO if not already installed
pip install platformio

# Build the project
pio run

# The firmware will be automatically copied to release/ folder
```

### 3. Flash to ESP32
```bash
# Flash via USB
pio run --target upload

# Or use the generated .bin file from release/ folder
# with ESP32 flash tools or mobile apps
```

## 🔧 Setup Process

### 1. First Boot
1. Power on your ESP32
2. Connect to WiFi network `ESPgram-Setup` (no password)
3. Open browser - should auto-redirect to setup page
4. If not redirected, visit `http://***********`

### 2. Configuration
1. **WiFi Settings**: Enter your WiFi network credentials
2. **Telegram Bot**: 
   - Create bot with @BotFather on Telegram
   - Copy the bot token
   - Start a chat with your bot
   - Get your Chat ID from bot API
3. **Save Configuration**: Submit the form
4. **Automatic Restart**: ESP32 will restart and connect to WiFi

### 3. Telegram Control
1. Open Telegram and find your bot
2. Send `/start` to begin
3. Use inline buttons or commands to control your ESP32

## 📱 Telegram Commands

### 🔌 GPIO Commands
- `/gpio list` - Show all configured pins
- `/gpio set <pin> <mode>` - Configure pin (modes: output, input, pwm, analog)
- `/gpio read <pin>` - Read pin value
- `/gpio write <pin> <value>` - Write to pin (0/1)
- `/gpio pwm <pin> <value>` - Set PWM value (0-255)
- `/gpio toggle <pin>` - Toggle digital output pin
- `/gpio remove <pin>` - Remove pin configuration

### 📊 System Commands
- `/status` - System information
- `/help` - Command reference
- `/system restart` - Restart ESP32
- `/system reset` - Factory reset (requires confirmation)
- `/system wifi` - WiFi information

### 💡 Examples
```
/gpio set 2 output LED
/gpio write 2 1
/gpio set 34 analog LDR
/gpio read 34
/gpio set 18 pwm Motor
/gpio pwm 18 128
```

## 🏗️ Project Structure

```
ESPgram/
├── src/
│   ├── main.cpp              # Main application entry point
│   ├── wifi_setup.h/.cpp     # WiFi & captive portal management
│   ├── telegram_bot.h/.cpp   # Telegram Bot integration
│   ├── gpio_manager.h/.cpp   # GPIO pin management
│   ├── html_page.h           # Embedded setup web page
│   ├── sensor_handler.h      # Sensor management (Phase 2)
│   └── automation.h          # Automation engine (Phase 2)
├── include/
│   └── config.h              # Configuration constants
├── version/
│   ├── version.txt           # Current version
│   └── update_version.py     # Version management script
├── scripts/
│   └── copy_firmware.py      # Post-build firmware copy
├── release/                  # Generated firmware files
├── platformio.ini            # PlatformIO configuration
└── README.md                 # This file
```

## 🔧 Development

### Building
```bash
# Clean build
pio run --target clean
pio run

# Monitor serial output
pio device monitor

# Upload and monitor
pio run --target upload --target monitor
```

### Version Management
```bash
# Increment version before build
export INCREMENT_VERSION=true
pio run
```

### Memory Optimization
- Uses PROGMEM for HTML storage
- Efficient string handling
- Minimal JSON buffer sizes
- Smart memory monitoring

## 🛡️ Security Features

- **Single User**: Bot responds only to configured Chat ID
- **Input Validation**: All commands are validated
- **Safe Defaults**: Pins default to safe states
- **Factory Reset**: Complete configuration wipe option

## 📊 Technical Specifications

- **Target**: ESP32 (240MHz dual-core)
- **Memory**: ~300KB flash, ~50KB RAM usage
- **WiFi**: 802.11 b/g/n
- **GPIO**: Up to 34 configurable pins
- **PWM**: 16 channels, 8-bit resolution
- **ADC**: 12-bit resolution
- **Storage**: Preferences (NVS)

## 🔮 Roadmap

### Phase 2 - Sensors & Automation
- [ ] DHT11/22 temperature & humidity sensors
- [ ] LDR light sensors
- [ ] PIR motion sensors
- [ ] IR sensors
- [ ] Rule-based automation engine
- [ ] Sensor threshold alerts
- [ ] Time-based triggers

### Phase 3 - Advanced Features
- [ ] Web dashboard (optional)
- [ ] Data logging to cloud
- [ ] Multiple user support
- [ ] Advanced scheduling
- [ ] Sensor calibration
- [ ] Custom sensor types

## 🐛 Troubleshooting

### Setup Issues
- **Can't connect to setup WiFi**: Restart ESP32, wait 30 seconds
- **Setup page won't load**: Try `http://***********` manually
- **Bot not responding**: Check token and Chat ID

### Runtime Issues
- **WiFi disconnects**: ESP32 will auto-reconnect or enter setup mode
- **Bot stops working**: Check internet connection and bot token
- **Memory errors**: Restart ESP32, check for memory leaks

### Debug Mode
Enable debug output by setting `CORE_DEBUG_LEVEL=3` in platformio.ini

## 📄 License

This project is open source. See LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Test thoroughly
5. Submit pull request

## 📞 Support

- **Issues**: Use GitHub Issues
- **Documentation**: Check README and code comments
- **Community**: Join our Telegram group (link in releases)

---

**ESPgram** - Making ESP32 control simple and accessible through Telegram! 🚀
