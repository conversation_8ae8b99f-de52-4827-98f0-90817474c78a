ESPgram v1.0.0 - Flashing Instructions
=========================================

Method 1: Using PlatformIO (Recommended)
----------------------------------------
1. Connect ESP8266 to computer via USB
2. Open terminal in project directory
3. Run: pio run --target upload

Method 2: Using esptool.py
-------------------------
1. Install esptool: pip install esptool
2. Connect ESP8266 to computer via USB
3. Find COM port (usually COM3, COM4, etc.)
4. Run: esptool.py --port COMX --baud 921600 write_flash 0x0 ESPgram_v1.0.0.bin

Method 3: Using ESP Flash Download Tool
--------------------------------------
1. Download ESP Flash Download Tool from Espressif
2. Load ESPgram_v1.0.0.bin at address 0x0
3. Set baud rate to 921600
4. Click START

First-Time Setup:
----------------
1. After flashing, ESP8266 will create WiFi AP: 'ESPgram-Setup'
2. Connect to this AP (no password)
3. Open browser and go to ***********
4. Enter WiFi credentials and Telegram bot info
5. Save configuration and ESP will restart
6. Send /start to your Telegram bot to begin
